# 应用配置管理系统API文档

## 📋 概述

本文档提供应用配置管理系统的完整API接口说明，包括安全配置更新、配置验证、缓存管理和回滚机制。该系统支持Skii MinIO、MQTT、FH-SDK、EJ-FH-SERVER等多种配置的动态管理。

## 🏗️ 系统架构

### 四层缓存架构
```
本地内存缓存 → Redis缓存 → 数据库 → YAML配置文件 
     5分钟        1小时      持久化    降级方案
```

### 配置获取策略
1. **本地内存缓存**：极速响应 (~1ms)
2. **Redis缓存**：快速响应 (~10ms)  
3. **数据库查询**：正常响应 (~50ms)
4. **YAML降级**：兜底方案 (~5ms)

### 安全更新流程
1. **配置验证**：测试连通性和有效性
2. **配置备份**：自动备份当前配置
3. **原子更新**：事务性更新所有配置
4. **缓存同步**：立即更新所有缓存层
5. **失败回滚**：自动回滚到上一版本

## 🔗 API接口列表

### 基础URL
```
http://your-domain/fh/application-config
```

## 📖 配置查询接口

### 1. 获取所有配置
```http
GET /all
```

**响应示例**：
```json
{
  "code": 200,
  "success": true,
  "data": {
    "skiiMinio": {
      "endpoint": "https://www.skii.ejdrone.com:20802",
      "accessKey": "sZ****N",
      "secretKey": "D0****LS",
      "bucket": "file-storage-privatization"
    },
    "mqtt": {
      "basic": {
        "protocol": "MQTT",
        "host": "ecs04.ejdrone.com",
        "port": "1883",
        "username": "MLP-Server-User",
        "password": "EJ****er",
        "clientId": "mlp-server-0",
        "path": ""
      },
      "drc": {
        "protocol": "WS",
        "host": "ecs04.ejdrone.com",
        "port": "8083",
        "path": "/mqtt",
        "username": "MLP-Server-User",
        "password": "EJ****er"
      }
    },
    "fhSdk": {
      "inboundTopic": "sys/product/+/status"
    },
    "ejFhServer": {
      "url": "https://ims.ejdrone.com/fh-cloud"
    }
  }
}
```

### 2. 获取Skii MinIO配置
```http
GET /skii-minio
```

### 3. 获取MQTT配置
```http
GET /mqtt
```

## ⚡ 安全配置更新接口（推荐）

### 1. 安全更新Skii MinIO配置
```http
PUT /skii-minio
Content-Type: application/json
```

**请求体**：
```json
{
  "endpoint": "https://new-minio.com:9000",
  "accessKey": "new-access-key",
  "secretKey": "new-secret-key",
  "bucket": "new-bucket"
}
```

**成功响应**：
```json
{
  "code": 200,
  "success": true,
  "data": {
    "success": true,
    "validationResult": {
      "valid": true,
      "validationTimeMs": 256,
      "details": "MinIO连接成功，存储桶存在"
    },
    "updateDetails": {
      "endpoint": "https://new-minio.com:9000",
      "accessKey": "ne****ey",
      "secretKey": "ne****et",
      "bucket": "new-bucket"
    },
    "totalTimeMs": 1205
  },
  "msg": "MinIO配置验证并更新成功"
}
```

**失败响应**：
```json
{
  "code": 500,
  "success": false,
  "data": {
    "success": false,
    "failureReason": "配置验证失败: 存储桶不存在: wrong-bucket",
    "validationResult": {
      "valid": false,
      "failureReason": "存储桶不存在: wrong-bucket",
      "validationTimeMs": 2156
    },
    "totalTimeMs": 2156
  },
  "msg": "配置验证失败: 存储桶不存在: wrong-bucket"
}
```

### 2. 安全更新MQTT Basic配置
```http
PUT /mqtt/basic
Content-Type: application/json
```

**请求体**：
```json
{
  "protocol": "MQTT",
  "host": "new-mqtt.com",
  "port": "1883",
  "username": "mqtt-user",
  "password": "mqtt-password",
  "clientId": "client-001",
  "path": "/mqtt"
}
```

## 🧪 配置验证接口（更新前预检）

### 1. 验证MinIO配置
```http
POST /validate/minio
Content-Type: application/json
```

**请求体**：
```json
{
  "endpoint": "https://test-minio.com:9000",
  "accessKey": "test-access-key",
  "secretKey": "test-secret-key",
  "bucket": "test-bucket"
}
```

**成功响应**：
```json
{
  "code": 200,
  "success": true,
  "data": {
    "valid": true,
    "validationTimeMs": 256,
    "details": "MinIO连接成功，存储桶存在"
  },
  "msg": "MinIO配置验证成功"
}
```

**失败响应**：
```json
{
  "code": 500,
  "success": false,
  "data": {
    "valid": false,
    "failureReason": "MinIO连接失败: Connection refused",
    "validationTimeMs": 10000,
    "details": "connection failed"
  },
  "msg": "MinIO连接失败: Connection refused"
}
```

### 2. 验证MQTT配置
```http
POST /validate/mqtt
Content-Type: application/json
```

### 3. 验证当前MinIO连通性
```http
GET /validate/minio/current
```

## 🔄 配置回滚接口

### 回滚指定配置
```http
POST /rollback
Content-Type: application/json
```

**请求体**：
```json
[
  "skii.minio.endpoint",
  "skii.minio.accessKey",
  "skii.minio.secretKey",
  "skii.minio.bucket"
]
```

**响应**：
```json
{
  "code": 200,
  "success": true,
  "data": true,
  "msg": "配置回滚成功"
}
```

## 🚀 缓存管理接口

### 1. 刷新指定配置缓存
```http
POST /cache/refresh?paramKey=skii.minio.endpoint
```

### 2. 刷新所有配置缓存
```http
POST /cache/refresh-all
```

### 3. 预热配置缓存
```http
POST /cache/warm-up
```

## 📊 传统接口（兼容性，不推荐）

### 1. 传统MinIO更新（无验证）
```http
PUT /skii-minio/legacy
```
⚠️ **已标记过时**：建议使用安全更新接口

### 2. 传统MQTT更新（无验证）
```http
PUT /mqtt/basic/legacy
```
⚠️ **已标记过时**：建议使用安全更新接口

## 🔧 配置键常量

### Skii MinIO配置键
- `skii.minio.endpoint` - MinIO服务端点
- `skii.minio.accessKey` - MinIO访问密钥
- `skii.minio.secretKey` - MinIO秘密密钥
- `skii.minio.bucket` - MinIO存储桶名称

### MQTT Basic配置键
- `mqtt.basic.protocol` - MQTT协议
- `mqtt.basic.host` - MQTT主机
- `mqtt.basic.port` - MQTT端口
- `mqtt.basic.username` - MQTT用户名
- `mqtt.basic.password` - MQTT密码
- `mqtt.basic.clientId` - MQTT客户端ID
- `mqtt.basic.path` - MQTT路径

### MQTT DRC配置键
- `mqtt.drc.protocol` - MQTT DRC协议
- `mqtt.drc.host` - MQTT DRC主机
- `mqtt.drc.port` - MQTT DRC端口
- `mqtt.drc.path` - MQTT DRC路径
- `mqtt.drc.username` - MQTT DRC用户名
- `mqtt.drc.password` - MQTT DRC密码

### FH-SDK配置键
- `fh.sdk.mqtt.inboundTopic` - FH-SDK MQTT入站主题

### EJ-FH-SERVER配置键
- `ej.fh.server.url` - EJ-FH-SERVER URL

## 🎯 最佳实践

### 生产环境推荐流程
1. **先验证**：调用 `POST /validate/minio` 或 `POST /validate/mqtt` 测试配置
2. **再更新**：调用 `PUT /skii-minio` 或 `PUT /mqtt/basic` 安全更新
3. **如需回滚**：调用 `POST /rollback` 快速恢复

### 开发环境快速操作
- 直接使用安全更新接口，验证和更新一次性完成
- 关注响应中的 `validationResult` 了解验证详情

### 紧急情况处理
- 使用 `POST /rollback` 立即回滚到上一版本
- 查看应用日志了解详细错误信息

## 🚨 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 200 | 操作成功 | - |
| 400 | 请求参数错误 | 检查请求体格式和必填字段 |
| 500 | 配置验证失败 | 检查配置有效性和网络连通性 |
| 500 | 配置更新失败 | 查看日志了解具体失败原因 |
| 500 | 系统异常 | 联系技术支持 |

## 📝 日志监控

### 配置获取日志格式
```
[CONFIG] [DEV环境] 配置获取成功: key=skii.minio.endpoint, value=https://..., source=本地内存缓存
```

### 配置验证日志格式
```
[CONFIG] [VALIDATION] MinIO配置验证成功，耗时: 256ms
[CONFIG] [VALIDATION] MQTT配置验证失败: MQTT连接失败: Connection refused
```

### 配置更新日志格式
```
[CONFIG] [SAFE_UPDATE] MinIO配置安全更新成功，耗时: 1205ms
[CONFIG] [SAFE_UPDATE] MinIO配置更新失败，开始回滚: 部分配置更新失败
```

### 配置回滚日志格式
```
[CONFIG] [ROLLBACK] 配置回滚成功: key=skii.minio.endpoint
[CONFIG] [ROLLBACK] 所有配置回滚成功
```

## 🔍 故障排查

### 常见问题及解决方案

#### 1. MinIO连接失败
- **现象**：验证失败，提示"Connection refused"
- **原因**：网络不通或MinIO服务未启动
- **解决**：检查网络连接和MinIO服务状态

#### 2. 存储桶不存在
- **现象**：验证失败，提示"存储桶不存在"
- **原因**：指定的bucket在MinIO中不存在
- **解决**：创建对应的bucket或修改配置

#### 3. MQTT连接超时
- **现象**：验证失败，提示"Connection timeout"
- **原因**：MQTT服务器不可达或端口被封
- **解决**：检查网络和防火墙设置

#### 4. 配置更新部分成功
- **现象**：部分配置更新成功，部分失败
- **原因**：数据库或缓存异常
- **解决**：使用回滚接口恢复，然后重试

## 📞 技术支持

如有问题，请联系技术支持团队，并提供以下信息：
- 操作时间
- 请求接口和参数
- 完整的错误响应
- 相关的应用日志

---

**文档版本**：v1.0  
**更新时间**：2025-01-25  
**维护团队**：系统开发团队