# 应用配置环境管理指南

## 环境架构概述

系统采用**环境隔离**的配置管理架构，确保不同环境的配置完全独立，互不影响。

### 核心原则

1. **一环境一数据库** - 每个环境使用独立的数据库实例
2. **配置键统一** - 所有环境使用相同的配置键名，不包含环境后缀
3. **环境内降级** - 三层降级策略在每个环境内独立运行

## 环境配置工作流程

### 1. 环境识别

应用通过`spring.profiles.active`参数识别当前运行环境：

```bash
# 开发环境
-Dspring.profiles.active=dev

# 测试环境  
-Dspring.profiles.active=test

# 生产环境
-Dspring.profiles.active=prod
```

### 2. 配置获取流程

每个环境内的配置获取遵循三层策略：

```
环境内配置获取流程：
┌─────────────────┐
│  Redis缓存      │ ← 第一层：性能最优
└─────────────────┘
         ↓ 失败
┌─────────────────┐
│  环境数据库      │ ← 第二层：当前环境的数据库
└─────────────────┘
         ↓ 失败  
┌─────────────────┐
│ 环境YAML配置     │ ← 第三层：application-{env}.yml
└─────────────────┘
```

### 3. 环境配置示例

#### 开发环境 (dev)

**数据库连接：** `*************************************`

**配置数据：**
```sql
INSERT INTO blade_param (param_key, param_value, param_name) VALUES
('mqtt.basic.host', 'dev-mqtt.ejdrone.com', 'MQTT BASIC主机'),
('mqtt.basic.port', '1883', 'MQTT BASIC端口'),
('skii.minio.endpoint', 'https://dev-minio.ejdrone.com:9000', 'Skii MinIO服务端点');
```

**降级配置：** `application-dev.yml`
```yaml
mqtt:
  BASIC:
    host: dev-mqtt.ejdrone.com
    port: 1883
skii-minio:
  endpoint: https://dev-minio.ejdrone.com:9000
```

#### 生产环境 (prod)

**数据库连接：** `***************************************`

**配置数据：**
```sql
INSERT INTO blade_param (param_key, param_value, param_name) VALUES
('mqtt.basic.host', 'prod-mqtt.ejdrone.com', 'MQTT BASIC主机'),
('mqtt.basic.port', '1883', 'MQTT BASIC端口'),
('skii.minio.endpoint', 'https://prod-minio.ejdrone.com:9000', 'Skii MinIO服务端点');
```

**降级配置：** `application-prod.yml`
```yaml
mqtt:
  BASIC:
    host: prod-mqtt.ejdrone.com
    port: 1883
skii-minio:
  endpoint: https://prod-minio.ejdrone.com:9000
```

## Docker部署配置

### Docker Compose 示例

```yaml
# docker-compose-dev.yml
version: '3.8'
services:
  app-dev:
    image: your-app:latest
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - SPRING_DATASOURCE_URL=*******************************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=dev_password
      - SPRING_REDIS_HOST=dev-redis
    depends_on:
      - dev-postgres
      - dev-redis

  dev-postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=app_dev
      - POSTGRES_PASSWORD=dev_password

  dev-redis:
    image: redis:7-alpine
```

```yaml
# docker-compose-prod.yml
version: '3.8'
services:
  app-prod:
    image: your-app:latest
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_DATASOURCE_URL=*********************************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=prod_password
      - SPRING_REDIS_HOST=prod-redis
    depends_on:
      - prod-postgres
      - prod-redis

  prod-postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=app_prod
      - POSTGRES_PASSWORD=prod_password

  prod-redis:
    image: redis:7-alpine
```

## 配置管理最佳实践

### 1. 环境配置同步

当需要在所有环境中添加新的配置项时：

```sql
-- 在每个环境的数据库中执行
INSERT INTO blade_param (param_key, param_value, param_name, remark) VALUES
('new.config.key', '环境特定值', '新配置项', '新配置项说明');
```

### 2. 配置值环境差异管理

| 配置项 | 开发环境 | 测试环境 | 生产环境 |
|--------|----------|----------|----------|
| `mqtt.basic.host` | `dev-mqtt.ejdrone.com` | `test-mqtt.ejdrone.com` | `prod-mqtt.ejdrone.com` |
| `skii.minio.endpoint` | `https://dev-minio:9000` | `https://test-minio:9000` | `https://prod-minio:9000` |
| `ej.fh.server.url` | `https://dev-ims.ejdrone.com` | `https://test-ims.ejdrone.com` | `https://ims.ejdrone.com` |

### 3. 配置更新策略

**开发/测试环境：**
- 支持频繁的配置更新
- 可以直接通过API修改配置
- 重启成本较低

**生产环境：**
- 配置变更需要严格的审批流程
- 建议通过数据库脚本进行配置更新
- 配置变更后需要验证系统稳定性

### 4. 监控和告警

每个环境都应设置独立的监控：

```bash
# 开发环境监控
curl -X GET "http://dev-app:8501/fh/application-config/all"

# 生产环境监控  
curl -X GET "http://prod-app:8501/fh/application-config/all"
```

## 故障排查

### 常见问题及解决方案

#### 1. 配置获取失败

**问题：** 应用启动时提示配置获取失败

**排查步骤：**
1. 检查当前环境标识：`spring.profiles.active`
2. 验证数据库连接：查看数据库连接字符串
3. 检查配置表数据：确认`blade_param`表中有对应配置
4. 验证Redis连接：检查Redis缓存状态

#### 2. 环境配置混乱

**问题：** 获取到了错误环境的配置值

**可能原因：**
- 数据库连接配置错误，连接到了其他环境的数据库
- 配置缓存污染，不同环境共享了Redis实例

**解决方案：**
1. 验证数据库连接字符串
2. 清理Redis缓存：`POST /fh/application-config/cache/refresh-all`
3. 重启应用

#### 3. 降级配置不生效

**问题：** 数据库异常时没有正确降级到YAML配置

**排查步骤：**
1. 检查YAML配置文件是否存在
2. 验证YAML配置格式是否正确
3. 确认`@ConfigurationProperties`注解配置正确

## 环境迁移指南

### 新增环境

1. **创建新的数据库实例**
2. **执行配置迁移脚本**
3. **创建对应的YAML配置文件**
4. **配置Docker/K8s部署参数**
5. **验证配置获取流程**

### 环境数据迁移

```bash
# 从开发环境迁移配置到测试环境
pg_dump -h dev-db -U postgres -t blade_param app_dev | \
sed 's/specific-dev-value/specific-test-value/g' | \
psql -h test-db -U postgres app_test
```

---

*本指南确保每个环境的配置管理完全独立，避免环境间的配置污染和冲突。*