# 应用配置管理系统迁移指南

## 概述

本指南详细说明如何将应用中的硬编码配置（包括Skii MinIO、MQTT、FH-SDK、EJ-FH-SERVER等）从`application.yml`迁移到数据库动态配置管理。

## 迁移后的系统架构

### 支持的配置类型
1. **Skii MinIO配置** - MinIO对象存储相关配置
2. **MQTT BASIC配置** - MQTT基础连接配置
3. **MQTT DRC配置** - MQTT DRC连接配置
4. **FH-SDK配置** - 飞行任务SDK相关配置
5. **EJ-FH-SERVER配置** - EJ飞行服务器配置

### 配置获取策略
系统采用三层配置获取策略，确保高可用性：
1. **第一层：Redis缓存** - 性能最优，响应时间 < 100ms
2. **第二层：数据库查询** - 数据库异常时的备选方案，响应时间 < 500ms
3. **第三层：YAML降级** - 最终降级方案，确保服务不中断

## 迁移步骤

### 第一步：执行数据库迁移脚本

```bash
# 1. 备份现有数据库
pg_dump -h your_host -U your_user -d your_database > backup_before_migration.sql

# 2. 执行迁移脚本
psql -h your_host -U your_user -d your_database -f doc/sql/skii-minio-config-migration.sql
```

### 第二步：验证配置迁移

执行以下查询验证配置是否正确迁移：

```sql
SELECT param_name, param_key, param_value 
FROM blade_param 
WHERE param_key LIKE 'skii.minio.%' 
   OR param_key LIKE 'mqtt.%' 
   OR param_key LIKE 'fh.sdk.%' 
   OR param_key LIKE 'ej.fh.server.%'
ORDER BY param_key;
```

### 第三步：重启应用

配置迁移完成后，重启应用以使新的配置管理系统生效：

```bash
# 停止应用
docker-compose down

# 启动应用
docker-compose up -d
```

### 第四步：验证系统功能

1. **检查应用启动日志**
```
[CONFIG] [INFO] 应用配置初始化开始...
[CONFIG] [INFO] 开始预热应用配置缓存...
[CONFIG] [INFO] 应用配置完整性验证通过
[CONFIG] [INFO] 应用配置初始化完成
```

2. **验证配置API**
```bash
# 获取所有配置
curl -X GET "http://localhost:8501/fh/application-config/all"

# 验证MinIO连通性
curl -X GET "http://localhost:8501/fh/application-config/validate/minio"
```

## 多环境配置支持

### 环境配置架构

系统采用**环境隔离**的配置管理架构：

- **每个环境使用独立的数据库**
- **配置键统一，不包含环境后缀**
- **三层降级策略在每个环境内独立运行**

### 环境配置策略

每个环境按以下优先级获取配置：

1. **数据库配置** - 最高优先级，存储在当前环境对应的数据库中
2. **对应环境的YAML配置** - 根据启动时的`spring.profiles.active`参数
   - `dev` → `application-dev.yml`
   - `test` → `application-test.yml`
   - `prod` → `application-prod.yml`

### 环境部署示例

```bash
# 开发环境启动
docker run -e SPRING_PROFILES_ACTIVE=dev \
  -e SPRING_DATASOURCE_URL=************************************* \
  your-app:latest

# 生产环境启动  
docker run -e SPRING_PROFILES_ACTIVE=prod \
  -e SPRING_DATASOURCE_URL=*************************************** \
  your-app:latest
```

### 环境配置数据示例

所有环境使用相同的配置键，但存储在不同的数据库中：

```sql
-- 开发环境数据库中的配置
INSERT INTO blade_param (param_key, param_value, param_name) VALUES
('mqtt.basic.host', 'dev-mqtt.ejdrone.com', 'MQTT BASIC主机');

-- 生产环境数据库中的配置
INSERT INTO blade_param (param_key, param_value, param_name) VALUES
('mqtt.basic.host', 'prod-mqtt.ejdrone.com', 'MQTT BASIC主机');
```

这样每个环境获取`mqtt.basic.host`时会得到对应环境的值。

## API接口说明

### 配置查询接口

| 接口 | 方法 | 描述 |
|------|------|------|
| `/fh/application-config/all` | GET | 获取所有应用配置 |
| `/fh/application-config/skii-minio` | GET | 获取Skii MinIO配置 |
| `/fh/application-config/mqtt` | GET | 获取MQTT配置 |

### 配置更新接口

| 接口 | 方法 | 描述 |
|------|------|------|
| `/fh/application-config/skii-minio` | PUT | 批量更新Skii MinIO配置 |
| `/fh/application-config/mqtt/basic` | PUT | 批量更新MQTT Basic配置 |
| `/fh/application-config/mqtt/drc` | PUT | 批量更新MQTT DRC配置 |
| `/fh/application-config/fh-sdk` | PUT | 更新FH-SDK配置 |
| `/fh/application-config/ej-fh-server` | PUT | 更新EJ-FH-SERVER配置 |

### 缓存管理接口

| 接口 | 方法 | 描述 |
|------|------|------|
| `/fh/application-config/cache/refresh-all` | POST | 刷新所有配置缓存 |
| `/fh/application-config/cache/warm-up` | POST | 预热配置缓存 |
| `/fh/application-config/cache/refresh` | POST | 刷新指定配置缓存 |

## 配置参数对照表

### Skii MinIO配置

| YAML配置路径 | 数据库配置键 | 描述 |
|-------------|-------------|------|
| `skii-minio.endpoint` | `skii.minio.endpoint` | MinIO服务端点 |
| `skii-minio.access-key` | `skii.minio.accessKey` | MinIO访问密钥 |
| `skii-minio.secret-key` | `skii.minio.secretKey` | MinIO秘密密钥 |
| `skii-minio.bucket` | `skii.minio.bucket` | MinIO存储桶名称 |

### MQTT BASIC配置

| YAML配置路径 | 数据库配置键 | 描述 |
|-------------|-------------|------|
| `mqtt.BASIC.protocol` | `mqtt.basic.protocol` | MQTT协议 |
| `mqtt.BASIC.host` | `mqtt.basic.host` | MQTT主机 |
| `mqtt.BASIC.port` | `mqtt.basic.port` | MQTT端口 |
| `mqtt.BASIC.username` | `mqtt.basic.username` | MQTT用户名 |
| `mqtt.BASIC.password` | `mqtt.basic.password` | MQTT密码 |
| `mqtt.BASIC.client-id` | `mqtt.basic.clientId` | MQTT客户端ID |
| `mqtt.BASIC.path` | `mqtt.basic.path` | MQTT路径 |

### MQTT DRC配置

| YAML配置路径 | 数据库配置键 | 描述 |
|-------------|-------------|------|
| `mqtt.DRC.protocol` | `mqtt.drc.protocol` | MQTT DRC协议 |
| `mqtt.DRC.host` | `mqtt.drc.host` | MQTT DRC主机 |
| `mqtt.DRC.port` | `mqtt.drc.port` | MQTT DRC端口 |
| `mqtt.DRC.path` | `mqtt.drc.path` | MQTT DRC路径 |
| `mqtt.DRC.username` | `mqtt.drc.username` | MQTT DRC用户名 |
| `mqtt.DRC.password` | `mqtt.drc.password` | MQTT DRC密码 |

### FH-SDK配置

| YAML配置路径 | 数据库配置键 | 描述 |
|-------------|-------------|------|
| `fh-sdk.mqtt.inbound-topic` | `fh.sdk.mqtt.inboundTopic` | FH-SDK MQTT入站主题 |

### EJ-FH-SERVER配置

| YAML配置路径 | 数据库配置键 | 描述 |
|-------------|-------------|------|
| `ej-fh-server.url` | `ej.fh.server.url` | EJ-FH-SERVER服务地址 |

## 监控和运维

### 关键监控指标

1. **配置获取成功率** - 应 > 99%
2. **配置获取响应时间** - 缓存命中 < 100ms，数据库查询 < 500ms
3. **缓存命中率** - 应 > 90%
4. **降级事件次数** - 应尽可能少

### 日志监控

关注以下关键日志：

```
[CONFIG] [INFO] 配置获取成功: key=skii.minio.endpoint, source=redis
[CONFIG] [WARN] Redis不可用，降级到数据库: key=mqtt.basic.host
[CONFIG] [ERROR] 数据库异常，降级到YAML: key=fh.sdk.mqtt.inboundTopic
```

### 告警规则

建议设置以下告警规则：

- 配置获取失败率 > 1%
- 配置获取响应时间 > 1000ms
- 缓存命中率 < 90%
- 连续降级事件 > 5次

## 回滚方案

### 紧急回滚步骤

如果迁移后出现问题，可以按以下步骤快速回滚：

1. **停止应用**
```bash
docker-compose down
```

2. **恢复数据库**
```bash
# 删除迁移的配置数据
DELETE FROM blade_param WHERE param_key LIKE 'skii.minio.%' 
   OR param_key LIKE 'mqtt.%' 
   OR param_key LIKE 'fh.sdk.%' 
   OR param_key LIKE 'ej.fh.server.%';
```

3. **恢复YAML配置**
将备份的YAML配置文件恢复到对应的环境配置文件中。

4. **重启应用**
```bash
docker-compose up -d
```

## 常见问题

### Q1: 配置更新后不生效怎么办？

**A1**: 执行缓存刷新操作：
```bash
curl -X POST "http://localhost:8501/fh/application-config/cache/refresh-all"
```

### Q2: Redis不可用时系统还能正常工作吗？

**A2**: 是的，系统会自动降级到数据库查询，不会影响业务功能。

### Q3: 如何验证配置迁移是否成功？

**A3**: 
1. 检查应用启动日志中的配置初始化信息
2. 调用配置查询API验证配置值
3. 执行MinIO连通性测试

### Q4: 多环境部署时如何管理不同的配置值？

**A4**: 每个环境使用独立的数据库，在对应环境的数据库中存储该环境的配置值。配置键保持统一，不需要环境后缀。同时在对应环境的YAML文件中保留环境特定的配置作为降级方案。

## 技术支持

如遇到迁移过程中的技术问题，请参考：

1. 应用启动日志：`/logs/application.log`
2. 配置服务日志：搜索关键字 `[CONFIG]`
3. API文档：访问 `/doc.html` 查看完整的API文档

---

*最后更新时间：2025-07-25*