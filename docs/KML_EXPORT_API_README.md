# 空间网格KML导出API接口

## 功能概述

本API接口用于导出所有有效的空间网格数据为KML文件，支持批量导出功能。

## API接口详情

### 接口地址
```
GET /spatialGrid/export/all
```

### 请求参数

| 参数名称 | 类型 | 是否必需 | 描述 | 默认值 | 示例 |
|---------|------|----------|------|--------|------|
| filename | String | 否 | 自定义下载的KML文件名称 | full_grid_export.kml | all_grids_backup.kml |

### 请求示例

```bash
# 使用默认文件名
GET /spatialGrid/export/all

# 自定义文件名
GET /spatialGrid/export/all?filename=my_grids_export
```

### 响应格式

**成功响应:**
- **Content-Type:** `application/vnd.google-earth.kml+xml`
- **Content-Disposition:** `attachment; filename="<filename>.kml"`
- **Body:** KML文件内容

**错误响应:**
- **HTTP 404:** 没有找到有效的网格数据
- **HTTP 500:** 服务器内部错误

### 数据处理逻辑

1. **数据查询:** 执行固定SQL查询，获取所有 `is_deleted = 0` 的网格数据
   ```sql
   SELECT id, grid_code, grid_name, geom_json, geom_type, grid_area, create_time 
   FROM public.spatial_grid 
   WHERE is_deleted = 0;
   ```

2. **KML生成:** 将每条网格记录转换为KML `<Placemark>` 元素
   - **名称:** 使用 `grid_name` 字段
   - **描述:** 包含网格编码、面积、创建时间的HTML表格
   - **几何图形:** 解析 `geom_json` 转换为对应的KML几何标签

3. **支持的几何类型:**
   - Point → `<Point>`
   - LineString → `<LineString>`
   - Polygon → `<Polygon>`

### KML输出示例

```xml
<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2">
  <Document>
    <name>空间网格数据导出</name>
    <description>导出所有有效的空间网格数据</description>
    <Placemark>
      <name>第一象限A区网格</name>
      <description><![CDATA[
        <table border="1" cellpadding="5">
          <tr><td><b>网格编码</b></td><td>SEC-01-A</td></tr>
          <tr><td><b>网格面积</b></td><td>12500.75 平方米</td></tr>
          <tr><td><b>创建时间</b></td><td>2025-06-10T14:30:00</td></tr>
        </table>
      ]]></description>
      <Polygon>
        <outerBoundaryIs>
          <LinearRing>
            <coordinates>
              121.50,25.05,0 121.51,25.05,0 121.51,25.04,0 121.50,25.04,0 121.50,25.05,0
            </coordinates>
          </LinearRing>
        </outerBoundaryIs>
      </Polygon>
    </Placemark>
  </Document>
</kml>
```

## 实现细节

### 核心类和方法

1. **控制器:** `SpatialGridController.exportAllGridsToKml()`
2. **服务接口:** `ISpatialGridService.exportAllGridsToKml()`
3. **服务实现:** `SpatialGridServiceImpl.exportAllGridsToKml()`

### 关键特性

- **批量处理:** 一次性导出所有有效网格数据
- **错误处理:** 单个网格数据错误不影响整体导出
- **XML安全:** 对所有文本内容进行XML转义处理
- **日志记录:** 详细的操作日志便于调试和监控
- **几何解析:** 支持多种GeoJSON几何类型转换

### 安全考虑

- 只导出 `is_deleted = 0` 的有效数据
- 对用户输入的文件名进行验证和处理
- XML内容转义防止注入攻击
- 异常处理避免敏感信息泄露

## 使用说明

1. 确保数据库中存在有效的空间网格数据
2. 调用API接口获取KML文件
3. 可在Google Earth等GIS软件中打开查看
4. 支持自定义文件名便于管理

## 注意事项

- 大量数据导出可能需要较长时间，建议在低峰期使用
- KML文件大小取决于网格数量和几何复杂度
- 建议定期清理无效数据以提高导出效率