# 缩略图功能使用说明

## 功能概述

本功能为海滩垃圾事件管理系统提供了根据bbox坐标自动生成缩略图的能力。当查询事件详情时，系统会根据事件中的bbox信息，在原始发现图片中框选出垃圾位置并生成缩略图，存储到MinIO中并返回访问URL。

## 主要特性

- ✅ 根据bbox坐标自动裁剪图片
- ✅ 生成指定尺寸的缩略图
- ✅ 自动上传到MinIO存储
- ✅ 支持配置化参数调整
- ✅ 定时清理过期缩略图
- ✅ 异常处理和日志记录

## 核心组件

### 1. 配置类
- `ThumbnailConfig`: 缩略图相关配置参数管理

### 2. 工具类
- `ImageProcessingUtil`: 图片处理工具，负责根据bbox裁剪和生成缩略图

### 3. 服务类
- `IThumbnailImageService`: 缩略图服务接口
- `ThumbnailImageServiceImpl`: 缩略图服务实现，负责上传和URL管理

### 4. 定时任务
- `ThumbnailCleanupTask`: 定时清理过期缩略图文件

## 配置说明

在 `application.yml` 中添加以下配置：

```yaml
beachwaste:
  thumbnail:
    # 是否启用缩略图功能
    enabled: true
    # 缩略图尺寸
    width: 300
    height: 300
    # 图片质量（0.0-1.0）
    quality: 0.8
    # bbox区域放大系数
    scale-factor: 1.2
    # 缩略图过期天数
    expiry-days: 7
    # 是否启用定时清理
    cleanup-enabled: true
    # 清理任务执行时间（cron表达式）
    cleanup-cron: "0 0 2 * * ?"
    # 缩略图存储路径前缀
    path-prefix: "thumbnails/"
    # 支持的图片格式
    supported-formats:
      - jpg
      - jpeg
      - png
      - bmp
      - gif
    # 最大处理图片大小（字节）
    max-image-size: 10485760
    # 处理超时时间（毫秒）
    process-timeout: 30000
```

## 使用方式

### 1. 自动处理

当调用 `EventServiceImpl.getEventsByStaffId()` 方法时，系统会自动为每个事件处理缩略图：

```java
// 在查询事件列表时自动处理
IPage<EventDetailVO> events = eventService.getEventsByStaffId(staffId, query);
// 返回的EventDetailVO中的discoveryImagePath字段将包含缩略图URL
```

### 2. bbox格式

bbox字段应包含坐标信息，格式示例：
```json
"x:100,y:200,width:300,height:400"
```

### 3. 存储路径

缩略图将存储在MinIO的以下路径结构中：
```
thumbnails/
├── 2024/
│   ├── 01/
│   │   ├── 15/
│   │   │   ├── eventId_uuid.jpg
│   │   │   └── ...
│   │   └── ...
│   └── ...
└── ...
```

## 依赖要求

### Maven依赖

```xml
<dependency>
    <groupId>net.coobird</groupId>
    <artifactId>thumbnailator</artifactId>
    <version>0.4.20</version>
</dependency>
```

### 系统要求

- Java 8+
- Spring Boot 2.x+
- MinIO客户端
- 支持的图片格式：JPG, JPEG, PNG, BMP, GIF

## 性能考虑

1. **图片大小限制**: 默认最大处理10MB的图片文件
2. **处理超时**: 默认30秒处理超时
3. **异步处理**: 建议在高并发场景下考虑异步处理
4. **缓存策略**: 生成的缩略图会缓存在MinIO中，避免重复处理

## 监控和日志

系统会记录以下关键日志：

- 缩略图生成成功/失败
- 文件上传状态
- 定时清理执行情况
- 异常处理信息

## 故障排除

### 常见问题

1. **缩略图生成失败**
   - 检查bbox格式是否正确
   - 确认原始图片文件是否存在且可访问
   - 检查图片格式是否支持

2. **MinIO上传失败**
   - 检查MinIO连接配置
   - 确认存储桶权限
   - 检查网络连接

3. **定时清理不执行**
   - 检查 `cleanup-enabled` 配置
   - 确认cron表达式格式
   - 检查Spring定时任务是否启用

### 调试建议

1. 启用DEBUG日志级别查看详细处理过程
2. 检查MinIO控制台确认文件上传状态
3. 使用小尺寸测试图片进行功能验证

## 扩展功能

未来可考虑的扩展：

- 支持多种缩略图尺寸
- 添加图片水印功能
- 支持图片格式转换
- 实现智能裁剪算法
- 添加图片压缩优化

## 版本历史

- v1.0.0: 初始版本，支持基础缩略图生成和存储功能

#policy
```
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:ListAllMyBuckets"
            ],
            "Resource": [
                "arn:aws:s3:::*"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "s3:CreateBucket",
                "s3:GetBucketLocation",
                "s3:ListBucket"
            ],
            "Resource": [
                "arn:aws:s3:::processed-images"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "s3:DeleteObject",
                "s3:GetObject",
                "s3:PutObject"
            ],
            "Resource": [
                "arn:aws:s3:::processed-images/*"
            ]
        }
    ]
}
```