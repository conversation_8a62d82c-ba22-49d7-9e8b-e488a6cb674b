# 配置管理系统迁移项目需求文档 (PRD)

## 1. 项目概述

### 1.1 项目背景
当前系统的Skii MinIO配置信息硬编码在`application.yml`文件中，存在以下问题：
- 配置变更需要重新部署应用
- 无法实现配置的动态管理
- 缺乏配置变更的审计追踪
- 多环境配置管理复杂

### 1.2 项目目标
将Skii MinIO相关配置从`application.yml`迁移到数据库存储，实现：
- 配置的集中化管理
- 支持配置的动态更新
- 提供配置缓存机制提升性能
- 实现配置获取的降级策略

### 1.3 项目范围
- 迁移`skii-minio`配置段到`blade_param`表
- 实现基于数据库的配置管理服务
- 集成Redis缓存提升配置获取性能
- 实现配置获取的多层降级机制

## 2. 需求分析

### 2.1 功能需求

#### 2.1.1 配置存储需求
- **FR-001**: 将以下配置项迁移到`blade_param`表：
  - `skii.minio.endpoint`: MinIO服务端点
  - `skii.minio.accessKey`: MinIO访问密钥
  - `skii.minio.secretKey`: MinIO秘密密钥
  - `skii.minio.bucket`: MinIO存储桶名称

#### 2.1.2 配置获取需求
- **FR-002**: 应用启动后，所有MinIO配置获取均通过数据库
- **FR-003**: 支持配置的实时获取和缓存刷新
- **FR-004**: 提供配置管理的统一接口

#### 2.1.3 缓存需求
- **FR-005**: 使用Redis实现配置缓存，提升获取性能
- **FR-006**: 应用启动完成后预热配置缓存
- **FR-007**: 支持配置变更时的缓存同步更新

#### 2.1.4 降级需求
- **FR-008**: 数据库异常时，自动降级到`application.yml`配置
- **FR-009**: Redis异常时，直接从数据库获取配置
- **FR-010**: 记录降级事件，便于监控和排查

### 2.2 非功能需求

#### 2.2.1 性能需求
- **NFR-001**: 配置获取响应时间 < 100ms (缓存命中)
- **NFR-002**: 配置获取响应时间 < 500ms (数据库查询)
- **NFR-003**: 支持并发配置获取请求

#### 2.2.2 可靠性需求
- **NFR-004**: 配置服务可用性 ≥ 99.9%
- **NFR-005**: 降级机制确保服务不中断
- **NFR-006**: 配置数据一致性保证

#### 2.2.3 可维护性需求
- **NFR-007**: 提供配置管理的监控指标
- **NFR-008**: 支持配置的热更新
- **NFR-009**: 详细的日志记录和异常追踪

## 3. 技术方案

### 3.1 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │───▶│ Configuration   │───▶│     Redis       │
│    Services     │    │    Service      │    │     Cache       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        │
                       ┌─────────────────┐               │
                       │   blade_param   │               │
                       │     Table       │               │
                       └─────────────────┘               │
                                │                        │
                                ▼                        │
                       ┌─────────────────┐               │
                       │ application.yml │◀──────────────┘
                       │  (Fallback)     │
                       └─────────────────┘
```

### 3.2 核心组件设计

#### 3.2.1 ConfigurationService
```java
@Service
public class ConfigurationService {
    // 配置获取的主要业务逻辑
    // 实现三层获取策略：Redis -> Database -> YAML
}
```

#### 3.2.2 ConfigurationRepository
```java
@Repository
public interface ConfigurationRepository extends JpaRepository<BladeParam, Long> {
    // 数据库配置访问接口
}
```

#### 3.2.3 ConfigurationCacheManager
```java
@Component
public class ConfigurationCacheManager {
    // Redis缓存管理
    // 缓存预热、更新、失效处理
}
```

#### 3.2.4 SkiiMinioProperties
```java
@ConfigurationProperties(prefix = "skii-minio")
public class SkiiMinioProperties {
    // YAML配置属性映射（降级使用）
}
```

### 3.3 配置获取流程

```
开始
  │
  ▼
从Redis获取配置
  │
  ├─成功─▶ 返回配置
  │
  ├─失败─▶ 从数据库获取配置
  │           │
  │           ├─成功─▶ 更新Redis缓存 ─▶ 返回配置
  │           │
  │           ├─失败─▶ 从YAML获取配置
  │                      │
  │                      ├─成功─▶ 记录降级日志 ─▶ 返回配置
  │                      │
  │                      ├─失败─▶ 抛出异常
  │
  ▼
结束
```

### 3.4 数据库设计

#### 3.4.1 blade_param表结构
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| param_name | VARCHAR(255) | 参数名称 |
| param_key | VARCHAR(255) | 参数键（唯一） |
| param_value | TEXT | 参数值 |
| remark | VARCHAR(500) | 备注说明 |
| create_user | BIGINT | 创建用户 |
| create_time | TIMESTAMP | 创建时间 |
| update_user | BIGINT | 更新用户 |
| update_time | TIMESTAMP | 更新时间 |
| status | INT | 状态（1:启用 0:禁用） |
| is_deleted | INT | 删除标记 |

#### 3.4.2 配置数据
| param_key | param_value | param_name |
|-----------|-------------|------------|
| skii.minio.endpoint | https://www.skii.ejdrone.com:20802 | Skii MinIO服务端点 |
| skii.minio.accessKey | sZ6iY65sQcXfj6aDppsN | Skii MinIO访问密钥 |
| skii.minio.secretKey | D0of1tjsvdk1i4hADKBVWQmKX9KxAGDwFJ996ZLS | Skii MinIO秘密密钥 |
| skii.minio.bucket | file-storage-privatization | Skii MinIO存储桶 |

### 3.5 Redis缓存设计

#### 3.5.1 缓存键命名规范
- 格式：`config:{param_key}`
- 示例：`config:skii.minio.endpoint`

#### 3.5.2 缓存策略
- **过期时间**: 3600秒（1小时）
- **更新策略**: Write-Through（写入时同步更新缓存）
- **失效策略**: 配置变更时主动失效

## 4. 实施计划

### 4.1 第一阶段：数据迁移（1天）
- [ ] 执行SQL脚本，将配置数据插入`blade_param`表
- [ ] 验证数据完整性和正确性
- [ ] 备份原始配置数据

### 4.2 第二阶段：核心组件开发（3天）
- [ ] 开发`ConfigurationService`核心业务逻辑
- [ ] 实现`ConfigurationRepository`数据访问层
- [ ] 开发`ConfigurationCacheManager`缓存管理
- [ ] 实现配置获取的三层降级策略

### 4.3 第三阶段：集成测试（2天）
- [ ] 单元测试覆盖率 ≥ 80%
- [ ] 集成测试验证配置获取流程
- [ ] 异常场景测试（数据库断开、Redis不可用）
- [ ] 性能测试验证响应时间要求

### 4.4 第四阶段：部署上线（1天）
- [ ] 生产环境数据迁移
- [ ] 应用部署和配置验证
- [ ] 监控指标配置
- [ ] 回滚方案准备

## 5. 风险评估与应对

### 5.1 技术风险

| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| 数据库连接异常 | 中 | 配置获取失败 | 实现YAML降级策略 |
| Redis服务不可用 | 低 | 性能下降 | 直接查询数据库 |
| 配置数据不一致 | 高 | 业务功能异常 | 实现配置验证机制 |
| 缓存穿透 | 中 | 数据库压力增大 | 实现空值缓存 |

### 5.2 业务风险

| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| 配置迁移错误 | 高 | MinIO服务不可用 | 详细测试和回滚方案 |
| 性能下降 | 中 | 用户体验影响 | 性能监控和优化 |
| 服务启动失败 | 高 | 系统不可用 | 启动时配置验证 |

## 6. 监控与运维

### 6.1 监控指标
- 配置获取成功率
- 配置获取响应时间
- 缓存命中率
- 降级事件次数
- 数据库连接状态
- Redis连接状态

### 6.2 告警规则
- 配置获取失败率 > 1%
- 配置获取响应时间 > 1000ms
- 缓存命中率 < 90%
- 连续降级事件 > 5次

### 6.3 日志规范
```
[CONFIG] [INFO] 配置获取成功: key=skii.minio.endpoint, source=redis, time=50ms
[CONFIG] [WARN] Redis不可用，降级到数据库: key=skii.minio.endpoint
[CONFIG] [ERROR] 数据库异常，降级到YAML: key=skii.minio.endpoint, error=Connection timeout
```

## 7. 验收标准

### 7.1 功能验收
- [ ] 所有MinIO配置成功从数据库获取
- [ ] Redis缓存正常工作，命中率 > 90%
- [ ] 数据库异常时成功降级到YAML配置
- [ ] Redis异常时成功降级到数据库
- [ ] 应用启动时配置预热成功

### 7.2 性能验收
- [ ] 缓存命中时响应时间 < 100ms
- [ ] 数据库查询时响应时间 < 500ms
- [ ] 并发100个请求时系统稳定

### 7.3 可靠性验收
- [ ] 连续运行24小时无异常
- [ ] 模拟数据库断开，系统正常降级
- [ ] 模拟Redis断开，系统正常工作
- [ ] 配置变更后缓存正确更新

## 8. 附录

### 8.1 相关文件
- 数据迁移脚本：`/doc/sql/upgrade/v20250724_v1.1_update.sql`
- 配置文件：`/src/main/resources/application.yml`
- 项目文档：`/docs/`

### 8.2 技术栈
- Spring Boot 3.x
- Spring Data JPA
- Redis
- PostgreSQL/MySQL
- Maven

### 8.3 开发规范
- 遵循阿里Java开发规范
- 代码覆盖率 ≥ 80%
- 使用SLF4J进行日志记录
- 异常处理使用统一的异常体系

### 8.4 追加要求
application-dev.yml、application-prod.yml、application-test.yml这三个文件，在不同的启动配置下读取，我希望其中：
```yaml
mqtt:
  BASIC:
    protocol: MQTT
    host: ecs04.ejdrone.com
    port: 1883
    username: MLP-Server-User
    password: EJdr0neServer
    client-id: mlp-server-0
    path:
  DRC:
    protocol: WS
    host: ecs04.ejdrone.com
    port: 8083
    path: /mqtt
    username: MLP-Server-User
    password: EJdr0neServer

fh-sdk:
  mqtt:
    # 最初连接到mqtt时需要订阅的主题，多个主题按 ",".
    inbound-topic: sys/product/+/status

ej-fh-server:
  url: https://ims.ejdrone.com/fh-cloud
```
如上部分的配置能够也通过Skii MinIO配置相同的方式进行配置，使用相同的方式在启动时与Skii MinIO一同读取启动，集成到一起

重复要求你需要遵守的开发要求：
- 1.在docker启动时，配置了不同的启动环境，一样先从数据库中读取对应配置，降级时在按照启动配置对应的yml使用对应yml配置降级
- 2.在skii-minio的基础上修改，集成到一起，相关的类名称也要一同修改
- 3.数据库sql也需要在原本的基础上完善新增配置的部分
---