# 配置管理系统运维手册

## 📋 系统概述

本手册为运维人员提供配置管理系统的维护指南，包括监控、故障处理、性能优化和日常维护等内容。

## 🏗️ 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   移动端应用    │    │   第三方系统    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │              Load Balancer                      │
         └─────────────────────┬───────────────────────────┘
                               │
    ┌──────────────────────────┼──────────────────────────┐
    │                          │                          │
┌───▼────┐              ┌─────▼────┐              ┌─────▼────┐
│App     │              │App       │              │App       │
│Instance│              │Instance  │              │Instance  │
│   1    │              │    2     │              │    3     │
└───┬────┘              └─────┬────┘              └─────┬────┘
    │                         │                         │
    └─────────────────────────┼─────────────────────────┘
                              │
         ┌────────────────────────────────────────────────┐
         │            配置管理系统                        │
         │  ┌─────────┐  ┌─────────┐  ┌─────────┐        │
         │  │本地缓存 │  │Redis缓存│  │PostgreSQL│        │
         │  │ 5分钟   │  │ 1小时   │  │  持久化  │        │
         │  └─────────┘  └─────────┘  └─────────┘        │
         └────────────────────────────────────────────────┘
                              │
         ┌────────────────────────────────────────────────┐
         │              外部服务                          │
         │  ┌─────────┐  ┌─────────┐  ┌─────────┐        │
         │  │ MinIO   │  │ MQTT    │  │其他服务 │        │
         │  │ 存储    │  │ 消息    │  │        │        │
         │  └─────────┘  └─────────┘  └─────────┘        │
         └────────────────────────────────────────────────┘
```

## 📊 监控指标

### 1. 核心性能指标

#### 配置读取性能
- **本地缓存命中率**：> 95%
- **Redis缓存命中率**：> 90%
- **平均响应时间**：< 10ms
- **P99响应时间**：< 50ms

#### 配置更新性能
- **验证成功率**：> 99%
- **更新成功率**：> 99.5%
- **平均验证时间**：< 2秒
- **平均更新时间**：< 5秒

### 2. 业务指标监控

#### 每日统计
```bash
# 配置读取次数
grep "\[CONFIG\].*配置获取成功" app.log | wc -l

# 配置更新次数
grep "\[SAFE_UPDATE\].*更新成功" app.log | wc -l

# 验证失败次数
grep "\[VALIDATION\].*验证失败" app.log | wc -l

# 回滚操作次数
grep "\[ROLLBACK\]" app.log | wc -l
```

#### 缓存命中率统计
```bash
# 本地缓存命中
grep "source=本地内存缓存" app.log | wc -l

# Redis缓存命中
grep "source=Redis缓存" app.log | wc -l

# 数据库查询
grep "source=数据库" app.log | wc -l

# YAML降级
grep "source=application.*yml" app.log | wc -l
```

### 3. Prometheus监控指标

```yaml
# 配置读取指标
config_read_total{source="local_cache"}
config_read_total{source="redis_cache"}
config_read_total{source="database"}
config_read_total{source="yaml_fallback"}

# 配置更新指标
config_update_total{type="safe_update", result="success"}
config_update_total{type="safe_update", result="failure"}
config_validation_total{service="minio", result="success"}
config_validation_total{service="mqtt", result="success"}

# 响应时间指标
config_read_duration_seconds{quantile="0.5"}
config_read_duration_seconds{quantile="0.95"}
config_read_duration_seconds{quantile="0.99"}
```

## 🚨 告警规则

### 1. 关键告警（P0）

#### 配置服务不可用
```yaml
alert: ConfigServiceDown
expr: up{job="config-service"} == 0
for: 1m
labels:
  severity: critical
annotations:
  summary: "配置服务实例下线"
  description: "{{ $labels.instance }} 配置服务实例已下线超过1分钟"
```

#### 数据库连接失败
```yaml
alert: ConfigDatabaseDown
expr: increase(config_database_errors_total[5m]) > 10
for: 2m
labels:
  severity: critical
annotations:
  summary: "配置数据库连接异常"
  description: "配置数据库连接失败次数在5分钟内超过10次"
```

### 2. 重要告警（P1）

#### Redis缓存异常
```yaml
alert: ConfigRedisCacheDown
expr: increase(config_redis_errors_total[5m]) > 5
for: 3m
labels:
  severity: warning
annotations:
  summary: "配置Redis缓存异常"
  description: "Redis缓存错误次数在5分钟内超过5次"
```

#### 配置验证失败率过高
```yaml
alert: ConfigValidationFailureHigh
expr: rate(config_validation_total{result="failure"}[10m]) > 0.1
for: 3m
labels:
  severity: warning
annotations:
  summary: "配置验证失败率过高"
  description: "配置验证失败率超过10%"
```

### 3. 一般告警（P2）

#### 缓存命中率下降
```yaml
alert: ConfigCacheHitRateLow
expr: rate(config_read_total{source="local_cache"}[10m]) / rate(config_read_total[10m]) < 0.8
for: 5m
labels:
  severity: info
annotations:
  summary: "配置本地缓存命中率过低"
  description: "本地缓存命中率低于80%"
```

## 🔧 日常维护

### 1. 日志管理

#### 日志文件位置
```bash
# 应用日志
/var/log/app/application.log

# 配置专项日志
/var/log/app/config.log

# 错误日志
/var/log/app/error.log
```

#### 日志轮转配置
```bash
# /etc/logrotate.d/config-service
/var/log/app/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    copytruncate
    dateext
}
```

#### 日志分析脚本
```bash
#!/bin/bash
# config-log-analyzer.sh

LOG_FILE="/var/log/app/application.log"
DATE=$(date +%Y-%m-%d)

echo "=== 配置管理系统日志分析 ($DATE) ==="

echo "1. 配置读取统计:"
echo "  本地缓存命中: $(grep "$DATE.*source=本地内存缓存" $LOG_FILE | wc -l)"
echo "  Redis缓存命中: $(grep "$DATE.*source=Redis缓存" $LOG_FILE | wc -l)"
echo "  数据库查询: $(grep "$DATE.*source=数据库" $LOG_FILE | wc -l)"
echo "  YAML降级: $(grep "$DATE.*source=application.*yml" $LOG_FILE | wc -l)"

echo -e "\n2. 配置更新统计:"
echo "  安全更新成功: $(grep "$DATE.*SAFE_UPDATE.*更新成功" $LOG_FILE | wc -l)"
echo "  安全更新失败: $(grep "$DATE.*SAFE_UPDATE.*更新失败" $LOG_FILE | wc -l)"
echo "  配置验证失败: $(grep "$DATE.*VALIDATION.*验证失败" $LOG_FILE | wc -l)"

echo -e "\n3. 异常情况:"
echo "  配置回滚操作: $(grep "$DATE.*ROLLBACK" $LOG_FILE | wc -l)"
echo "  Redis连接异常: $(grep "$DATE.*Redis不可用" $LOG_FILE | wc -l)"
echo "  数据库异常: $(grep "$DATE.*数据库异常" $LOG_FILE | wc -l)"
```

### 2. 性能优化

#### 缓存预热脚本
```bash
#!/bin/bash
# cache-warmup.sh

echo "开始配置缓存预热..."

curl -X POST "http://localhost:8080/fh/application-config/cache/warm-up" \
  -H "Content-Type: application/json" \
  -w "HTTP状态码: %{http_code}, 响应时间: %{time_total}s\n"

echo "缓存预热完成"
```

#### 缓存清理脚本
```bash
#!/bin/bash
# cache-cleanup.sh

echo "开始清理配置缓存..."

curl -X POST "http://localhost:8080/fh/application-config/cache/refresh-all" \
  -H "Content-Type: application/json" \
  -w "HTTP状态码: %{http_code}, 响应时间: %{time_total}s\n"

echo "缓存清理完成"
```

### 3. 健康检查

#### 健康检查脚本
```bash
#!/bin/bash
# config-health-check.sh

BASE_URL="http://localhost:8080/fh/application-config"

echo "=== 配置管理系统健康检查 ==="

# 1. 检查服务是否可用
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/all")
if [ "$HTTP_CODE" -eq 200 ]; then
    echo "✅ 配置服务正常"
else
    echo "❌ 配置服务异常 (HTTP: $HTTP_CODE)"
    exit 1
fi

# 2. 检查MinIO连通性
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/validate/minio/current")
if [ "$HTTP_CODE" -eq 200 ]; then
    echo "✅ MinIO连通性正常"
else
    echo "⚠️  MinIO连通性异常 (HTTP: $HTTP_CODE)"
fi

# 3. 检查缓存状态
curl -s "$BASE_URL/all" | jq '.success' >/dev/null
if [ $? -eq 0 ]; then
    echo "✅ 缓存状态正常"
else
    echo "❌ 缓存状态异常"
fi

echo "健康检查完成"
```

## 🚑 故障处理

### 1. 常见故障及处理

#### 配置服务无响应
**现象**：API接口无响应或超时
**原因**：应用进程异常或资源不足
**处理步骤**：
1. 检查进程状态：`ps aux | grep java`
2. 检查资源使用：`top`, `free -h`, `df -h`
3. 检查应用日志：`tail -f /var/log/app/application.log`
4. 重启服务：`systemctl restart config-service`

#### Redis缓存异常
**现象**：大量"Redis不可用"日志
**原因**：Redis服务异常或网络问题
**处理步骤**：
1. 检查Redis服务：`systemctl status redis`
2. 测试连接：`redis-cli ping`
3. 检查网络：`telnet redis-host 6379`
4. 重启Redis：`systemctl restart redis`

#### 数据库连接异常
**现象**：配置无法更新，数据库异常日志
**原因**：PostgreSQL服务异常或连接池耗尽
**处理步骤**：
1. 检查PostgreSQL：`systemctl status postgresql`
2. 检查连接数：`select count(*) from pg_stat_activity;`
3. 检查慢查询：`select * from pg_stat_statements order by total_time desc limit 10;`
4. 重启数据库：`systemctl restart postgresql`

#### 配置验证频繁失败
**现象**：MinIO或MQTT验证失败率过高
**原因**：外部服务异常或网络问题
**处理步骤**：
1. 手动验证MinIO：`mc ls minio-host/bucket`
2. 手动验证MQTT：`mosquitto_pub -h mqtt-host -p 1883 -t test -m "test"`
3. 检查网络连通性：`ping`, `telnet`
4. 联系外部服务提供方

### 2. 应急处理预案

#### 配置服务完全不可用
```bash
# 应急处理脚本
#!/bin/bash
# emergency-recovery.sh

echo "=== 配置服务应急恢复 ==="

# 1. 停止服务
systemctl stop config-service

# 2. 备份当前日志
cp /var/log/app/application.log /var/log/app/application.log.$(date +%Y%m%d_%H%M%S)

# 3. 清理临时文件
rm -rf /tmp/config-*

# 4. 启动服务
systemctl start config-service

# 5. 等待服务启动
sleep 30

# 6. 健康检查
./config-health-check.sh

echo "应急恢复完成"
```

#### 数据库异常紧急切换
```bash
# 切换到只读模式
curl -X POST "http://localhost:8080/actuator/env" \
  -H "Content-Type: application/json" \
  -d '{"name":"config.readonly.mode","value":"true"}'

# 重启应用使配置生效
systemctl restart config-service
```

## 📈 性能调优

### 1. JVM参数优化

```bash
# /etc/systemd/system/config-service.service
[Service]
Environment="JAVA_OPTS=-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/app/heapdump.hprof"
```

### 2. 数据库连接池优化

```yaml
# application.yml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
```

### 3. Redis连接池优化

```yaml
# application.yml
spring:
  redis:
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 5000ms
```

## 🔄 备份恢复

### 1. 配置数据备份

```bash
#!/bin/bash
# config-backup.sh

BACKUP_DIR="/data/backup/config"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份配置数据
pg_dump -h localhost -U postgres -d bladex -t blade_param > $BACKUP_DIR/config_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/config_$DATE.sql

# 保留最近30天的备份
find $BACKUP_DIR -name "config_*.sql.gz" -mtime +30 -delete

echo "配置数据备份完成: $BACKUP_DIR/config_$DATE.sql.gz"
```

### 2. 配置数据恢复

```bash
#!/bin/bash
# config-restore.sh

if [ $# -ne 1 ]; then
    echo "使用方法: $0 <备份文件>"
    exit 1
fi

BACKUP_FILE=$1

# 解压备份文件
gunzip -c $BACKUP_FILE > /tmp/config_restore.sql

# 恢复数据
psql -h localhost -U postgres -d bladex -f /tmp/config_restore.sql

# 清理缓存
curl -X POST "http://localhost:8080/fh/application-config/cache/refresh-all"

# 清理临时文件
rm /tmp/config_restore.sql

echo "配置数据恢复完成"
```

## 📞 联系方式

### 技术支持
- **开发团队**：<EMAIL>
- **运维团队**：<EMAIL>
- **紧急联系**：+86-xxx-xxxx-xxxx

### 升级维护窗口
- **日常维护**：每周二 02:00-04:00
- **版本升级**：每月第一个周六 02:00-06:00
- **紧急维护**：随时（需提前通知）

---

**文档版本**：v1.0  
**更新时间**：2025-01-25  
**维护团队**：运维团队