-- 配置迁移补充脚本
-- 适用于已执行v20250724_v1.1_update.sql后的补充迁移
-- 安全处理重复配置，确保所有配置项完整迁移

-- 创建序列（如果不存在）
CREATE SEQUENCE IF NOT EXISTS blade_param_seq START WITH 1 INCREMENT BY 1;

-- 添加唯一约束（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'blade_param_param_key_unique'
    ) THEN
        ALTER TABLE blade_param ADD CONSTRAINT blade_param_param_key_unique UNIQUE (param_key);
    END IF;
END $$;

-- =========================== 完整配置迁移（安全模式） ===========================
-- 使用ON CONFLICT DO NOTHING避免重复插入

-- <PERSON><PERSON>IO配置（可能已存在，安全处理）
INSERT INTO blade_param (
    id,
    param_name,
    param_key,
    param_value,
    remark,
    create_user,
    create_dept,
    create_time,
    update_user,
    update_time,
    status,
    is_deleted
) VALUES
      (
          nextval('blade_param_seq'),
          'Skii MinIO服务端点',
          'skii.minio.endpoint',
          'https://www.skii.ejdrone.com:20802',
          'Skii MinIO服务器地址配置',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'Skii MinIO访问密钥',
          'skii.minio.accessKey',
          'sZ6iY65sQcXfj6aDppsN',
          'Skii MinIO访问密钥配置',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'Skii MinIO秘密密钥',
          'skii.minio.secretKey',
          'D0of1tjsvdk1i4hADKBVWQmKX9KxAGDwFJ996ZLS',
          'Skii MinIO秘密密钥配置',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'Skii MinIO存储桶',
          'skii.minio.bucket',
          'file-storage-privatization',
          'Skii MinIO默认存储桶名称配置',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      )
ON CONFLICT (param_key) DO NOTHING;

-- MQTT BASIC配置（新增）
INSERT INTO blade_param (
    id,
    param_name,
    param_key,
    param_value,
    remark,
    create_user,
    create_dept,
    create_time,
    update_user,
    update_time,
    status,
    is_deleted
) VALUES
      (
          nextval('blade_param_seq'),
          'MQTT BASIC协议',
          'mqtt.basic.protocol',
          'MQTT',
          'MQTT BASIC连接协议配置',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT BASIC主机',
          'mqtt.basic.host',
          'ecs04.ejdrone.com',
          'MQTT BASIC服务器主机地址',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT BASIC端口',
          'mqtt.basic.port',
          '1883',
          'MQTT BASIC服务器端口',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT BASIC用户名',
          'mqtt.basic.username',
          'MLP-Server-User',
          'MQTT BASIC连接用户名',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT BASIC密码',
          'mqtt.basic.password',
          'EJdr0neServer',
          'MQTT BASIC连接密码',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT BASIC客户端ID',
          'mqtt.basic.clientId',
          'mlp-server-0',
          'MQTT BASIC客户端ID',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT BASIC路径',
          'mqtt.basic.path',
          '',
          'MQTT BASIC连接路径',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      )
ON CONFLICT (param_key) DO NOTHING;

-- MQTT DRC配置（新增）
INSERT INTO blade_param (
    id,
    param_name,
    param_key,
    param_value,
    remark,
    create_user,
    create_dept,
    create_time,
    update_user,
    update_time,
    status,
    is_deleted
) VALUES
      (
          nextval('blade_param_seq'),
          'MQTT DRC协议',
          'mqtt.drc.protocol',
          'WS',
          'MQTT DRC连接协议配置',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT DRC主机',
          'mqtt.drc.host',
          'ecs04.ejdrone.com',
          'MQTT DRC服务器主机地址',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT DRC端口',
          'mqtt.drc.port',
          '8083',
          'MQTT DRC服务器端口',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT DRC路径',
          'mqtt.drc.path',
          '/mqtt',
          'MQTT DRC连接路径',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT DRC用户名',
          'mqtt.drc.username',
          'MLP-Server-User',
          'MQTT DRC连接用户名',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT DRC密码',
          'mqtt.drc.password',
          'EJdr0neServer',
          'MQTT DRC连接密码',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      )
ON CONFLICT (param_key) DO NOTHING;

-- FH-SDK配置（新增）
INSERT INTO blade_param (
    id,
    param_name,
    param_key,
    param_value,
    remark,
    create_user,
    create_dept,
    create_time,
    update_user,
    update_time,
    status,
    is_deleted
) VALUES
      (
          nextval('blade_param_seq'),
          'FH-SDK MQTT入站主题',
          'fh.sdk.mqtt.inboundTopic',
          'sys/product/+/status',
          'FH-SDK MQTT订阅的入站主题配置',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      )
ON CONFLICT (param_key) DO NOTHING;

-- EJ-FH-SERVER配置（新增）
INSERT INTO blade_param (
    id,
    param_name,
    param_key,
    param_value,
    remark,
    create_user,
    create_dept,
    create_time,
    update_user,
    update_time,
    status,
    is_deleted
) VALUES
      (
          nextval('blade_param_seq'),
          'EJ-FH-SERVER服务地址',
          'ej.fh.server.url',
          'https://ims.ejdrone.com/fh-cloud',
          'EJ-FH-SERVER服务地址配置',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      )
ON CONFLICT (param_key) DO NOTHING;

-- =========================== 验证迁移结果 ===========================
-- 检查所有配置项是否已正确迁移
SELECT 
    '配置迁移验证报告' as report_title,
    COUNT(*) as total_configs,
    COUNT(CASE WHEN param_key LIKE 'skii.minio.%' THEN 1 END) as skii_minio_count,
    COUNT(CASE WHEN param_key LIKE 'mqtt.basic.%' THEN 1 END) as mqtt_basic_count,
    COUNT(CASE WHEN param_key LIKE 'mqtt.drc.%' THEN 1 END) as mqtt_drc_count,
    COUNT(CASE WHEN param_key LIKE 'fh.sdk.%' THEN 1 END) as fh_sdk_count,
    COUNT(CASE WHEN param_key LIKE 'ej.fh.server.%' THEN 1 END) as ej_fh_server_count
FROM blade_param 
WHERE param_key IN (
    'skii.minio.endpoint', 'skii.minio.accessKey', 'skii.minio.secretKey', 'skii.minio.bucket',
    'mqtt.basic.protocol', 'mqtt.basic.host', 'mqtt.basic.port', 'mqtt.basic.username', 
    'mqtt.basic.password', 'mqtt.basic.clientId', 'mqtt.basic.path',
    'mqtt.drc.protocol', 'mqtt.drc.host', 'mqtt.drc.port', 'mqtt.drc.path', 
    'mqtt.drc.username', 'mqtt.drc.password',
    'fh.sdk.mqtt.inboundTopic',
    'ej.fh.server.url'
);

-- 详细配置列表
SELECT
    param_name,
    param_key,
    param_value,
    remark,
    create_time
FROM blade_param
WHERE param_key IN (
    'skii.minio.endpoint', 'skii.minio.accessKey', 'skii.minio.secretKey', 'skii.minio.bucket',
    'mqtt.basic.protocol', 'mqtt.basic.host', 'mqtt.basic.port', 'mqtt.basic.username', 
    'mqtt.basic.password', 'mqtt.basic.clientId', 'mqtt.basic.path',
    'mqtt.drc.protocol', 'mqtt.drc.host', 'mqtt.drc.port', 'mqtt.drc.path', 
    'mqtt.drc.username', 'mqtt.drc.password',
    'fh.sdk.mqtt.inboundTopic',
    'ej.fh.server.url'
)
ORDER BY 
    CASE 
        WHEN param_key LIKE 'skii.minio.%' THEN 1
        WHEN param_key LIKE 'mqtt.basic.%' THEN 2
        WHEN param_key LIKE 'mqtt.drc.%' THEN 3
        WHEN param_key LIKE 'fh.sdk.%' THEN 4
        WHEN param_key LIKE 'ej.fh.server.%' THEN 5
        ELSE 6
    END,
    param_key;

-- =========================== 执行说明 ===========================
-- 1. 此脚本安全处理已存在的Skii MinIO配置
-- 2. 使用ON CONFLICT DO NOTHING避免重复插入错误
-- 3. 确保所有配置模块完整迁移
-- 4. 验证查询确认迁移状态
-- 5. 预期结果：total_configs = 20, 各模块配置数量正确

-- =========================== 回滚说明（如需要） ===========================
-- 如果需要回滚新增的配置（保留原有Skii MinIO配置）：
-- DELETE FROM blade_param WHERE param_key IN (
--     'mqtt.basic.protocol', 'mqtt.basic.host', 'mqtt.basic.port', 'mqtt.basic.username', 
--     'mqtt.basic.password', 'mqtt.basic.clientId', 'mqtt.basic.path',
--     'mqtt.drc.protocol', 'mqtt.drc.host', 'mqtt.drc.port', 'mqtt.drc.path', 
--     'mqtt.drc.username', 'mqtt.drc.password',
--     'fh.sdk.mqtt.inboundTopic',
--     'ej.fh.server.url'
-- );

-- =========================== 注意事项 ===========================
-- 1. 执行前请备份数据库
-- 2. 建议在测试环境先验证
-- 3. 执行后检查验证查询结果
-- 4. 确认应用程序能正确读取新配置
-- 5. 可以安全删除application.yml中对应的配置段