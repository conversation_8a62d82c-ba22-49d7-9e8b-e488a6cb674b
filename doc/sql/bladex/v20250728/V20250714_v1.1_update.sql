-- 开始事务
BEGIN;

-- 修改grid_geom字段类型为geometry
ALTER TABLE spatial_grid
ALTER COLUMN grid_geom TYPE geometry
USING NULL;



-- 将geom_json的GeoJSON数据转换并更新到grid_geom字段
UPDATE spatial_grid
SET grid_geom = ST_SetSRID(
    ST_GeomFromGeoJSON(
        (geom_json::json->'geometry')::text
    ),
    4326
                )
WHERE geom_json IS NOT NULL
  AND geom_json != '';


-- 验证转换结果
SELECT
    id,
    grid_code,
    ST_AsText(grid_geom) as geometry_wkt,
    ST_GeometryType(grid_geom) as geom_type,
    ST_SRID(grid_geom) as srid
FROM spatial_grid
WHERE grid_geom IS NOT NULL
    LIMIT 5;

-- 如果验证无误，提交事务
COMMIT;



----------
-- 1. 添加字段（不带注释）
ALTER TABLE beach_litter_media
    ADD COLUMN task_name VARCHAR(255) NOT NULL DEFAULT '未命名任务',
  ADD COLUMN task_uuid VARCHAR(64) NOT NULL DEFAULT '',
  ADD COLUMN fh_create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  ADD COLUMN device_name VARCHAR(100) NOT NULL DEFAULT '未知设备';

-- 2. 为字段添加注释
COMMENT ON COLUMN beach_litter_media.task_name IS '任务名称';
COMMENT ON COLUMN beach_litter_media.task_uuid IS '任务唯一 ID';
COMMENT ON COLUMN beach_litter_media.fh_create_time IS '任务执行日期时间';
COMMENT ON COLUMN beach_litter_media.device_name IS '执行设备';
