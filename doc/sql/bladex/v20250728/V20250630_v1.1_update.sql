ALTER TABLE "public"."fh_flight_task"
    ADD COLUMN "flight_distance" int8;

COMMENT ON COLUMN "public"."fh_flight_task"."flight_distance" IS '飞行距离（米）';

INSERT INTO "public"."system_config" ("id", "config_key", "config_value", "description", "global") VALUES (10, 'geovisearth_key', '46844f06955a8057b080c276b75309ac131d6cabffcc9e63fc0052f9bf5f959d', '中科星图的 key 用于地图底图和 POI查询', 't');



-- v1.1版本调整
-- 1. docker-compose 文件修改pg容器镜像名称为： postgis/postgis:16-3.4
-- 2. 开启postgis插件所需执行sql
CREATE EXTENSION IF NOT EXISTS postgis;
-- 3. 查看已安装的插件
SELECT extname FROM pg_extension;
-- 如果返回结果中包含 postgis，则表示扩展已成功启用。

-- 4. 调整表结构：
-- 步骤1: 添加一个临时的 geometry 列
ALTER TABLE public.event ADD COLUMN geom GEOMETRY(Point, 4326);

-- 步骤2: 从 varchar 更新数据到新列 (假设格式是 '经度,纬度')
UPDATE public.event
SET geom = ST_SetSRID(ST_MakePoint(
                          CAST(split_part(location, ',', 1) AS DOUBLE PRECISION),
                          CAST(split_part(location, ',', 2) AS DOUBLE PRECISION)
                      ), 4326)
WHERE location IS NOT NULL AND location LIKE '%,%';

-- 步骤3: 删除旧列
ALTER TABLE public.event DROP COLUMN location;

-- 步骤4: 重命名新列
ALTER TABLE public.event RENAME COLUMN geom TO location;
