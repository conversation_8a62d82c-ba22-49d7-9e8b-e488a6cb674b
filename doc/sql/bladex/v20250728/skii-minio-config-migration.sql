-- 应用配置迁移脚本（完整版）
-- 将application.yml中的多种配置迁移到blade_param表中
-- 包括：Ski<PERSON> MinIO、MQTT BASIC、MQTT DRC、FH-SDK、EJ-FH-SERVER配置
-- 执行前请确保blade_param表已存在

-- 创建序列（如果不存在）
CREATE SEQUENCE IF NOT EXISTS blade_param_seq START WITH 1 INCREMENT BY 1;

-- =========================== Skii MinIO配置 ===========================
-- 插入Skii MinIO配置参数
INSERT INTO blade_param (
    id,
    param_name,
    param_key,
    param_value,
    remark,
    create_user,
    create_dept,
    create_time,
    update_user,
    update_time,
    status,
    is_deleted
) VALUES
      (
          nextval('blade_param_seq'),
          'Skii MinIO服务端点',
          'skii.minio.endpoint',
          'https://www.skii.ejdrone.com:20802',
          'Skii MinIO服务器地址配置',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'Skii MinIO访问密钥',
          'skii.minio.accessKey',
          'sZ6iY65sQcXfj6aDppsN',
          'Skii MinIO访问密钥配置',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'Skii MinIO秘密密钥',
          'skii.minio.secretKey',
          'D0of1tjsvdk1i4hADKBVWQmKX9KxAGDwFJ996ZLS',
          'Skii MinIO秘密密钥配置',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'Skii MinIO存储桶',
          'skii.minio.bucket',
          'file-storage-privatization',
          'Skii MinIO默认存储桶名称配置',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      );

-- =========================== MQTT BASIC配置 ===========================
-- 插入MQTT BASIC配置参数
INSERT INTO blade_param (
    id,
    param_name,
    param_key,
    param_value,
    remark,
    create_user,
    create_dept,
    create_time,
    update_user,
    update_time,
    status,
    is_deleted
) VALUES
      (
          nextval('blade_param_seq'),
          'MQTT BASIC协议',
          'mqtt.basic.protocol',
          'MQTT',
          'MQTT BASIC连接协议配置',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT BASIC主机',
          'mqtt.basic.host',
          'ecs04.ejdrone.com',
          'MQTT BASIC服务器主机地址',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT BASIC端口',
          'mqtt.basic.port',
          '1883',
          'MQTT BASIC服务器端口',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT BASIC用户名',
          'mqtt.basic.username',
          'MLP-Server-User',
          'MQTT BASIC连接用户名',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT BASIC密码',
          'mqtt.basic.password',
          'EJdr0neServer',
          'MQTT BASIC连接密码',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT BASIC客户端ID',
          'mqtt.basic.clientId',
          'mlp-server-0',
          'MQTT BASIC客户端ID',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT BASIC路径',
          'mqtt.basic.path',
          '',
          'MQTT BASIC连接路径',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      );

-- =========================== MQTT DRC配置 ===========================
-- 插入MQTT DRC配置参数
INSERT INTO blade_param (
    id,
    param_name,
    param_key,
    param_value,
    remark,
    create_user,
    create_dept,
    create_time,
    update_user,
    update_time,
    status,
    is_deleted
) VALUES
      (
          nextval('blade_param_seq'),
          'MQTT DRC协议',
          'mqtt.drc.protocol',
          'WS',
          'MQTT DRC连接协议配置',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT DRC主机',
          'mqtt.drc.host',
          'ecs04.ejdrone.com',
          'MQTT DRC服务器主机地址',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT DRC端口',
          'mqtt.drc.port',
          '8083',
          'MQTT DRC服务器端口',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT DRC路径',
          'mqtt.drc.path',
          '/mqtt',
          'MQTT DRC连接路径',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT DRC用户名',
          'mqtt.drc.username',
          'MLP-Server-User',
          'MQTT DRC连接用户名',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      ),
      (
          nextval('blade_param_seq'),
          'MQTT DRC密码',
          'mqtt.drc.password',
          'EJdr0neServer',
          'MQTT DRC连接密码',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      );

-- =========================== FH-SDK配置 ===========================
-- 插入FH-SDK配置参数
INSERT INTO blade_param (
    id,
    param_name,
    param_key,
    param_value,
    remark,
    create_user,
    create_dept,
    create_time,
    update_user,
    update_time,
    status,
    is_deleted
) VALUES
      (
          nextval('blade_param_seq'),
          'FH-SDK MQTT入站主题',
          'fh.sdk.mqtt.inboundTopic',
          'sys/product/+/status',
          'FH-SDK MQTT订阅的入站主题配置',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      );

-- =========================== EJ-FH-SERVER配置 ===========================
-- 插入EJ-FH-SERVER配置参数
INSERT INTO blade_param (
    id,
    param_name,
    param_key,
    param_value,
    remark,
    create_user,
    create_dept,
    create_time,
    update_user,
    update_time,
    status,
    is_deleted
) VALUES
      (
          nextval('blade_param_seq'),
          'EJ-FH-SERVER服务地址',
          'ej.fh.server.url',
          'https://ims.ejdrone.com/fh-cloud',
          'EJ-FH-SERVER服务地址配置',
          1123598821738675201,
          1123598813738675201,
          NOW(),
          1123598821738675201,
          NOW(),
          1,
          0
      );

-- =========================== 验证插入结果 ===========================
-- 验证所有插入的配置数据
SELECT
    param_name,
    param_key,
    param_value,
    remark,
    create_time
FROM blade_param
WHERE param_key IN (
    -- Skii MinIO配置
    'skii.minio.endpoint',
    'skii.minio.accessKey',
    'skii.minio.secretKey',
    'skii.minio.bucket',
    -- MQTT BASIC配置
    'mqtt.basic.protocol',
    'mqtt.basic.host',
    'mqtt.basic.port',
    'mqtt.basic.username',
    'mqtt.basic.password',
    'mqtt.basic.clientId',
    'mqtt.basic.path',
    -- MQTT DRC配置
    'mqtt.drc.protocol',
    'mqtt.drc.host',
    'mqtt.drc.port',
    'mqtt.drc.path',
    'mqtt.drc.username',
    'mqtt.drc.password',
    -- FH-SDK配置
    'fh.sdk.mqtt.inboundTopic',
    -- EJ-FH-SERVER配置
    'ej.fh.server.url'
)
ORDER BY param_key;

-- =========================== 注意事项 ===========================
-- 1. 执行此脚本后，可以从application.yml中删除对应的配置段
-- 2. 如果使用MySQL数据库，请将nextval('blade_param_seq')替换为NULL（使用自增ID）
-- 3. create_user和create_dept的值请根据实际情况调整
-- 4. 建议在生产环境执行前先在测试环境验证
-- 5. 支持多环境配置降级：dev、test、prod环境
-- 6. 配置变更会自动同步到Redis缓存

-- =========================== 可选：备份现有配置 ===========================
-- CREATE TABLE blade_param_backup_application_config AS
-- SELECT * FROM blade_param WHERE param_key LIKE 'skii.minio.%' 
--    OR param_key LIKE 'mqtt.%' 
--    OR param_key LIKE 'fh.sdk.%' 
--    OR param_key LIKE 'ej.fh.server.%';

-- =========================== 环境配置说明 ===========================
-- 每个环境（dev、test、prod）使用独立的数据库
-- 配置键统一，不包含环境后缀
-- 例如：所有环境都使用 'mqtt.basic.host'，但在不同环境的数据库中存储对应环境的值
-- 
-- 环境配置策略：
-- 1. 数据库配置（最高优先级） - 存储在当前环境对应的数据库中
-- 2. YAML降级配置 - 从对应环境的application-{env}.yml读取
--    - dev环境 → application-dev.yml
--    - test环境 → application-test.yml  
--    - prod环境 → application-prod.yml