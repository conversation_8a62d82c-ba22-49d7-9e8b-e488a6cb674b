-- 主查询字段复合索引（按选择性从高到低排序）
CREATE INDEX CONCURRENTLY idx_event_main_query
    ON event(event_status, discovery_method, waste_material, waste_size, grid_id, handler_staff_id);

-- 时间字段索引（支持范围查询）
CREATE INDEX CONCURRENTLY idx_event_discovery_time
    ON event(discovery_time DESC);

-- 状态+时间复合索引（最常用的查询组合）
CREATE INDEX CONCURRENTLY idx_event_status_time
    ON event(event_status, discovery_time DESC);

-- 网格+时间复合索引（如果按网格查询较多）
CREATE INDEX CONCURRENTLY idx_event_grid_time
    ON event(grid_id, discovery_time DESC)
    WHERE grid_id IS NOT NULL;

-- 地理位置空间索引
CREATE INDEX CONCURRENTLY idx_event_location_gist
    ON event USING GIST(location)
    WHERE location IS NOT NULL;

-- 只索引有处理人员的事件
CREATE INDEX CONCURRENTLY idx_event_with_handler
    ON event(handler_staff_id, event_status, discovery_time DESC)
    WHERE handler_staff_id IS NOT NULL;

-- 主查询字段复合索引
CREATE INDEX CONCURRENTLY idx_flight_task_query
    ON fh_flight_task (task_status, task_type, sn, begin_at DESC)
    WHERE is_deleted = 0;

-- 时间范围查询索引
CREATE INDEX CONCURRENTLY idx_flight_task_time_range
    ON fh_flight_task (begin_at, end_at)
    WHERE is_deleted = 0;

-- 设备和状态组合索引
CREATE INDEX CONCURRENTLY idx_flight_task_sn_status
    ON fh_flight_task (sn, task_status, begin_at DESC)
    WHERE is_deleted = 0;
