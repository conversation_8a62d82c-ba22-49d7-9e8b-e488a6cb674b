# 创建飞行任务

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /openapi/v0.1/flight-task:
    post:
      summary: 创建飞行任务
      deprecated: false
      description: 创建航线飞行任务，可通过 `航线上传完成通知`接口获取上传到司空 2 上的航线 UUID，然后调用此接口创建航线任务。
      tags:
        - 任务管理
      parameters:
        - name: X-Request-Id
          in: header
          description: 请求唯一标识
          required: false
          example: '{{$string.uuid}}'
          schema:
            type: string
            format: uuid
            default: '{{$string.uuid}}'
        - name: X-Language
          in: header
          description: ''
          example: zh
          schema:
            type: string
            enum:
              - zh
              - en
            x-apifox-enum:
              - value: zh
                name: ''
                description: 简体中文
              - value: en
                name: ''
                description: 英文
        - name: X-Project-Uuid
          in: header
          description: 项目编号
          example: '{{X-Project-Uuid}}'
          schema:
            type: string
            default: '{{X-Project-Uuid}}'
        - name: X-User-Token
          in: header
          description: X-User-Token
          required: true
          example: '{{X-User-Token}}'
          schema:
            type: string
            default: '{{X-User-Token}}'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 任务名称
                wayline_uuid:
                  type: string
                  description: 航线文件UUID
                sn:
                  type: string
                  description: 机场SN
                landing_dock_sn:
                  type: string
                  description: 降落机场SN，非蛙跳任务可不填
                rth_altitude:
                  description: 返航高度
                  type: integer
                rth_mode:
                  type: string
                  description: 返航模式
                  enum:
                    - optimal
                    - preset
                  x-apifox-enum:
                    - value: optimal
                      name: ''
                      description: 智能高度
                    - value: preset
                      name: ''
                      description: 设定高度
                wayline_precision_type:
                  type: string
                  description: 任务精度
                  enum:
                    - gps
                    - rtk
                  x-apifox-enum:
                    - value: gps
                      name: ''
                      description: ' GNSS 任务：飞行器无需等待 RTK 收敛便可以直接开始执行。建议精度要求不高的任务使用该模式。'
                    - value: rtk
                      name: ''
                      description: >-
                        高精度 RTK 任务：飞行器起飞后会在空中等待 RTK
                        收敛后再执行任务，等待过程中无法暂停任务。建议高精度航线任务使用该模式。
                out_of_control_action_in_flight:
                  type: string
                  description: 丢失信号后无人机动作
                  enum:
                    - return_home
                    - continue_task
                  x-apifox-enum:
                    - value: return_home
                      name: ''
                      description: 返航
                    - value: continue_task
                      name: ''
                      description: 继续执行
                resumable_status:
                  type: string
                  description: 自动断点续飞
                  enum:
                    - auto
                    - manual
                  x-apifox-enum:
                    - value: auto
                      name: ''
                      description: 自动断点续飞
                    - value: manual
                      name: ''
                      description: 手动断点续飞
                task_type:
                  type: string
                  description: 任务类型
                  enum:
                    - immediate
                    - timed
                    - recurring
                    - continuous
                  x-apifox-enum:
                    - value: immediate
                      name: ''
                      description: 立即任务
                    - value: timed
                      name: ''
                      description: 单次定时任务
                    - value: recurring
                      name: ''
                      description: 重复任务
                    - value: continuous
                      name: ''
                      description: 连续任务
                time_zone:
                  type: string
                  description: 时区，TZ database中的时区名称
                repeat_type:
                  type: string
                  description: 任务重复模式
                  enum:
                    - nonrepeating
                    - daily
                    - weekly
                    - absolute_monthly
                    - relative_monthly
                  x-apifox-enum:
                    - value: nonrepeating
                      name: ''
                      description: 不重复 默认
                    - value: daily
                      name: ''
                      description: 每几天
                    - value: weekly
                      name: ''
                      description: 每几周
                    - value: absolute_monthly
                      name: ''
                      description: 每几月（按日期）
                    - value: relative_monthly
                      name: ''
                      description: 每几月(按星期)
                repeat_option:
                  anyOf:
                    - $ref: '#/components/schemas/%20%20task_repeat_option_daily'
                    - $ref: '#/components/schemas/task_repeat_option_weekly'
                    - $ref: >-
                        #/components/schemas/%20%20task_repeat_option_absolute_monthly
                    - $ref: '#/components/schemas/task_repeat_option_relative_monthly'
                begin_at:
                  type: integer
                  description: >-
                    开始时间，秒级时间戳，立即任务不需要填，对于定时任务这个值代表任务执行时间。对于重复任务和连续任务这个值代表任务的开始时间。
                  nullable: true
                end_at:
                  type: integer
                  description: 结束时间，秒级时间戳，重复/连续任务必须填写
                  nullable: true
                recurring_task_start_time_list:
                  type: array
                  items:
                    type: integer
                    description: 秒级时间戳
                  description: 重复任务的多个开始执行的时间，秒级时间戳，必须跟“begin_at”时间同一天。
                  uniqueItems: true
                continuous_task_periods:
                  type: array
                  items:
                    type: array
                    items:
                      type: integer
                      description: 秒级时间戳
                    minItems: 2
                    maxItems: 2
                    uniqueItems: true
                    description: 时间段：[开始，结束]
                  description: 连续任务的多个执行时间段，秒级时间戳，必须跟“begin_at”时间同一天。
                min_battery_capacity:
                  type: integer
                  minimum: 50
                  maximum: 100
                  description: 连续执行最低执行电量
              required:
                - name
                - sn
                - time_zone
                - wayline_uuid
                - rth_altitude
                - rth_mode
                - wayline_precision_type
                - out_of_control_action_in_flight
                - resumable_status
                - task_type
                - repeat_type
                - begin_at
                - end_at
                - recurring_task_start_time_list
                - continuous_task_periods
                - min_battery_capacity
                - repeat_option
                - landing_dock_sn
              x-apifox-orders:
                - name
                - wayline_uuid
                - sn
                - landing_dock_sn
                - rth_altitude
                - rth_mode
                - wayline_precision_type
                - out_of_control_action_in_flight
                - resumable_status
                - task_type
                - time_zone
                - repeat_type
                - repeat_option
                - begin_at
                - end_at
                - recurring_task_start_time_list
                - continuous_task_periods
                - min_battery_capacity
              x-apifox-ignore-properties: []
            example:
              name: flight_task_01
              sn: 8PHDM8D0010097
              time_zone: Asia/Chongqing
              wayline_uuid: 80232917-2f63-4375-8614-0c70c4458aa3
              rth_altitude: 110
              rth_mode: optimal
              wayline_precision_type: rtk
              out_of_control_action_in_flight: return_home
              resumable_status: manual
              task_type: immediate
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 非0代表异常
                  message:
                    type: string
                    description: 消息提示
                  data:
                    type: object
                    properties:
                      task_uuid:
                        type: string
                    required:
                      - task_uuid
                    x-apifox-orders:
                      - task_uuid
                    x-apifox-ignore-properties: []
                required:
                  - code
                  - message
                  - data
                x-apifox-orders:
                  - code
                  - message
                  - data
                x-apifox-ignore-properties: []
              examples:
                '1':
                  summary: 成功示例
                  value:
                    code: 0
                    message: success
                    data:
                      task_uuid: 1280bab3-3b0d-4517-a53c-3257e48319e5
                '2':
                  summary: 异常示例
                  value:
                    code: 219014
                    message: 机场不属于该项目，无法执行当前操作或执行飞行任务，请将机场迁回该项目或到机场所在项目重试
                    data:
                      task_uuid: ''
          headers: {}
          x-apifox-name: 成功
      security: []
      x-apifox-folder: 任务管理
      x-apifox-status: developing
      x-run-in-apifox: https://app.apifox.com/web/project/5271812/apis/api-221842037-run
components:
  schemas:
    task_repeat_option_relative_monthly:
      type: object
      properties:
        interval:
          type: integer
          description: '重复间隔时间: 最小1，每几月'
          minimum: 1
        week_of_month:
          type: integer
          description: 每月第几周，1-4
          minimum: 1
          maximum: 4
        days_of_week:
          type: array
          items:
            type: integer
            minimum: 0
            maximum: 6
          description: 每周执行日数组，每周第几天，0-6，0=周日，1=周一
      x-apifox-orders:
        - interval
        - week_of_month
        - days_of_week
      required:
        - interval
        - days_of_week
        - week_of_month
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    '  task_repeat_option_absolute_monthly':
      type: object
      properties:
        interval:
          type: integer
          description: '重复间隔时间: 最小1，每几月'
          minimum: 1
        days_of_month:
          type: array
          items:
            type: integer
            minimum: 1
            maximum: 31
          description: 每月执行日数组，每月第几天，1-31
      x-apifox-orders:
        - interval
        - days_of_month
      required:
        - interval
        - days_of_month
      x-apifox-refs: {}
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    task_repeat_option_weekly:
      type: object
      properties:
        interval:
          type: integer
          description: '重复间隔时间: 最小1，每几周'
          minimum: 1
        days_of_week:
          type: array
          items:
            type: integer
            minimum: 0
            maximum: 6
          description: 每周执行日数组，每周第几天，0-6，0=周日，1=周一
      x-apifox-orders:
        - interval
        - days_of_week
      x-apifox-refs: {}
      required:
        - interval
        - days_of_week
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    '  task_repeat_option_daily':
      type: object
      properties:
        interval:
          type: integer
          description: '重复间隔时间: 最小1，每几天'
          minimum: 1
      x-apifox-orders:
        - interval
      required:
        - interval
      x-apifox-refs: {}
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
  securitySchemes: {}
servers:
  - url: https://es-flight-api-cn.djigate.com
    description: 对外文档使用，勿动！！
security: []

```