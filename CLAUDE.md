# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

所有回答和思考必须使用中文

This is a Spring Boot application built on the BladeX framework (version 4.1.0.RELEASE). It's a beach waste detection and management system with multi-tenant architecture, OAuth2 authentication, and comprehensive API functionality.

## Build and Development Commands

### Maven Commands
- **Build**: `mvn clean package`
- **Run locally**: `mvn spring-boot:run`
- **Run tests**: `mvn test`
- **Generate code**: Available through `/blade-develop/code` controller endpoints

### Docker Commands
- **Build image**: `mvn clean package` then `docker build -t blade-api .`
- **Run container**: `docker run -p 8800:80 blade-api`

### Application Profiles
- **Development**: `--spring.profiles.active=dev`
- **Testing**: `--spring.profiles.active=test` 
- **Production**: `--spring.profiles.active=prod`

## Architecture Overview

### Core Framework
- **Base Framework**: BladeX 4.1.0.RELEASE (Commercial Spring Boot framework)
- **Java Version**: 17
- **Database**: PostgreSQL (configurable for MySQL, Oracle, SQL Server, DaMeng, YashanDB)
- **ORM**: MyBatis-Plus with custom geometry type handlers
- **Security**: OAuth2 with JWT tokens, multi-client support
- **Multi-tenancy**: Field-based isolation (configurable for database isolation)

### Key Modules Structure
```
src/main/java/org/springblade/
├── common/           # Shared utilities, caches, constants
├── modules/
│   ├── auth/         # OAuth2 authentication & authorization
│   ├── beachwaste/   # Beach waste detection core business logic
│   ├── fh/           # Flight tasks and drone management
│   ├── system/       # User, role, tenant, menu management
│   ├── resource/     # File storage (OSS), SMS services
│   └── desk/         # Dashboard and notifications
└── component/        # MQTT, scheduling components
```

### Beach Waste Detection Module
The primary business module (`modules/beachwaste/`) includes:
- **Spatial grid management** with PostGIS integration
- **Event detection and tracking** with AI service integration
- **Media processing** with MinIO storage and thumbnail generation
- **Real-time video streaming** integration
- **Map tile services** for GIS visualization

### External Service Integrations
- **MinIO**: Object storage for media files
- **AI Service**: Frame recognition at `192.168.103.161:9090`
- **Stream Media**: Live video streaming via ZLMediaKit
- **MQTT**: Device communication and real-time messaging
- **PostGIS**: Spatial data processing

## Database Configuration

### Supported Databases
- PostgreSQL (default, with PostGIS extensions)
- MySQL, Oracle, SQL Server, DaMeng, YashanDB

### Key Type Handlers
- `GeometryTypeHandler`: Handles PostGIS geometry types
- `PointTypeHandler`: Specifically for Point geometries

## Security & Authentication

### OAuth2 Configuration
- Multiple grant types: password, sms, captcha, social, wechat
- Client-based authorization with path pattern matching
- SM2 encryption support for enhanced security

### Multi-tenant Features
- Field-based tenant isolation (default)
- Configurable database-level isolation
- Tenant-aware data scoping

## Development Guidelines

### Code Generation
Use the develop module controllers for rapid CRUD generation:
- Model management via `/blade-develop/model/**`
- Code generation via `/blade-develop/code/**`
- Custom templates and prototypes supported

### Spatial Data Handling
- All location data uses PostGIS Point type with SRID 4326 (WGS84)
- Map tiles use SRID 3857 (Web Mercator) for rendering
- Custom serializers handle geometry conversion to/from JSON

### File Storage Patterns
- Primary storage: MinIO with bucket `processed-images`
- Thumbnail generation: Automated with configurable dimensions
- Support for multiple OSS providers (AWS S3, Alibaba OSS, etc.)

## Testing and Quality

### Testing Framework
- Uses `blade-core-test` module
- JUnit integration with Spring Boot Test

### API Documentation
- Swagger/OpenAPI 3 with Knife4j UI at `/doc.html`
- Comprehensive API documentation for all modules

## Environment-Specific Notes

### Development Setup
- Default port: 80 (configurable)
- Database connection pooling via Druid
- Redis session management enabled
- MQTT integration for real-time updates

### Production Considerations
- Undertow server with tuned thread pools
- Connection pooling optimized for high load
- Distributed caching with Redis
- Scheduled tasks for cleanup and maintenance