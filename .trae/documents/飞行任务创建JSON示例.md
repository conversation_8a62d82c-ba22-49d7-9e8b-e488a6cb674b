# 飞行任务创建JSON示例

## 基于图片配置的JSON构造

根据提供的任务创建界面配置，以下是不同任务类型的JSON示例：

### 1. 立即任务 (immediate)

```json
{
  "name": "冲突2",
  "wayline_uuid": "白龙-规距内测试航线",
  "sn": "海洋垃圾机场3-白龙",
  "landing_dock_sn": null,
  "rth_altitude": 120,
  "rth_mode": "optimal",
  "wayline_precision_type": "rtk",
  "out_of_control_action_in_flight": "return_home",
  "resumable_status": "auto",
  "task_type": "immediate",
  "time_zone": "Asia/Shanghai",
  "repeat_type": "nonrepeating",
  "repeat_option": null,
  "begin_at": null,
  "end_at": null,
  "recurring_task_start_time_list": [],
  "continuous_task_periods": [],
  "min_battery_capacity": 90
}
```

### 2. 单次定时任务 (timed)

```json
{
  "name": "冲突2",
  "wayline_uuid": "白龙-规距内测试航线",
  "sn": "海洋垃圾机场3-白龙",
  "landing_dock_sn": null,
  "rth_altitude": 120,
  "rth_mode": "optimal",
  "wayline_precision_type": "rtk",
  "out_of_control_action_in_flight": "return_home",
  "resumable_status": "auto",
  "task_type": "timed",
  "time_zone": "Asia/Shanghai",
  "repeat_type": "nonrepeating",
  "repeat_option": null,
  "begin_at": 1722330909,
  "end_at": 1722330909,
  "recurring_task_start_time_list": [],
  "continuous_task_periods": [],
  "min_battery_capacity": 90
}
```

### 3. 重复任务 (recurring) - 基于图片配置

根据图片显示的配置：

* 执行日期：2025-07-30 \~ 2025-07-31

* 执行时间：17:35:09 - 18:35:09

* 重复频率：每1月

* 重复规则：按日期，选中30、31日

```json
{
  "name": "冲突2",
  "wayline_uuid": "白龙-规距内测试航线",
  "sn": "海洋垃圾机场3-白龙",
  "landing_dock_sn": null,
  "rth_altitude": 120,
  "rth_mode": "optimal",
  "wayline_precision_type": "rtk",
  "out_of_control_action_in_flight": "return_home",
  "resumable_status": "auto",
  "task_type": "recurring",
  "time_zone": "Asia/Shanghai",
  "repeat_type": "absolute_monthly",
  "repeat_option": {
    "interval": 1,
    "days_of_month": [30, 31]
  },
  "begin_at": 1722330909,
  "end_at": 1722417309,
  "recurring_task_start_time_list": [1722330909, 1722417309],
  "continuous_task_periods": [],
  "min_battery_capacity": 90
}
```

### 4. 连续任务 (continuous)

```json
{
  "name": "冲突2",
  "wayline_uuid": "白龙-规距内测试航线",
  "sn": "海洋垃圾机场3-白龙",
  "landing_dock_sn": null,
  "rth_altitude": 120,
  "rth_mode": "optimal",
  "wayline_precision_type": "rtk",
  "out_of_control_action_in_flight": "return_home",
  "resumable_status": "auto",
  "task_type": "continuous",
  "time_zone": "Asia/Shanghai",
  "repeat_type": "daily",
  "repeat_option": {
    "interval": 1
  },
  "begin_at": 1722330909,
  "end_at": 1722417309,
  "recurring_task_start_time_list": [],
  "continuous_task_periods": [
    [1722330909, 1722334509],
    [1722417309, 1722420909]
  ],
  "min_battery_capacity": 90
}
```

## 时间戳说明

* `1722330909` = 2024-07-30 17:35:09 (UTC+8)

* `1722334509` = 2024-07-30 18:35:09 (UTC+8)

* `1722417309` = 2024-07-31 17:35:09 (UTC+8)

* `1722420909` = 2024-07-31 18:35:09 (UTC+8)

## 字段说明

* `name`: 任务名称

* `wayline_uuid`: 航线UUID，对应界面中的"白龙-规距内测试航线"

* `sn`: 机场SN，对应界面中的"海洋垃圾机场3-白龙"

* `task_type`: 任务类型，根据界面选择的"连续执行"对应"continuous"

* `repeat_type`: 重复类型，"按日期"对应"absolute\_monthly"

* `repeat_option`: 重复选项，包含间隔和具体日期

* `begin_at/end_at`: 任务的整体时间范围

* `recurring_task_start_time_list`: 重复任务的具体执行时间点

* `continuous_task_periods`: 连续任务的时间段数组

