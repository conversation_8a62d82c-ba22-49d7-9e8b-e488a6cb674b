# 飞行任务时间冲突校验系统产品需求文档

## 1. 产品概述

本系统旨在为飞行任务创建提供完善的时间冲突校验机制，确保新创建的任务与已有任务不会产生时间重叠或冲突，保障飞行安全和任务执行的有序性。

系统通过调用大疆司空OpenAPI获取已有任务信息，对立即任务、单次定时任务、重复任务、连续任务等不同类型进行精确的时间段校验，确保任务间至少保持1分钟的安全间隔。

## 2. 核心功能

### 2.1 用户角色

| 角色 | 注册方式 | 核心权限 |
|------|----------|----------|
| 系统管理员 | 系统内置账户 | 可创建和管理所有类型的飞行任务 |
| 操作员 | 管理员分配账户 | 可创建飞行任务，受时间冲突校验限制 |

### 2.2 功能模块

本系统主要包含以下核心页面：
1. **任务创建页面**：提供任务配置界面，支持四种任务类型的参数设置
2. **时间校验页面**：显示时间冲突检查结果和冲突详情
3. **任务管理页面**：展示已有任务列表和时间安排

### 2.3 页面详情

| 页面名称 | 模块名称 | 功能描述 |
|----------|----------|----------|
| 任务创建页面 | 基础信息配置 | 配置任务名称、航线UUID、机场SN、返航高度等基础参数 |
| 任务创建页面 | 任务类型选择 | 支持立即任务、单次定时、重复任务、连续任务四种类型选择 |
| 任务创建页面 | 时间参数配置 | 根据任务类型配置相应的时间参数（开始时间、结束时间、重复规则等） |
| 任务创建页面 | 重复规则配置 | 配置每日、每周、每月重复模式和具体执行时间 |
| 时间校验页面 | 冲突检测模块 | 实时检测新任务与已有任务的时间冲突情况 |
| 时间校验页面 | 冲突详情展示 | 显示具体的冲突时间段和冲突任务信息 |
| 任务管理页面 | 任务列表展示 | 展示所有已创建任务的基本信息和执行状态 |
| 任务管理页面 | 时间轴视图 | 以时间轴形式展示任务的执行时间安排 |

## 3. 核心流程

### 3.1 任务创建流程

用户在任务创建页面填写任务基础信息 → 选择任务类型 → 配置时间参数 → 系统解析时间参数生成具体执行时间段 → 调用OpenAPI获取已有任务信息 → 执行时间冲突校验 → 校验通过则创建任务，校验失败则提示冲突信息

### 3.2 时间校验流程

系统根据任务类型解析时间参数 → 生成所有具体执行时间段 → 为每个时间段前后扩展1分钟形成校验范围 → 调用OpenAPI查询校验范围内的已有任务 → 检测是否存在时间重叠 → 返回校验结果

```mermaid
graph TD
    A[任务创建页面] --> B[时间校验页面]
    B --> C{校验通过?}
    C -->|是| D[任务管理页面]
    C -->|否| B
    D --> A
```

## 4. 用户界面设计

### 4.1 设计风格

- 主色调：深蓝色(#1890ff)，辅助色：浅灰色(#f5f5f5)
- 按钮样式：圆角矩形，3D阴影效果
- 字体：微软雅黑，主要字号14px，标题字号16px
- 布局风格：卡片式布局，顶部导航栏
- 图标风格：线性图标，统一使用Ant Design图标库

### 4.2 页面设计概览

| 页面名称 | 模块名称 | UI元素 |
|----------|----------|--------|
| 任务创建页面 | 基础信息配置 | 表单输入框、下拉选择器，白色背景卡片，蓝色边框 |
| 任务创建页面 | 任务类型选择 | 单选按钮组，图标+文字形式，选中状态蓝色高亮 |
| 任务创建页面 | 时间参数配置 | 日期时间选择器、数字输入框，动态显示隐藏 |
| 时间校验页面 | 冲突检测模块 | 进度条、状态图标，实时更新检测状态 |
| 时间校验页面 | 冲突详情展示 | 表格形式展示，红色警告色标识冲突项 |
| 任务管理页面 | 任务列表展示 | 数据表格，支持分页和筛选功能 |
| 任务管理页面 | 时间轴视图 | 甘特图样式，不同任务类型用不同颜色区分 |

### 4.3 响应式设计

系统采用桌面优先设计，支持移动端自适应，在平板和手机端会自动调整布局和字体大小，确保良好的触控交互体验。