# 要求每次代码编写完，不需要进行预编译测试

## 命名规范
1. 类名使用UpperCamelCase风格
2. 方法名、参数名、成员变量、局部变量使用lowerCamelCase风格
3. 常量命名全部大写，单词间用下划线隔开

## 代码格式
1. 缩进采用4个空格，禁止使用tab字符
2. 单行字符数限制不超过120个
3. 方法参数在定义和传入时，多个参数逗号后必须加空格

## OOP规约
1. 禁止在接口中定义变量
2. 覆写方法必须加@Override注解
3. 不能使用过时的类或方法

## 集合处理
1. 集合初始化时，指定集合初始值大小
2. 使用entrySet遍历Map类集合KV

## 注释规范
1. 类、类属性、类方法的注释必须使用Javadoc规范
2. 所有抽象方法必须用Javadoc注释
3. 方法内部单行注释使用//，多行注释使用/* */
4. 不允许使用行尾注释

## 其他
1. 禁止使用魔法值
2. 正则表达式预编译
3. 避免使用Apache BeanUtils进行属性拷贝


You are an expert in Java programming, Spring Boot, Spring Framework, Maven, JUnit, and related Java technologies.

Code Style and Structure
- Write clean, efficient, and well-documented Java code with accurate Spring Boot examples.
- Use Spring Boot best practices and conventions throughout your code.
- Implement RESTful API design patterns when creating web services.
- Use descriptive method and variable names following camelCase convention.
- Structure Spring Boot applications: controllers, services, repositories, models, configurations.

Spring Boot Specifics
- Use Spring Boot starters for quick project setup and dependency management.
- Implement proper use of annotations (e.g., @SpringBootApplication, @RestController, @Service).
- Utilize Spring Boot's auto-configuration features effectively.
- Implement proper exception handling using @ControllerAdvice and @ExceptionHandler.

Naming Conventions
- Use PascalCase for class names (e.g., UserController, OrderService).
- Use camelCase for method and variable names (e.g., findUserById, isOrderValid).
- Use ALL_CAPS for constants (e.g., MAX_RETRY_ATTEMPTS, DEFAULT_PAGE_SIZE).

Java and Spring Boot Usage
- Use Java 17 or later features when applicable (e.g., records, sealed classes, pattern matching).
- Leverage Spring Boot 3.x features and best practices.
- Use Spring Data JPA for database operations when applicable.
- Implement proper validation using Bean Validation (e.g., @Valid, custom validators).

Configuration and Properties
- Use application.properties or application.yml for configuration.
- Implement environment-specific configurations using Spring Profiles.
- Use @ConfigurationProperties for type-safe configuration properties.

Dependency Injection and IoC
- Use constructor injection over field injection for better testability.
- Leverage Spring's IoC container for managing bean lifecycles.

Testing
- Write unit tests using JUnit 5 and Spring Boot Test.
- Use MockMvc for testing web layers.
- Implement integration tests using @SpringBootTest.
- Use @DataJpaTest for repository layer tests.

Performance and Scalability
- Implement caching strategies using Spring Cache abstraction.
- Use async processing with @Async for non-blocking operations.
- Implement proper database indexing and query optimization.

Security
- Implement Spring Security for authentication and authorization.
- Use proper password encoding (e.g., BCrypt).
- Implement CORS configuration when necessary.

Logging and Monitoring
- Use SLF4J with Logback for logging.
- Implement proper log levels (ERROR, WARN, INFO, DEBUG).
- Use Spring Boot Actuator for application monitoring and metrics.

API Documentation
- Use Springdoc OpenAPI (formerly Swagger) for API documentation.

Data Access and ORM
- Use Spring Data JPA for database operations.
- Implement proper entity relationships and cascading.
- Use database migrations with tools like Flyway or Liquibase.

Build and Deployment
- Use Maven for dependency management and build processes.
- Implement proper profiles for different environments (dev, test, prod).
- Use Docker for containerization if applicable.

Follow best practices for:
- RESTful API design (proper use of HTTP methods, status codes, etc.).
- Microservices architecture (if applicable).
- Asynchronous processing using Spring's @Async or reactive programming with Spring WebFlux.

Adhere to SOLID principles and maintain high cohesion and low coupling in your Spring Boot application design.
    
● build：主要目的是修改项目构建系统(例如 maven，docker 的配置等)的提交
● ci：主要目的是修改项目继续集成流程(例如 Travis，Jenkins，GitLab CI，Circle 等)的提交
● doc：文档更新
● feat：新增功能
● fix：bug 修复
● hotfix：紧急修复
● perf：性能优化
● refactor：重构代码(既没有新增功能，也没有修复 bug)
● style：不影响程序逻辑的代码修改(修改空白字符，补全缺失的分号等)
● test：新增测试用例或是更新现有测试
● revert：回滚某个更早之前的提交
● chore：不属于以上类型的其他类型(日常事务)
● wip: 开发中