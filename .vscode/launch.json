{"version": "0.2.0", "configurations": [{"type": "java", "name": "Launch BladeX-Boot Application", "request": "launch", "mainClass": "org.springblade.Application", "projectName": "BladeX-Boot", "args": "", "vmArgs": "-Dfile.encoding=UTF-8 -Dspring.profiles.active=dev", "console": "internalConsole", "stopOnEntry": false, "cwd": "${workspaceFolder}", "env": {"JAVA_TOOL_OPTIONS": "-Dfile.encoding=UTF-8"}, "preLaunchTask": ""}, {"type": "java", "name": "Debug BladeX-Boot Application", "request": "launch", "mainClass": "org.springblade.Application", "projectName": "BladeX-Boot", "args": "", "vmArgs": "-Dfile.encoding=UTF-8 -Dspring.profiles.active=dev -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005", "console": "internalConsole", "stopOnEntry": false, "cwd": "${workspaceFolder}", "env": {"JAVA_TOOL_OPTIONS": "-Dfile.encoding=UTF-8"}, "preLaunchTask": ""}, {"type": "java", "name": "Launch Current File", "request": "launch", "mainClass": "${file}", "console": "internalConsole", "stopOnEntry": false, "cwd": "${workspaceFolder}", "env": {"JAVA_TOOL_OPTIONS": "-Dfile.encoding=UTF-8"}}]}