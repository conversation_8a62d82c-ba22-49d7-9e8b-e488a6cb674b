package org.springblade.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import lombok.extern.slf4j.Slf4j;

/**
 * JSON工具类，提供JSON相关的工具方法
 *
 * <AUTHOR> AI
 */
@Slf4j
public class JsonUtils {

    /**
     * 创建配置了蛇形命名策略的ObjectMapper实例
     * 用于将Java对象序列化为下划线分隔的JSON字符串（例如：userName -> user_name）
     *
     * @return 配置了蛇形命名策略的ObjectMapper实例
     */
    public static ObjectMapper createSnakeCaseMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
        return objectMapper;
    }

    /**
     * 将对象转换为蛇形命名（下划线分隔）的JSON字符串
     * 例如：将{"userName":"test"} 转换为 {"user_name":"test"}
     *
     * @param object 要转换的对象
     * @return 转换后的JSON字符串
     * @throws JsonProcessingException 如果转换过程中发生错误
     */
    public static String toSnakeCaseJson(Object object) throws JsonProcessingException {
        return createSnakeCaseMapper().writeValueAsString(object);
    }

    /**
     * 将对象转换为蛇形命名（下划线分隔）的JSON字符串
     * 与toSnakeCaseJson方法不同，此方法会捕获异常并返回null
     *
     * @param object 要转换的对象
     * @return 转换后的JSON字符串，转换失败时返回null
     */
    public static String toSnakeCaseJsonSafe(Object object) {
        try {
            return toSnakeCaseJson(object);
        } catch (JsonProcessingException e) {
            log.error("将对象转换为蛇形命名JSON字符串失败", e);
            return null;
        }
    }
}