package org.springblade.common.constant;

public class ChannelName {

    public static final String INBOUND = "inbound";
    public static final String DEFAULT = "default";
    public static final String OUTBOUND = "outbound";


    // osd
    public static final String INBOUND_OSD = "inboundOsd";

    public static final String INBOUND_OSD_RC = "inboundOsdRc";

    public static final String INBOUND_OSD_DOCK = "inboundOsdDock";

    public static final String INBOUND_OSD_RC_DRONE = "inboundOsdRcDrone";

    public static final String INBOUND_OSD_DOCK_DRONE = "inboundOsdDockDrone";


    // events
    public static final String INBOUND_EVENTS = "inboundEvents";

    public static final String OUTBOUND_EVENTS = "outboundEvents";

    public static final String INBOUND_EVENTS_FLIGHTTASK_PROGRESS = "inboundEventsFlighttaskProgress";

    public static final String INBOUND_EVENTS_FILE_UPLOAD_CALLBACK = "inboundEventsFileUploadCallback";
}
