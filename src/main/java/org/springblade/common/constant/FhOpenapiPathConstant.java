package org.springblade.common.constant;

public interface FhOpenapiPathConstant {
    String SYSTEM_STATUS = "/openapi/v0.1/system_status"; //系统状态

    //----------------------------------组织项目---------------------------
    String PROJECTS = "/openapi/v0.1/project"; //获取组织下的项目列表
    String DEVICES = "/openapi/v0.1/device"; //获取组织下的设备列表
    String PROJECT_DEVICES = "/openapi/v0.1/project/device"; //获取项目下的设备列表
    String PROJECT_STS_TOKEN = "/openapi/v0.1/project/sts-token"; //获取项目的存储上传凭证
    String PROJECT_MEMBER = "/openapi/v0.1/project/member"; //添加人员到项目
    //----------------------------------组织项目---------------------------


    //----------------------------------设备管理---------------------------
    String DEVICE_STATE = "/openapi/v0.1/device/%s/state"; //物模型获取
    String DEVICE_HMS = "/openapi/v0.1/device/hms"; //HMS信息获取
    String DEVICE_COMMAND = "/openapi/v0.1/device/%s/command"; //实时控制指令下发
    String DEVICE_CHANGE_CAMERA = "/openapi/v0.1/device/change-camera"; //机场相机切换
    String DEVICE_CHANGE_LENS = "/openapi/v0.1/device/change-lens"; //飞行器镜头切换
    String DEVICE_CONTROL = "/openapi/v0.1/device/control"; //控制权
    String DEVICE_STREAM_QUALITY = "/openapi/v0.1/device/stream/quality"; //图传清晰度设置
    String DEVICE_SN_RTK = "/openapi/v0.1/device/%s/rtk"; //自定义网络RTK标定

    //----------------------------------设备管理---------------------------


    //----------------------------------直播管理---------------------------
    String LIVE_STREAM_START = "/openapi/v0.1/live-stream/start"; //开始直播
    String LIVE_STREAM_CONVERTER = "/openapi/v0.1/live-stream/converter"; //转码器
    String LIVE_STREAM_CONVERTER_CHANGE = LIVE_STREAM_CONVERTER + "/%s"; //转码器操作
    String LIVE_STREAM_CONVERTER_STATUS = "/openapi/v0.1/live-stream/converter/%s"; //码流转发器状态控制
    String LIVE_STREAM_CONVERTER_LIST = "/openapi/v0.1/live-stream/converter"; //获取码流转发器列表

    //----------------------------------直播管理---------------------------


    //----------------------------------任务管理---------------------------
    String FLIGHT_TASK = "/openapi/v0.1/flight-task"; //飞行任务
    String FLIGHT_TASK_STATUS = "/openapi/v0.1/flight-task/%s/status"; //更新飞行任务状态
    String FLIGHT_TASK_DETAIL = FLIGHT_TASK + "/%s"; //获取飞行任务信息
    String FLIGHT_TASK_LIST = "/openapi/v0.1/flight-task/list"; //获取飞行任务列表
    String FLIGHT_TASK_MEDIA = "/openapi/v0.1/flight-task/%s/media"; //获取飞行任务产生的媒体资源
    String FLIGHT_TASK_TRACK = "/openapi/v0.1/flight-task/%s/track"; //获取飞行任务轨迹信息
    //----------------------------------任务管理---------------------------

    //----------------------------------航线管理---------------------------
    String WAYLINE_FINISH_UPLOAD = "/openapi/v0.1/wayline/finish-upload"; //航线上传完成通知
    String WAYLINES = "/openapi/v0.1/wayline"; //获取项目下的航线列表
    String WAYLINE_DETAIL = "/openapi/v0.1/wayline/%s"; //获取航线详情

    //----------------------------------航线管理---------------------------

    //----------------------------------模型管理---------------------------
    String MODELS = "/openapi/v0.1/model"; //获取项目下的模型列表
    String MODEL_CREATE = "/openapi/v0.1/model/create"; //模型重建
    String MODEL_DETAIL = "/openapi/v0.1/model/%s"; //获取模型详情

    //----------------------------------模型管理---------------------------

    //----------------------------------标注管理---------------------------
    String MAP_ELEMENT = "/openapi/v0.1/map/element"; //创建地图标注
    String MAP_V09_BASE = "/openapi/v0.9/map/api/v1/workspaces/{proj_uuid}"; //v0.9版本地图API基础路径
    String MAP_ELEMENT_V09 = MAP_V09_BASE + "/element-groups/{group_id}/elements"; //创建地图标注(v0.9)
    String MAP_ELEMENT_DELETE = MAP_V09_BASE + "/elements/{id}"; //删除地图标注(v0.9)
    String MAP_ELEMENT_FOLDERS = MAP_V09_BASE + "/element-groups"; //获取标注文件夹列表
    String MAP_ELEMENT_FOLDER_CREATE = MAP_V09_BASE + "/element-groups"; //创建标注文件夹
	//----------------------------------标注管理---------------------------

}
