
package org.springblade.modules.auth.granter;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.exception.AuthException;
import me.zhyd.oauth.model.AuthToken;
import me.zhyd.oauth.utils.HttpUtils;
import me.zhyd.oauth.utils.UrlBuilder;
import org.springblade.core.oauth2.exception.OAuth2ErrorCode;
import org.springblade.core.oauth2.granter.AbstractTokenGranter;
import org.springblade.core.oauth2.handler.PasswordHandler;
import org.springblade.core.oauth2.provider.OAuth2Request;
import org.springblade.core.oauth2.service.OAuth2ClientService;
import org.springblade.core.oauth2.service.OAuth2User;
import org.springblade.core.oauth2.service.OAuth2UserService;
import org.springblade.core.oauth2.utils.OAuth2ExceptionUtil;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.utils.DigestUtil;
import org.springblade.modules.auth.utils.TokenUtil;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.pojo.entity.UserInfo;
import org.springblade.modules.system.pojo.entity.WechatApplet;
import org.springblade.modules.system.service.IUserService;
import org.springblade.modules.system.service.IWechatAppletService;
import org.springframework.stereotype.Component;

import java.util.Optional;


/**
 * SocialTokenGranter
 *
 * <AUTHOR>
 */
@Component
public class WechatAppletTokenGranter extends AbstractTokenGranter {
	private static final String JSCODE2SESSION_URL = "https://api.weixin.qq.com/sns/jscode2session";
	private static final String TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token";
	private static final String PHONE_URL = "https://api.weixin.qq.com/wxa/business/getuserphonenumber";
	private static final String TOKEN_CACHE = "wechat-applet:token:";
	private static final Integer AUTH_SUCCESS_CODE = 2000;

	private final IUserService userService;
	private final BladeRedis bladeRedis;
	private final IWechatAppletService wechatAppletService;


	public WechatAppletTokenGranter(OAuth2ClientService clientService,
									OAuth2UserService oAuth2UserService,
									PasswordHandler passwordHandler,
									IUserService userService,
									BladeRedis bladeRedis,
									IWechatAppletService wechatAppletService) {
		super(clientService, oAuth2UserService, passwordHandler);
		this.userService = userService;
		this.bladeRedis = bladeRedis;
		this.wechatAppletService = wechatAppletService;
	}

	@Override
	public String type() {
		return WECHAT_APPLET;
	}

	@Override
	public OAuth2User user(OAuth2Request request) {
		String tenantId = request.getTenantId();
		//AuthToken authToken = getAuthToken(builderJscode2sessionUrl("wxc1a6df0b2ef5bb29", "11792f0f9af12d8bb23074788a23edc2", request.getCode()));

		Optional<WechatApplet> optional = getWechatApplet(request.getClientId(), request.getClientSecret());
		if (optional.isEmpty()){
			OAuth2ExceptionUtil.throwFromCode(OAuth2ErrorCode.INVALID_CLIENT);
		}
		WechatApplet wechatApplet = optional.get();

		AuthToken authToken = getAccessToken(wechatApplet.getAppId(), wechatApplet.getSecret());

		String phone = getPhone(authToken.getAccessToken(), request.getCode());

		UserInfo userInfo = userService.userInfoByPhone(tenantId, phone);
		if (userInfo == null){
			// 用户注册
			User newUser = new User();
			// 设置基本信息
			newUser.setAccount(phone);
			newUser.setPhone(phone);
			newUser.setName("微信用户");
			newUser.setRealName("微信用户");
			newUser.setTenantId(tenantId);

			newUser.setUserType(3);
			newUser.setRoleId("1909076107069083652");

			// 密码使用HTLJ+手机号码加密（与Excel导入用户一致）
			String passwordPrefix = "HTLJ";
			String passwordToEncrypt = passwordPrefix + phone;
			newUser.setPassword(DigestUtil.encrypt(passwordToEncrypt));

			// 保存用户
			userService.save(newUser);

			// 重新查询用户信息
			userInfo = userService.userInfoByPhone(tenantId, phone);
		}
		//todo 其他校验

		// 设置Oauth2用户信息
		OAuth2User user = TokenUtil.convertUser(userInfo, request);
		// 设置客户端信息
		user.setClient(client(request));
		return user;
	}

	private Optional<WechatApplet> getWechatApplet(String clientId, String clientSecret){
		return Optional.ofNullable(wechatAppletService.getOne(Wrappers.lambdaQuery(WechatApplet.class)
			.eq(WechatApplet::getClientId,clientId)
			.eq(WechatApplet::getClientSecret,clientSecret)
			.last("limit 1")));
	}

	private String builderJscode2sessionUrl(String appid,String secret ,String code){
		return UrlBuilder.fromBaseUrl(JSCODE2SESSION_URL)
			.queryParam("appid", appid)
			.queryParam("secret", secret)
			.queryParam("js_code", code)
			.queryParam("grant_type", "authorization_code")
			.build();

	}

	private AuthToken getAuthToken(String url){
		String response = (new HttpUtils(new AuthConfig().getHttpConfig())).get(url).getBody();
		JSONObject object = JSONObject.parseObject(response);
		this.checkResponse(object);
		return AuthToken.builder()
			.openId(object.getString("openid"))
			.unionId(object.getString("unionid"))
			.accessToken(object.getString("session_key"))
			.build();
	}

	private AuthToken getAccessToken(String appid,String secret){
		String key = TOKEN_CACHE + appid + ":" + secret;

		AuthToken t = bladeRedis.get(key);
		if (t!=null){
			return t;
		}

		String url = UrlBuilder.fromBaseUrl(TOKEN_URL)
			.queryParam("grant_type", "client_credential")
			.queryParam("appid", appid)
			.queryParam("secret", secret)
			.build();

		String response = (new HttpUtils(new AuthConfig().getHttpConfig())).get(url).getBody();
		JSONObject object = JSONObject.parseObject(response);
		this.checkResponse(object);

		t = AuthToken.builder()
			.accessToken(object.getString("access_token"))
			.expireIn(object.getIntValue("expires_in"))
			.build();

		bladeRedis.setEx(key,t,3600L);

		return t;
	}

	private String getPhone(String accessToken,String code){
		String url = UrlBuilder.fromBaseUrl(PHONE_URL)
			.queryParam("access_token", accessToken)
			.build();

		JSONObject requestObj = new JSONObject();
		requestObj.put("code",code);

		String response = (new HttpUtils(new AuthConfig().getHttpConfig())).post(url, requestObj.toJSONString()).getBody();

		JSONObject object = JSONObject.parseObject(response);
		//this.checkResponse(object);

		int errcode = object.getIntValue("errcode");
		if (errcode != 0){
			throw new AuthException(errcode, object.getString("errmsg"));
		}

		return object
			.getJSONObject("phone_info")
			.getString("purePhoneNumber");

	}

	private void checkResponse(JSONObject object) {
		if (object.containsKey("errcode")) {
			throw new AuthException(object.getIntValue("errcode"), object.getString("errmsg"));
		}
	}

}
