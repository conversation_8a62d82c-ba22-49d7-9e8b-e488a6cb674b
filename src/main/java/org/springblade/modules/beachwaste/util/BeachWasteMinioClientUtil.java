package org.springblade.modules.beachwaste.util;

import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.BucketExistsArgs;
import io.minio.MakeBucketArgs;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.errors.ErrorResponseException;
import io.minio.http.Method;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.config.BeachWasteMinioConfiguration;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.concurrent.TimeUnit;

/**
 * 海滩垃圾模块MinIO客户端工具类
 * 提供MinIO客户端实例和相关配置
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class BeachWasteMinioClientUtil {

    /**
     * 预签名URL过期时间（小时）
     */
    public static final Integer EXPIRY = 12;

	/**
	 * -- GETTER --
	 *  获取配置信息
	 *
	 * @return 配置实例
	 */
	@Getter
	private final BeachWasteMinioConfiguration config;
    private MinioClient minioClient;

    public BeachWasteMinioClientUtil(BeachWasteMinioConfiguration config) {
        this.config = config;
    }

    /**
     * 获取MinIO客户端实例
     * 使用单例模式，确保客户端复用
     *
     * @return MinIO客户端实例
     */
    public MinioClient getMinioClient() {
        if (minioClient == null) {
            synchronized (this) {
                if (minioClient == null) {
                    try {
                        minioClient = MinioClient.builder()
                                .endpoint(config.getEndpoint())
                                .credentials(config.getAccessKey(), config.getSecretKey())
                                .build();
                        log.info("海滩垃圾模块MinIO客户端初始化成功，端点: {}", config.getEndpoint());
                    } catch (Exception e) {
                        log.error("海滩垃圾模块MinIO客户端初始化失败: {}", e.getMessage(), e);
                        throw new RuntimeException("MinIO客户端初始化失败", e);
                    }
                }
            }
        }
        return minioClient;
    }

    /**
     * 获取存储桶名称
     *
     * @return 存储桶名称
     */
    public String getBucketName() {
        return config.getBucketName();
    }

	/**
     * 获取对象URL
     *
     * @param objectKey 对象键
     * @return 预签名URL
     */
    public String getObjectUrl(String objectKey) {
        if (objectKey == null || objectKey.trim().isEmpty()) {
            return null;
        }
        try {
            MinioClient client = getMinioClient();
            String bucket = getBucketName();

            return client.getPresignedObjectUrl(
                GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(bucket)
                    .object(objectKey)
                    .expiry(EXPIRY, TimeUnit.HOURS)
                    .build());
        } catch (Exception e) {
            log.error("获取对象URL失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 上传文件到MinIO
     *
     * @param bucketName  存储桶名称
     * @param objectKey   对象键（文件路径）
     * @param inputStream 文件输入流
     * @param contentType 文件内容类型
     * @return 上传是否成功
     */
    public boolean uploadFile(String bucketName, String objectKey, InputStream inputStream, String contentType) {
        try {
            MinioClient client = getMinioClient();

            // 检查存储桶是否存在，如果不存在则创建
            if (!client.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build())) {
                log.info("存储桶 [{}] 不存在，正在创建...", bucketName);
                client.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                log.info("存储桶 [{}] 创建成功", bucketName);
            }

            log.debug("开始上传文件到存储桶 [{}]，对象键: [{}]", bucketName, objectKey);

            // 上传文件
            client.putObject(
                PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectKey)
                    .stream(inputStream, -1, 10485760)
                    .contentType(contentType)
                    .build()
            );

            log.info("文件上传成功，存储桶: [{}]，对象键: [{}]", bucketName, objectKey);
            return true;

        } catch (ErrorResponseException e) {
            log.error("MinIO服务器响应错误 - 错误代码: [{}], 错误消息: [{}], 存储桶: [{}], 对象键: [{}]",
                    e.errorResponse().code(), e.errorResponse().message(), bucketName, objectKey, e);
            return false;
        } catch (Exception e) {
            log.error("文件上传失败，存储桶: [{}], 对象键: [{}], 错误信息: [{}]", bucketName, objectKey, e.getMessage(), e);
            return false;
        }
    }

}
