package org.springblade.modules.beachwaste.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.pojo.entity.SpatialGrid;
import org.springblade.modules.beachwaste.service.ISpatialGridService;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 空间网格专用校验工具类
 * 负责空间网格相关的数据校验逻辑
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SpatialGridValidationUtil {

    private static ISpatialGridService spatialGridService;
    private static IUserService userService;

    @Autowired
    public void setSpatialGridService(ISpatialGridService spatialGridService) {
        SpatialGridValidationUtil.spatialGridService = spatialGridService;
    }

    @Autowired
    public void setUserService(IUserService userService) {
        SpatialGridValidationUtil.userService = userService;
    }

    /**
     * 校验网格基础数据
     * 
     * @param grid 空间网格对象
     * @return 校验结果消息，null表示校验通过
     */
    public static String validateGridBasicData(SpatialGrid grid) {
        if (grid == null) {
            return "网格数据不能为空";
        }

        // 校验必填字段
        if (grid.getGridCode() == null || grid.getGridCode().trim().isEmpty()) {
            return "网格编码不能为空";
        }
        if (grid.getGridName() == null || grid.getGridName().trim().isEmpty()) {
            return "网格名称不能为空";
        }
        if (grid.getUserId() == null) {
            return "管理员ID不能为空";
        }

        return null;
    }

    /**
     * 校验网格数据唯一性（新增时使用）
     * 
     * @param gridCode 网格编码
     * @param gridName 网格名称
     * @return 校验结果消息，null表示校验通过
     */
    public static String validateGridUniqueness(String gridCode, String gridName) {
        if (!StringUtils.hasText(gridCode) || !StringUtils.hasText(gridName)) {
            return "网格编码和网格名称不能为空";
        }

        // 检查网格编码和网格名称是否重复
        Long count = spatialGridService.getBaseMapper().selectCount(
            new LambdaQueryWrapper<SpatialGrid>()
                .eq(SpatialGrid::getIsDeleted, 0)
                .and(wrapper -> wrapper
                    .eq(SpatialGrid::getGridCode, gridCode)
                    .or()
                    .eq(SpatialGrid::getGridName, gridName)
                )
        );
        
        if (count > 0) {
            return "网格编码或网格名称已存在，请重新输入";
        }

        return null;
    }

    /**
     * 校验网格数据唯一性（更新时使用）
     * 
     * @param gridId 当前网格ID
     * @param gridCode 网格编码
     * @param gridName 网格名称
     * @return 校验结果消息，null表示校验通过
     */
    public static String validateGridUniquenessForUpdate(Long gridId, String gridCode, String gridName) {
        if (gridId == null) {
            return "网格ID不能为空";
        }
        if (!StringUtils.hasText(gridCode) || !StringUtils.hasText(gridName)) {
            return "网格编码和网格名称不能为空";
        }

        // 检查网格编码和网格名称是否重复（排除当前记录）
        Long count = spatialGridService.getBaseMapper().selectCount(
            new LambdaQueryWrapper<SpatialGrid>()
                .eq(SpatialGrid::getIsDeleted, 0)
                .ne(SpatialGrid::getId, gridId)
                .and(wrapper -> wrapper
                    .eq(SpatialGrid::getGridCode, gridCode)
                    .or()
                    .eq(SpatialGrid::getGridName, gridName)
                )
        );
        
        if (count > 0) {
            return "网格编码或网格名称已存在，请重新输入";
        }

        return null;
    }

    /**
     * 校验管理员是否存在
     * 
     * @param userId 管理员ID
     * @return 校验结果消息，null表示校验通过
     */
    public static String validateInspectorExists(Long userId) {
        if (userId == null) {
            return "管理员ID不能为空";
        }

        User user = userService.getById(userId);
        if (user == null) {
            return "指定的管理员不存在";
        }

        return null;
    }

    /**
     * 校验KML文件
     * 
     * @param file KML文件
     * @return 校验结果消息，null表示校验通过
     */
    public static String validateKmlFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return "请选择要上传的KML文件";
        }

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".kml")) {
            return "请上传KML格式的文件";
        }

        // 检查文件大小（限制为10MB）
        long maxSize = 10 * 1024 * 1024; // 10MB
        if (file.getSize() > maxSize) {
            return "文件大小不能超过10MB";
        }

        return null;
    }

    /**
     * 批量校验网格数据
     * 
     * @param gridList 网格数据列表
     * @return 校验结果消息，null表示校验通过
     */
    public static String validateGridListForBatch(List<SpatialGrid> gridList) {
        if (gridList == null || gridList.isEmpty()) {
            return "网格数据列表不能为空";
        }

        // 校验每个网格的基础数据
        for (int i = 0; i < gridList.size(); i++) {
            SpatialGrid grid = gridList.get(i);
            String basicValidationResult = validateGridBasicData(grid);
            if (basicValidationResult != null) {
                return String.format("第%d条数据校验失败：%s", i + 1, basicValidationResult);
            }

            // 校验管理员是否存在
            String inspectorValidationResult = validateInspectorExists(grid.getUserId());
            if (inspectorValidationResult != null) {
                return String.format("第%d条数据校验失败：%s", i + 1, inspectorValidationResult);
            }
        }

        return null;
    }

    /**
     * 校验网格是否存在
     * 
     * @param gridId 网格ID
     * @return 校验结果消息，null表示校验通过
     */
    public static String validateGridExists(Long gridId) {
        if (gridId == null) {
            return "网格ID不能为空";
        }

        SpatialGrid grid = spatialGridService.getById(gridId);
        if (grid == null) {
            return "指定的网格不存在";
        }

        return null;
    }

    /**
     * 校验年月格式
     * 
     * @param yearMonth 年月字符串（格式：yyyy-MM）
     * @return 校验结果消息，null表示校验通过
     */
    public static String validateYearMonthFormat(String yearMonth) {
        if (!StringUtils.hasText(yearMonth)) {
            return "年月参数不能为空";
        }

        try {
            // 尝试解析年月格式
            java.time.YearMonth.parse(yearMonth);
            return null;
        } catch (Exception e) {
            return "年月格式不正确，请使用yyyy-MM格式";
        }
    }
}