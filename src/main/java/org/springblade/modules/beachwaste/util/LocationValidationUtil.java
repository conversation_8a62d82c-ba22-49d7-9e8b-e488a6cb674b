package org.springblade.modules.beachwaste.util;

import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.modules.system.service.ISystemConfigService;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 位置验证工具类
 * 用于验证事件处置时的位置距离
 *
 * <AUTHOR>
 */
@Slf4j
public class LocationValidationUtil {

    /**
     * 验证距离是否在允许范围内
     * @param eventLocation 事件位置字符串
     * @param currentLongitude 当前经度
     * @param currentLatitude 当前纬度
     * @param systemConfigService 系统配置服务
     * @return 是否在允许范围内
     */
    public static boolean validateDistance(String eventLocation,
                                         BigDecimal currentLongitude,
                                         BigDecimal currentLatitude,
                                         ISystemConfigService systemConfigService) {
        try {
            // 获取距离限制配置（默认100米）
            double maxDistance = getMaxAllowedDistance(systemConfigService);

            // 解析事件位置
            double[] eventCoords = DistanceUtil.parseLocation(eventLocation);
            double eventLon = eventCoords[0];
            double eventLat = eventCoords[1];

            // 计算距离
            double distance = DistanceUtil.calculateDistance(
                eventLat, eventLon,
                currentLatitude.doubleValue(), currentLongitude.doubleValue()
            );

            log.debug("事件位置: [{}, {}], 当前位置: [{}, {}], 距离: {}米, 限制: {}米",
                     eventLat, eventLon, currentLatitude, currentLongitude, distance, maxDistance);

            return distance <= maxDistance;

        } catch (Exception e) {
            log.error("距离验证失败", e);
            return false;
        }
    }

    /**
     * 获取最大允许距离配置
     * @param systemConfigService 系统配置服务
     * @return 最大允许距离（米）
     */
    private static double getMaxAllowedDistance(ISystemConfigService systemConfigService) {
        try {
            R<Map<String, String>> configResult = systemConfigService.getGlobalConfigMap();
            if (configResult.isSuccess() && configResult.getData() != null) {
                String maxDistanceStr = configResult.getData().get("electronicFenceRadius");
                if (StringUtils.hasText(maxDistanceStr)) {
                    return Double.parseDouble(maxDistanceStr);
                }
            }
        } catch (Exception e) {
            log.warn("获取距离配置失败，使用默认值", e);
        }

        // 默认100米
        return 100.0;
    }
}
