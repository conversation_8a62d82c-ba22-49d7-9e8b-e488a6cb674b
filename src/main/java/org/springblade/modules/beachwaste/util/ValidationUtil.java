package org.springblade.modules.beachwaste.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springblade.modules.beachwaste.pojo.entity.SpatialGrid;
import org.springblade.modules.beachwaste.service.ISpatialGridService;
import org.springblade.modules.system.pojo.entity.Role;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IRoleService;
import org.springblade.modules.system.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 网格数据存储校验工具类
 * <AUTHOR>
 */
@Component
public class ValidationUtil {

    private static IUserService userService;
    private static IRoleService roleService;
	private static ISpatialGridService spatialGridService;

	@Autowired
	public void setUserService(IUserService userService) {
		ValidationUtil.userService = userService;
	}
	@Autowired
	public void setRoleService(IRoleService roleService) {
		ValidationUtil.roleService = roleService;
	}
	@Autowired
	public void setSpatialGridService(ISpatialGridService spatialGridService) {
		ValidationUtil.spatialGridService = spatialGridService;
	}

	/**
	 * 校验网格数据存储信息：网格名称，网格编号，网格归属管理员
	 * @param id 管理员id
	 * @param gridCode 网格编码
	 * @param gridName 网格名称
	 * @return true：校验通过，false：校验失败
	 */
    public static boolean checkBase(Long id, String gridCode, String gridName) {
        // 1. 查询用户信息
        User userInfo = userService.getById(id);
        if (userInfo == null) {
            throw new RuntimeException("关联用户不存在");
        }

        // 2. 查询网格编码和网格名称是否重复
        Long count = spatialGridService.getBaseMapper().selectCount(
            new LambdaQueryWrapper<SpatialGrid>()
				.eq(SpatialGrid::getIsDeleted,0)
                .and(wrapper -> wrapper
                    .eq(SpatialGrid::getGridCode, gridCode)
                    .or()
                    .eq(SpatialGrid::getGridName, gridName)
                )
        );
		return extracted(count, userInfo);
	}

	private static boolean extracted(Long count, User userInfo) {
		if (count > 0) {
			throw new RuntimeException("网格编码/网格名称存在重复，请确认");
		}

		// 3. 校验管理员角色
		return roleService.count(new LambdaQueryWrapper<Role>()
			.eq(Role::getIsDeleted, 0)
			.eq(Role::getParentId, 0)
			.eq(Role::getRoleAlias, "网格管理员")
			.eq(Role::getId, Long.parseLong(userInfo.getRoleId()))) > 1;
	}

	/**
     * 更新时校验网格数据存储信息：网格名称，网格编号，网格归属管理员
     * @param gridId 当前网格ID
     * @param id 管理员id
     * @param gridCode 网格编码
     * @param gridName 网格名称
     * @return true：校验通过，false：校验失败
     */
    public static boolean checkBaseForUpdate(Long gridId, Long id, String gridCode, String gridName) {
        // 1. 查询用户信息
        User userInfo = userService.getById(id);
        if (userInfo == null) {
            throw new RuntimeException("关联用户不存在");
        }

        // 2. 查询网格编码和网格名称是否重复（排除当前记录）
        Long count = spatialGridService.getBaseMapper().selectCount(
            new LambdaQueryWrapper<SpatialGrid>()
				.eq(SpatialGrid::getIsDeleted,0)
                .ne(SpatialGrid::getId, gridId)
                .and(wrapper -> wrapper
                    .eq(SpatialGrid::getGridCode, gridCode)
                    .or()
                    .eq(SpatialGrid::getGridName, gridName)
                )
        );
		return extracted(count, userInfo);
	}

}
