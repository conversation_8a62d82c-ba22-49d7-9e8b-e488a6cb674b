package org.springblade.modules.beachwaste.util;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.springblade.modules.beachwaste.config.ThumbnailConfig;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URL;
import java.util.Arrays;
import java.util.List;

/**
 * 图片处理工具类
 * 提供图片裁剪、缩略图生成等功能
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class ImageProcessingUtil {

    private final ThumbnailConfig thumbnailConfig;

    /**
     * 根据bbox坐标裁剪图片并生成缩略图
     *
     * @param imageUrl 原始图片URL
     * @param bboxStr  bbox坐标字符串，格式："x1,y1,x2,y2"
     * @return 裁剪后的缩略图字节数组
     */
    public byte[] cropAndCreateThumbnail(String imageUrl, String bboxStr) {
        if (!StringUtils.hasText(imageUrl) || !StringUtils.hasText(bboxStr)) {
            log.warn("图片URL或bbox坐标为空，无法处理");
            return null;
        }

        try {
            // 解析bbox坐标
            BboxCoordinates bbox = parseBboxCoordinates(bboxStr);
            if (bbox == null) {
                log.warn("bbox坐标解析失败: {}", bboxStr);
                return null;
            }

            // 从URL加载图片
            BufferedImage originalImage = loadImageFromUrl(imageUrl);
            if (originalImage == null) {
                log.warn("无法从URL加载图片: {}", imageUrl);
                return null;
            }

            // 验证和调整bbox坐标
            bbox = adjustBboxCoordinates(bbox, originalImage.getWidth(), originalImage.getHeight());
            if (bbox == null) {
                log.warn("bbox坐标超出图片范围");
                return null;
            }

            // 计算放大后的裁剪区域
            BboxCoordinates expandedBbox = expandBbox(bbox, originalImage.getWidth(), originalImage.getHeight());

            // 裁剪图片并绘制框选效果
            BufferedImage croppedImage = cropImageWithBoundingBox(originalImage, bbox, expandedBbox);
            if (croppedImage == null) {
                log.warn("图片裁剪失败");
                return null;
            }

            // 生成缩略图
            return createThumbnail(croppedImage);

        } catch (Exception e) {
            log.error("图片处理失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解析bbox坐标字符串
     * 支持多种格式："x1,y1,x2,y2" 或 "[x1, y1, x2, y2]" 或 "[x1,y1,x2,y2]"
     *
     * @param bboxStr bbox坐标字符串
     * @return bbox坐标对象
     */
    private BboxCoordinates parseBboxCoordinates(String bboxStr) {
        try {
            // 清理输入字符串，移除方括号和多余的空格
            String cleanedStr = bboxStr.trim()
                    .replaceAll("[\\[\\]]", "")
                    .replaceAll("\s+", "");
            
            String[] parts = cleanedStr.split(",");
            if (parts.length != 4) {
                log.warn("bbox坐标格式错误，应为4个数值: {}", bboxStr);
                return null;
            }

            List<Integer> coordinates = Arrays.stream(parts)
                    .map(String::trim)
                    .map(s -> {
                        try {
                            // 支持整数和浮点数格式
                            return (int) Math.round(Double.parseDouble(s));
                        } catch (NumberFormatException e) {
                            log.warn("bbox坐标解析失败: {}", s);
                            return null;
                        }
                    })
                    .toList();

            if (coordinates.contains(null)) {
                log.warn("bbox坐标包含无效数值: {}", bboxStr);
                return null;
            }

            int x1 = coordinates.get(0);
            int y1 = coordinates.get(1);
            int x2 = coordinates.get(2);
            int y2 = coordinates.get(3);
            
            // 验证坐标的合理性
            if (x1 >= x2 || y1 >= y2) {
                log.warn("bbox坐标不合理，x1应小于x2，y1应小于y2: x1={}, y1={}, x2={}, y2={}", x1, y1, x2, y2);
                return null;
            }
            
            if (x1 < 0 || y1 < 0) {
                log.warn("bbox坐标不能为负数: x1={}, y1={}, x2={}, y2={}", x1, y1, x2, y2);
                return null;
            }

            return new BboxCoordinates(x1, y1, x2, y2);
        } catch (Exception e) {
            log.error("解析bbox坐标时发生异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从URL加载图片
     *
     * @param imageUrl 图片URL
     * @return BufferedImage对象
     */
    private BufferedImage loadImageFromUrl(String imageUrl) {
        try {
            URL url = new URL(imageUrl);
            return ImageIO.read(url);
        } catch (Exception e) {
            log.error("从URL加载图片失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 验证和调整bbox坐标
     *
     * @param bbox        原始bbox坐标
     * @param imageWidth  图片宽度
     * @param imageHeight 图片高度
     * @return 调整后的bbox坐标
     */
    private BboxCoordinates adjustBboxCoordinates(BboxCoordinates bbox, int imageWidth, int imageHeight) {
        // 确保坐标在图片范围内
        int x1 = Math.max(0, Math.min(bbox.x1, imageWidth - 1));
        int y1 = Math.max(0, Math.min(bbox.y1, imageHeight - 1));
        int x2 = Math.max(x1 + 1, Math.min(bbox.x2, imageWidth));
        int y2 = Math.max(y1 + 1, Math.min(bbox.y2, imageHeight));

        // 确保x2 > x1 且 y2 > y1
        if (x2 <= x1 || y2 <= y1) {
            log.warn("bbox坐标无效: x1={}, y1={}, x2={}, y2={}", x1, y1, x2, y2);
            return null;
        }

        return new BboxCoordinates(x1, y1, x2, y2);
    }

    /**
     * 扩展bbox区域以适当放大，并确保4:3比例
     *
     * @param bbox        原始bbox坐标
     * @param imageWidth  图片宽度
     * @param imageHeight 图片高度
     * @return 扩展后的bbox坐标
     */
    private BboxCoordinates expandBbox(BboxCoordinates bbox, int imageWidth, int imageHeight) {
        int bboxWidth = bbox.x2 - bbox.x1;
        int bboxHeight = bbox.y2 - bbox.y1;
        
        // 增加扩展系数，提供更多周围环境信息
        double expandFactor = Math.max(thumbnailConfig.getScaleFactor(), 2.0); // 至少2倍扩展
        
        // 计算初始扩展量
        int expandX = (int) (bboxWidth * (expandFactor - 1) / 2);
        int expandY = (int) (bboxHeight * (expandFactor - 1) / 2);
        
        // 计算初始扩展后的区域
        int tempX1 = Math.max(0, bbox.x1 - expandX);
        int tempY1 = Math.max(0, bbox.y1 - expandY);
        int tempX2 = Math.min(imageWidth, bbox.x2 + expandX);
        int tempY2 = Math.min(imageHeight, bbox.y2 + expandY);
        
        int tempWidth = tempX2 - tempX1;
        int tempHeight = tempY2 - tempY1;
        
        // 调整为4:3比例
        int targetWidth, targetHeight;
        double currentRatio = (double) tempWidth / tempHeight;
        double targetRatio = 4.0 / 3.0;
        
        if (currentRatio > targetRatio) {
            // 当前比例过宽，以高度为准调整宽度
            targetHeight = tempHeight;
            targetWidth = (int) (targetHeight * targetRatio);
        } else {
            // 当前比例过高，以宽度为准调整高度
            targetWidth = tempWidth;
            targetHeight = (int) (targetWidth / targetRatio);
        }
        
        // 计算中心点
        int centerX = (tempX1 + tempX2) / 2;
        int centerY = (tempY1 + tempY2) / 2;
        
        // 以中心点为基准，计算最终坐标
        int finalX1 = Math.max(0, centerX - targetWidth / 2);
        int finalY1 = Math.max(0, centerY - targetHeight / 2);
        int finalX2 = Math.min(imageWidth, finalX1 + targetWidth);
        int finalY2 = Math.min(imageHeight, finalY1 + targetHeight);
        
        // 如果触及边界，需要重新调整
        if (finalX2 == imageWidth) {
            finalX1 = Math.max(0, finalX2 - targetWidth);
        }
        if (finalY2 == imageHeight) {
            finalY1 = Math.max(0, finalY2 - targetHeight);
        }
        
        log.debug("bbox扩展: 原始({},{},{},{}) -> 扩展({},{},{},{}), 比例: {}:1", 
                 bbox.x1, bbox.y1, bbox.x2, bbox.y2,
                 finalX1, finalY1, finalX2, finalY2,
                 String.format("%.2f", (double)(finalX2-finalX1)/(finalY2-finalY1)));
        
        return new BboxCoordinates(finalX1, finalY1, finalX2, finalY2);
    }

    /**
     * 裁剪图片并绘制边界框
     *
     * @param originalImage 原始图片
     * @param originalBbox  原始bbox坐标
     * @param expandedBbox  扩展后的裁剪区域坐标
     * @return 裁剪后带框选效果的图片
     */
    private BufferedImage cropImageWithBoundingBox(BufferedImage originalImage, BboxCoordinates originalBbox, BboxCoordinates expandedBbox) {
        try {
            int width = expandedBbox.x2 - expandedBbox.x1;
            int height = expandedBbox.y2 - expandedBbox.y1;

            // 裁剪图片
            BufferedImage croppedImage = originalImage.getSubimage(expandedBbox.x1, expandedBbox.y1, width, height);
            
            // 创建一个新的图片用于绘制框选效果
            BufferedImage resultImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = null;
            
            try {
                g2d = resultImage.createGraphics();
                
                // 绘制裁剪后的原图
                g2d.drawImage(croppedImage, 0, 0, null);
                
                // 设置抗锯齿
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                
                // 绘制红色框选区域（相对于裁剪后图片的坐标）
                drawBoundingBox(g2d, originalBbox, expandedBbox);
                
                log.debug("边界框绘制完成，环境类型: {}", 
                         GraphicsEnvironment.isHeadless() ? "无头环境" : "图形环境");
                
                return resultImage;
                
            } catch (Exception drawException) {
                log.warn("边界框绘制失败，返回原始裁剪图片: {}", drawException.getMessage());
                // 如果绘制失败，返回原始裁剪图片
                return croppedImage;
            } finally {
                if (g2d != null) {
                    g2d.dispose();
                }
            }
            
        } catch (Exception e) {
            log.error("图片裁剪失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 绘制边界框
     *
     * @param g2d          Graphics2D对象
     * @param originalBbox 原始bbox坐标
     * @param expandedBbox 扩展后的bbox坐标
     */
    private void drawBoundingBox(Graphics2D g2d, BboxCoordinates originalBbox, BboxCoordinates expandedBbox) {
        // 计算原始bbox在裁剪后图片中的相对位置
        int relativeX1 = originalBbox.x1 - expandedBbox.x1;
        int relativeY1 = originalBbox.y1 - expandedBbox.y1;
        int relativeX2 = originalBbox.x2 - expandedBbox.x1;
        int relativeY2 = originalBbox.y2 - expandedBbox.y1;
        
        int boxWidth = relativeX2 - relativeX1;
        int boxHeight = relativeY2 - relativeY1;
        
        // 添加调试日志
        log.debug("绘制边界框 - 原始bbox: ({},{},{},{}), 扩展bbox: ({},{},{},{})", 
                 originalBbox.x1, originalBbox.y1, originalBbox.x2, originalBbox.y2,
                 expandedBbox.x1, expandedBbox.y1, expandedBbox.x2, expandedBbox.y2);
        log.debug("相对位置: ({},{}) 尺寸: {}x{}", relativeX1, relativeY1, boxWidth, boxHeight);
        
        // 确保坐标有效
        if (boxWidth <= 0 || boxHeight <= 0) {
            log.warn("边界框尺寸无效: {}x{}", boxWidth, boxHeight);
            return;
        }
        
        // 设置更明显的红色边框样式
        g2d.setColor(Color.RED); // 使用红色更容易看到
        g2d.setStroke(new BasicStroke(5.0f, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND)); // 5像素宽度的边框
        
        // 绘制矩形边框
        g2d.drawRect(relativeX1, relativeY1, boxWidth, boxHeight);
        
        // 添加半透明红色填充效果
        g2d.setColor(new Color(255, 0, 0, 50)); // 半透明红色
        g2d.fillRect(relativeX1, relativeY1, boxWidth, boxHeight);
        
        log.debug("边界框绘制完成");
    }

    /**
     * 生成缩略图
     *
     * @param croppedImage 裁剪后的图片
     * @return 缩略图字节数组
     */
    private byte[] createThumbnail(BufferedImage croppedImage) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            Thumbnails.of(croppedImage)
                    .size(thumbnailConfig.getWidth(), thumbnailConfig.getHeight())
                    .outputFormat("jpg")
                    .outputQuality(thumbnailConfig.getQuality())
                    .toOutputStream(outputStream);

            return outputStream.toByteArray();
        } catch (IOException e) {
            log.error("生成缩略图失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * bbox坐标内部类
     */
    private static class BboxCoordinates {
        final int x1, y1, x2, y2;

        BboxCoordinates(int x1, int y1, int x2, int y2) {
            this.x1 = x1;
            this.y1 = y1;
            this.x2 = x2;
            this.y2 = y2;
        }
    }
}
