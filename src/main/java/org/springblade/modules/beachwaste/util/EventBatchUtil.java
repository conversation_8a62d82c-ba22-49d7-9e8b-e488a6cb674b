package org.springblade.modules.beachwaste.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.mapper.BeachLitterMediaMapper;
import org.springblade.modules.beachwaste.pojo.entity.BeachLitterMedia;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springblade.modules.beachwaste.pojo.entity.SpatialGrid;
import org.springblade.modules.beachwaste.service.ISpatialGridService;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 事件批量处理工具类，提供批量数据操作相关的公共方法
 * <p>
 * 该工具类主要用于优化事件数据的批量处理，避免N+1查询问题，
 * 提供网格信息、用户信息和媒体信息的批量获取功能。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class EventBatchUtil {

    /**
     * 默认分页大小
     */
    private static final int DEFAULT_PAGE_SIZE = 500;

    /**
     * 最大分页数量限制
     */
    private static final int MAX_PAGES = 10000;

    /**
     * 默认批处理大小
     */
    private static final int DEFAULT_BATCH_SIZE = 1000;

    /**
     * 默认集合初始容量
     */
    private static final int DEFAULT_INITIAL_CAPACITY = 16;

    /**
     * 未知网格名称
     */
    private static final String UNKNOWN_GRID_NAME = "未知网格";

    /**
     * 未分配网格名称
     */
    private static final String UNASSIGNED_GRID_NAME = "未分配网格";

    /**
     * 未知网格员名称
     */
    private static final String UNKNOWN_STAFF_NAME = "未知网格员";

    /**
     * 未分配处理人员名称
     */
    private static final String UNASSIGNED_STAFF_NAME = "未分配";

    /**
     * 批量数据映射类，用于存储网格、用户和媒体信息的映射
     * <p>
     * 该类封装了事件处理过程中需要的关联数据映射，包括：
     * <ul>
     * <li>网格ID到网格名称的映射</li>
     * <li>用户ID到用户姓名的映射</li>
     * <li>任务UUID到媒体信息的映射</li>
     * </ul>
     * </p>
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @Getter
    public static class BatchDataMaps {
        /**
         * 网格ID到网格名称的映射
         */
        private final Map<Long, String> gridNameMap;

        /**
         * 用户ID到用户姓名的映射
         */
        private final Map<Long, String> userNameMap;

        /**
         * 任务UUID到媒体信息的映射
         */
        private final Map<String, BeachLitterMedia> mediaMap;

        /**
         * 构造函数，创建包含网格、用户和媒体信息映射的批量数据映射对象
         *
         * @param gridNameMap 网格ID到网格名称的映射
         * @param userNameMap 用户ID到用户姓名的映射
         * @param mediaMap    任务UUID到媒体信息的映射
         */
        public BatchDataMaps(Map<Long, String> gridNameMap, Map<Long, String> userNameMap, Map<String, BeachLitterMedia> mediaMap) {
            this.gridNameMap = gridNameMap != null ? gridNameMap : new HashMap<>(DEFAULT_INITIAL_CAPACITY);
            this.userNameMap = userNameMap != null ? userNameMap : new HashMap<>(DEFAULT_INITIAL_CAPACITY);
            this.mediaMap = mediaMap != null ? mediaMap : new HashMap<>(DEFAULT_INITIAL_CAPACITY);
        }

        /**
         * 构造函数，创建包含网格和用户信息映射的批量数据映射对象（向后兼容）
         *
         * @param gridNameMap 网格ID到网格名称的映射
         * @param userNameMap 用户ID到用户姓名的映射
         */
        public BatchDataMaps(Map<Long, String> gridNameMap, Map<Long, String> userNameMap) {
            this.gridNameMap = gridNameMap != null ? gridNameMap : new HashMap<>(DEFAULT_INITIAL_CAPACITY);
            this.userNameMap = userNameMap != null ? userNameMap : new HashMap<>(DEFAULT_INITIAL_CAPACITY);
            this.mediaMap = new HashMap<>(DEFAULT_INITIAL_CAPACITY);
        }
    }

    /**
     * 批量获取关联数据映射（网格、用户和媒体信息）
     * <p>
     * 该方法通过批量查询的方式获取事件相关的网格信息、用户信息和媒体信息，
     * 避免在循环中进行单个查询导致的N+1查询问题，提高查询效率。
     * </p>
     *
     * @param eventList              事件列表，不能为null
     * @param spatialGridService     网格服务，用于批量获取网格信息
     * @param userService            用户服务，用于批量获取用户信息
     * @param beachLitterMediaMapper 媒体数据映射器，用于批量获取媒体信息
     * @return 批量数据映射对象，包含网格、用户和媒体信息的映射关系
     * @throws IllegalArgumentException 当eventList为null时抛出
     */
    public static BatchDataMaps getBatchDataMaps(List<Event> eventList,
                                                  ISpatialGridService spatialGridService,
                                                  IUserService userService,
                                                  BeachLitterMediaMapper beachLitterMediaMapper) {
        if (eventList == null) {
            throw new IllegalArgumentException("事件列表不能为null");
        }

        Map<Long, String> gridNameMap = fetchGridNameMap(eventList, spatialGridService);
        Map<Long, String> userNameMap = fetchUserNameMap(eventList, userService);
        Map<String, BeachLitterMedia> mediaMap = fetchMediaMap(eventList, beachLitterMediaMapper);

        return new BatchDataMaps(gridNameMap, userNameMap, mediaMap);
    }

    /**
     * 批量获取网格名称映射
     *
     * @param eventList          事件列表
     * @param spatialGridService 网格服务
     * @return 网格ID到网格名称的映射
     */
    private static Map<Long, String> fetchGridNameMap(List<Event> eventList, ISpatialGridService spatialGridService) {
        try {
            Set<Long> gridIds = eventList.stream()
                .map(Event::getGridId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            if (gridIds.isEmpty()) {
                return new HashMap<>(DEFAULT_INITIAL_CAPACITY);
            }

            List<SpatialGrid> grids = spatialGridService.listByIds(gridIds);
            if (grids == null || grids.isEmpty()) {
                return new HashMap<>(DEFAULT_INITIAL_CAPACITY);
            }

            return grids.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                    SpatialGrid::getId,
                    SpatialGrid::getGridName,
                    (existing, replacement) -> existing,
                    () -> new HashMap<>(gridIds.size())
                ));
        } catch (Exception e) {
            log.warn("批量获取网格信息失败，将使用空映射", e);
            return new HashMap<>(DEFAULT_INITIAL_CAPACITY);
        }
    }

    /**
     * 批量获取用户名称映射
     *
     * @param eventList   事件列表
     * @param userService 用户服务
     * @return 用户ID到用户姓名的映射
     */
    private static Map<Long, String> fetchUserNameMap(List<Event> eventList, IUserService userService) {
        try {
            Set<Long> userIds = eventList.stream()
                .map(Event::getHandlerStaffId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            if (userIds.isEmpty()) {
                return new HashMap<>(DEFAULT_INITIAL_CAPACITY);
            }

            List<User> users = userService.listByIds(userIds);
            if (users == null || users.isEmpty()) {
                return new HashMap<>(DEFAULT_INITIAL_CAPACITY);
            }

            return users.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                    User::getId,
                    User::getRealName,
                    (existing, replacement) -> existing,
                    () -> new HashMap<>(userIds.size())
                ));
        } catch (Exception e) {
            log.warn("批量获取用户信息失败，将使用空映射", e);
            return new HashMap<>(DEFAULT_INITIAL_CAPACITY);
        }
    }

    /**
     * 批量获取媒体信息映射
     *
     * @param eventList              事件列表
     * @param beachLitterMediaMapper 媒体数据映射器
     * @return 任务UUID到媒体信息的映射
     */
    private static Map<String, BeachLitterMedia> fetchMediaMap(List<Event> eventList, BeachLitterMediaMapper beachLitterMediaMapper) {
        try {
            Set<String> taskUuids = eventList.stream()
                .map(Event::getDiscoveryImagePath)
                .filter(StringUtils::hasText)
                .collect(Collectors.toSet());

            if (taskUuids.isEmpty()) {
                return new HashMap<>(DEFAULT_INITIAL_CAPACITY);
            }

            LambdaQueryWrapper<BeachLitterMedia> mediaQueryWrapper = new LambdaQueryWrapper<BeachLitterMedia>()
                .in(BeachLitterMedia::getTaskUuid, taskUuids)
                .isNotNull(BeachLitterMedia::getTaskUuid);

            List<BeachLitterMedia> mediaList = beachLitterMediaMapper.selectList(mediaQueryWrapper);
            if (mediaList == null || mediaList.isEmpty()) {
                return new HashMap<>(DEFAULT_INITIAL_CAPACITY);
            }

            return mediaList.stream()
                .filter(Objects::nonNull)
                .filter(media -> StringUtils.hasText(media.getTaskUuid()))
                .collect(Collectors.toMap(
                    BeachLitterMedia::getTaskUuid,
                    media -> media,
                    (existing, replacement) -> existing,
                    () -> new HashMap<>(taskUuids.size())
                ));
        } catch (Exception e) {
            log.error("批量获取媒体信息失败，将使用空映射", e);
            return new HashMap<>(DEFAULT_INITIAL_CAPACITY);
        }
    }

    /**
     * 批量获取关联数据映射（网格和用户信息）- 向后兼容方法
     * <p>
     * 该方法为向后兼容而保留，仅获取网格信息和用户信息的映射，
     * 不包含媒体信息。建议使用包含媒体信息的重载方法以获得更好的性能。
     * </p>
     *
     * @param eventList          事件列表，不能为null
     * @param spatialGridService 网格服务，用于批量获取网格信息
     * @param userService        用户服务，用于批量获取用户信息
     * @return 批量数据映射对象，包含网格和用户信息的映射关系
     * @throws IllegalArgumentException 当eventList为null时抛出
     */
    public static BatchDataMaps getBatchDataMaps(List<Event> eventList,
                                                  ISpatialGridService spatialGridService,
                                                  IUserService userService) {
        if (eventList == null) {
            throw new IllegalArgumentException("事件列表不能为null");
        }

        Map<Long, String> gridNameMap = fetchGridNameMap(eventList, spatialGridService);
        Map<Long, String> userNameMap = fetchUserNameMap(eventList, userService);

        return new BatchDataMaps(gridNameMap, userNameMap);
    }

    /**
     * 使用分页查询处理大量数据
     * <p>
     * 该方法通过分页的方式处理大量事件数据，避免一次性加载过多数据导致内存溢出。
     * 支持自定义页大小和数据处理器，适用于各种批量数据处理场景。
     * </p>
     *
     * @param queryWrapper 查询条件，不能为null
     * @param eventService 事件服务，用于执行分页查询
     * @param processor    数据处理器，用于处理每页的数据
     * @param pageSize     每页大小，必须大于0
     * @param <T>          处理结果的类型
     * @return 所有页面处理结果的汇总列表
     * @throws IllegalArgumentException 当参数不合法时抛出
     */
    public static <T> List<T> processWithPagination(LambdaQueryWrapper<Event> queryWrapper,
                                                     IService<Event> eventService,
                                                     BatchDataProcessor<T> processor,
                                                     int pageSize) {
        if (queryWrapper == null) {
            throw new IllegalArgumentException("查询条件不能为null");
        }
        if (eventService == null) {
            throw new IllegalArgumentException("事件服务不能为null");
        }
        if (processor == null) {
            throw new IllegalArgumentException("数据处理器不能为null");
        }
        if (pageSize <= 0) {
            throw new IllegalArgumentException("页大小必须大于0");
        }

        List<T> resultList = new ArrayList<>();
        int currentPage = 1;

        log.info("开始分页查询，页大小: {}", pageSize);

        while (currentPage <= MAX_PAGES) {
            Page<Event> page = new Page<>(currentPage, pageSize);
            Page<Event> eventPage = eventService.page(page, queryWrapper);
            List<Event> eventList = eventPage.getRecords();

            log.debug("第{}页查询结果: {}条记录", currentPage, eventList.size());

            // 如果当前页没有数据，说明已经查询完毕
            if (eventList.isEmpty()) {
                log.info("第{}页无数据，查询结束", currentPage);
                break;
            }

            // 处理当前页数据
            List<T> pageResults = processor.process(eventList);
            if (pageResults != null && !pageResults.isEmpty()) {
                resultList.addAll(pageResults);
                log.debug("第{}页处理完成，处理结果: {}条", currentPage, pageResults.size());
            }

            // 如果当前页返回的记录数小于页大小，说明已经是最后一页
            if (eventList.size() < pageSize) {
                log.info("第{}页记录数({})小于页大小({})，查询结束", currentPage, eventList.size(), pageSize);
                break;
            }

            currentPage++;
        }

        if (currentPage > MAX_PAGES) {
            log.warn("分页查询达到最大页数限制: {}", MAX_PAGES);
        }

        log.info("分页查询完成，总共查询{}页，处理结果: {}条", currentPage - 1, resultList.size());
        return resultList;
    }

    /**
     * 使用分页查询处理大量数据（使用默认页大小）
     * <p>
     * 该方法是 {@link #processWithPagination(LambdaQueryWrapper, IService, BatchDataProcessor, int)} 的便捷重载方法，
     * 使用默认的页大小进行分页查询处理。
     * </p>
     *
     * @param queryWrapper 查询条件，不能为null
     * @param eventService 事件服务，用于执行分页查询
     * @param processor    数据处理器，用于处理每页的数据
     * @param <T>          处理结果的类型
     * @return 所有页面处理结果的汇总列表
     * @throws IllegalArgumentException 当参数不合法时抛出
     * @see #processWithPagination(LambdaQueryWrapper, IService, BatchDataProcessor, int)
     */
    public static <T> List<T> processWithPagination(LambdaQueryWrapper<Event> queryWrapper,
                                                     IService<Event> eventService,
                                                     BatchDataProcessor<T> processor) {
        return processWithPagination(queryWrapper, eventService, processor, DEFAULT_PAGE_SIZE);
    }

    /**
     * 批量数据处理器接口
     * <p>
     * 该函数式接口用于定义事件数据的批量处理逻辑，
     * 配合分页查询方法使用，实现对大量事件数据的高效处理。
     * </p>
     *
     * @param <T> 处理结果的类型
     * <AUTHOR>
     * @since 1.0.0
     */
    @FunctionalInterface
    public interface BatchDataProcessor<T> {
        /**
         * 处理事件列表
         * <p>
         * 该方法接收一个事件列表，对其进行处理并返回处理结果。
         * 实现类应确保处理逻辑的线程安全性和异常处理。
         * </p>
         *
         * @param eventList 待处理的事件列表，不会为null但可能为空
         * @return 处理结果列表，可以为null或空列表
         */
        List<T> process(List<Event> eventList);
    }

    /**
     * 获取网格名称
     * <p>
     * 根据网格ID从映射中获取对应的网格名称，
     * 如果网格ID为null则返回未分配网格，如果映射中不存在则返回未知网格。
     * </p>
     *
     * @param gridId      网格ID，可以为null
     * @param gridNameMap 网格名称映射，不能为null
     * @return 网格名称，不会为null
     * @throws IllegalArgumentException 当gridNameMap为null时抛出
     */
    public static String getGridName(Long gridId, Map<Long, String> gridNameMap) {
        if (gridNameMap == null) {
            throw new IllegalArgumentException("网格名称映射不能为null");
        }

        if (gridId != null) {
            return gridNameMap.getOrDefault(gridId, UNKNOWN_GRID_NAME);
        } else {
            return UNASSIGNED_GRID_NAME;
        }
    }

    /**
     * 获取处理人员姓名
     * <p>
     * 根据处理人员ID从映射中获取对应的用户姓名，
     * 如果处理人员ID为null则返回未分配，如果映射中不存在则返回未知网格员。
     * </p>
     *
     * @param handlerStaffId 处理人员ID，可以为null
     * @param userNameMap    用户名称映射，不能为null
     * @return 处理人员姓名，不会为null
     * @throws IllegalArgumentException 当userNameMap为null时抛出
     */
    public static String getHandlerStaffName(Long handlerStaffId, Map<Long, String> userNameMap) {
        if (userNameMap == null) {
            throw new IllegalArgumentException("用户名称映射不能为null");
        }

        if (handlerStaffId != null) {
            return userNameMap.getOrDefault(handlerStaffId, UNKNOWN_STAFF_NAME);
        } else {
            return UNASSIGNED_STAFF_NAME;
        }
    }

}
