package org.springblade.modules.beachwaste.util;

import java.math.BigDecimal;

/**
 * 距离计算工具类
 *
 * <AUTHOR>
 */
public class DistanceUtil {

    /**
     * 地球半径（米）
     */
    private static final double EARTH_RADIUS = 6371000;

    /**
     * 计算两个经纬度点之间的距离（米）
     * 使用Haversine公式计算球面距离
     *
     * @param lat1 第一个点的纬度
     * @param lon1 第一个点的经度
     * @param lat2 第二个点的纬度
     * @param lon2 第二个点的经度
     * @return 距离（米）
     */
    public static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        // 将角度转换为弧度
        double lat1Rad = Math.toRadians(lat1);
        double lon1Rad = Math.toRadians(lon1);
        double lat2Rad = Math.toRadians(lat2);
        double lon2Rad = Math.toRadians(lon2);

        // 计算差值
        double deltaLat = lat2Rad - lat1Rad;
        double deltaLon = lon2Rad - lon1Rad;

        // Haversine公式
        double a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                   Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        // 计算距离
        return EARTH_RADIUS * c;
    }

    /**
     * 计算两个经纬度点之间的距离（米）
     * 重载方法，支持BigDecimal类型
     *
     * @param lat1 第一个点的纬度
     * @param lon1 第一个点的经度
     * @param lat2 第二个点的纬度
     * @param lon2 第二个点的经度
     * @return 距离（米）
     */
    public static double calculateDistance(BigDecimal lat1, BigDecimal lon1, BigDecimal lat2, BigDecimal lon2) {
        return calculateDistance(lat1.doubleValue(), lon1.doubleValue(), lat2.doubleValue(), lon2.doubleValue());
    }

    /**
     * 解析location字符串获取经纬度
     * location格式："经度,纬度"
     *
     * @param location 位置字符串
     * @return 经纬度数组 [经度, 纬度]
     */
    public static double[] parseLocation(String location) {
        if (location == null || location.trim().isEmpty()) {
            throw new IllegalArgumentException("位置信息不能为空");
        }
        
        String[] parts = location.split(",");
        if (parts.length != 2) {
            throw new IllegalArgumentException("位置信息格式错误，应为：经度,纬度");
        }
        
        try {
            double longitude = Double.parseDouble(parts[0].trim());
            double latitude = Double.parseDouble(parts[1].trim());
            return new double[]{longitude, latitude};
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("位置信息格式错误，经纬度必须为数字");
        }
    }

}