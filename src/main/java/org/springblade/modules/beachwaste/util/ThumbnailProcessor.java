package org.springblade.modules.beachwaste.util;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.config.ThumbnailConfig;
import org.springblade.modules.beachwaste.service.IThumbnailImageService;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 缩略图处理器
 * 提供缩略图生成和处理的通用功能
 * 将图片处理逻辑与业务逻辑分离
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class ThumbnailProcessor {

    private final ThumbnailConfig thumbnailConfig;
    private final ImageProcessingUtil imageProcessingUtil;
    private final IThumbnailImageService thumbnailImageService;

    /**
     * 处理缩略图生成的完整流程
     * 包括图片裁剪、缩略图生成、上传和URL获取
     *
     * @param originalImageUrl 原始图片URL
     * @param bboxStr         bbox坐标字符串
     * @param eventId         事件ID
     * @return 缩略图URL，如果处理失败返回null
     */
    public String processThumbnail(String originalImageUrl, String bboxStr, Long eventId) {
        try {
            // 检查缩略图功能是否启用
            if (!thumbnailConfig.isEnabled()) {
                log.debug("缩略图功能未启用，跳过处理");
                return null;
            }

            // 验证输入参数
            if (!isValidInput(originalImageUrl, bboxStr, eventId)) {
                return null;
            }

            log.debug("开始处理缩略图，事件ID: {}, bbox: {}", eventId, bboxStr);

            // 生成缩略图数据
            byte[] thumbnailData = generateThumbnailData(originalImageUrl, bboxStr, eventId);
            if (thumbnailData == null) {
                return null;
            }

            // 上传缩略图并获取URL
            return uploadAndGetThumbnailUrl(thumbnailData, eventId);

        } catch (Exception e) {
            log.error("处理缩略图时发生异常，事件ID: {}, 错误信息: {}", eventId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 验证输入参数的有效性
     *
     * @param originalImageUrl 原始图片URL
     * @param bboxStr         bbox坐标字符串
     * @param eventId         事件ID
     * @return 参数是否有效
     */
    private boolean isValidInput(String originalImageUrl, String bboxStr, Long eventId) {
        if (!StringUtils.hasText(originalImageUrl)) {
            log.debug("事件ID: {} 缺少图片路径，跳过缩略图生成", eventId);
            return false;
        }

        if (!StringUtils.hasText(bboxStr)) {
            log.debug("事件ID: {} 缺少bbox信息，跳过缩略图生成", eventId);
            return false;
        }

        if (eventId == null) {
            log.debug("事件ID为空，跳过缩略图生成");
            return false;
        }

        return true;
    }

    /**
     * 生成缩略图数据
     *
     * @param originalImageUrl 原始图片URL
     * @param bboxStr         bbox坐标字符串
     * @param eventId         事件ID
     * @return 缩略图字节数组
     */
    private byte[] generateThumbnailData(String originalImageUrl, String bboxStr, Long eventId) {
        byte[] thumbnailData = imageProcessingUtil.cropAndCreateThumbnail(originalImageUrl, bboxStr);
        if (thumbnailData == null || thumbnailData.length == 0) {
            log.warn("事件ID: {} 缩略图生成失败", eventId);
            return null;
        }
        return thumbnailData;
    }

    /**
     * 上传缩略图并获取URL
     *
     * @param thumbnailData 缩略图字节数组
     * @param eventId      事件ID
     * @return 缩略图URL
     */
    private String uploadAndGetThumbnailUrl(byte[] thumbnailData, Long eventId) {
        // 上传缩略图到MinIO
        String thumbnailObjectKey = thumbnailImageService.uploadThumbnail(thumbnailData, eventId);
        if (thumbnailObjectKey == null) {
            log.warn("事件ID: {} 缩略图上传失败", eventId);
            return null;
        }

        // 获取缩略图URL
        String thumbnailUrl = thumbnailImageService.getThumbnailUrl(thumbnailObjectKey);
        if (thumbnailUrl != null) {
            log.info("事件ID: {} 缩略图处理完成，缩略图URL: {}", eventId, thumbnailUrl);
            return thumbnailUrl;
        } else {
            log.warn("事件ID: {} 获取缩略图URL失败", eventId);
            return null;
        }
    }

    /**
     * 检查缩略图功能是否启用
     *
     * @return 是否启用
     */
    public boolean isThumbnailEnabled() {
        return thumbnailConfig.isEnabled();
    }
}