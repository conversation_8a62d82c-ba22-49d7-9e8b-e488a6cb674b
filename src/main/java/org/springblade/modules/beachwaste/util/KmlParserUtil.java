package org.springblade.modules.beachwaste.util;

import org.jetbrains.annotations.NotNull;
import org.springblade.modules.beachwaste.pojo.vo.SpatialGridInfoVo;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.util.ArrayList;
import java.util.List;

/**
 * KML文件解析工具类
 *
 * <AUTHOR>
public class KmlParserUtil {

    /**
     * 获取KML文件中的几何类型
     *
     * @param doc KML文档对象
     * @return 几何类型（Point, LineString, Polygon）
     */
    public static @NotNull String getGeomType(Document doc) {
        NodeList placemarks = doc.getElementsByTagName("Placemark");
        String geomType = "";
        if (placemarks.getLength() > 0) {
            Node placemark = placemarks.item(0);
            NodeList children = placemark.getChildNodes();
            for (int i = 0; i < children.getLength(); i++) {
                Node child = children.item(i);
                String nodeName = child.getNodeName();
                if ("MultiGeometry".equals(nodeName)) {
                    // 处理MultiGeometry节点
                    NodeList multiGeomChildren = child.getChildNodes();
                    for (int j = 0; j < multiGeomChildren.getLength(); j++) {
                        Node multiGeomChild = multiGeomChildren.item(j);
                        String multiGeomNodeName = multiGeomChild.getNodeName();
                        switch (multiGeomNodeName) {
                            case "Point":
                                return "Point";
                            case "LineString":
                                return "LineString";
                            case "Polygon":
                                return "Polygon";
                        }
                    }
                } else {
                    // 处理普通几何节点
                    switch (nodeName) {
                        case "Point":
                            return "Point";
                        case "LineString":
                            return "LineString";
                        case "Polygon":
                            return "Polygon";
                    }
                }
            }
        }
        return geomType;
    }

    /**
     * 解析KML文件中的坐标数据
     *
     * @param doc KML文档对象
     * @return 解析后的坐标数组
     */
    public static String[] parseCoordinates(Document doc) {
        // 获取坐标节点集合
        NodeList coordinatesNodes = doc.getElementsByTagName("coordinates");

        // 遍历坐标节点集合，解析坐标数据并返回
        String[] res = new String[coordinatesNodes.getLength()];
        for (int i = 0; i < coordinatesNodes.getLength(); i++) {
            Node node = coordinatesNodes.item(i);
            String coordinates = node.getTextContent().trim();
            res = coordinates.split("\\s+");
        }
        return res;
    }

    /**
     * 解析KML文件中所有Placemark的坐标数据
     *
     * @param doc KML文档对象
     * @return 解析后的多个地理要素信息列表
     */
    public static List<SpatialGridInfoVo> parseMultipleFeatures(Document doc) {
        List<SpatialGridInfoVo> result = new ArrayList<>();
        NodeList placemarks = doc.getElementsByTagName("Placemark");

        for (int i = 0; i < placemarks.getLength(); i++) {
            Node placemark = placemarks.item(i);
            NodeList children = placemark.getChildNodes();

            // 创建一个新的地理要素信息对象
            SpatialGridInfoVo gridInfo = new SpatialGridInfoVo();

            // 获取name标签内容
            String name = "";
            NodeList nameNodes = ((Element) placemark).getElementsByTagName("name");
            if (nameNodes.getLength() > 0) {
                name = nameNodes.item(0).getTextContent().trim();
            } else {
                // 如果没有name标签，尝试获取description标签内容作为备选
                NodeList descNodes = ((Element) placemark).getElementsByTagName("description");
                if (descNodes.getLength() > 0) {
                    name = descNodes.item(0).getTextContent().trim();
                }
            }
            gridInfo.setGridName(name);

            // 获取几何类型
            String geomType = "";
            for (int j = 0; j < children.getLength(); j++) {
                Node child = children.item(j);
                switch (child.getNodeName()) {
                    case "Point":
                        geomType = "Point";
                        break;
                    case "LineString":
                        geomType = "LineString";
                        break;
                    case "Polygon":
                        geomType = "Polygon";
                        break;
                }
            }
            gridInfo.setGeomType(geomType);
            // 设置对应的中文名称
            gridInfo.setGeomName(getGeomNameChinese(geomType));

            // 获取坐标数据
            NodeList coordinatesNodes = ((Element) placemark).getElementsByTagName("coordinates");
            if (coordinatesNodes.getLength() > 0) {
                Node node = coordinatesNodes.item(0);
                String coordinates = node.getTextContent().trim();
                String[] coords = coordinates.split("\\s+");
                gridInfo.setCoordinates(coords);
            }

            result.add(gridInfo);
        }

        return result;
    }

    /**
     * 验证KML文件中的地理要素数量
     *
     * @param doc KML文档对象
     * @return 地理要素数量
     */
    public static int countPlacemarks(Document doc) {
        NodeList placemarks = doc.getElementsByTagName("Placemark");
        return placemarks.getLength();
    }

    /**
     * 获取几何类型的中文名称
     *
     * @param geomType 几何类型英文名称
     * @return 几何类型中文名称
     */
    public static String getGeomNameChinese(String geomType) {
        switch (geomType) {
            case "Point":
                return "点";
            case "LineString":
                return "线";
            case "Polygon":
                return "面";
            default:
                return "未知类型";
        }
    }
}
