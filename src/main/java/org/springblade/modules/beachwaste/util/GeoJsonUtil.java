package org.springblade.modules.beachwaste.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * <AUTHOR>
public class GeoJsonUtil {
    private static final ObjectMapper mapper = new ObjectMapper()
        .setSerializationInclusion(JsonInclude.Include.NON_NULL);

    public static String createPoint(double[] coordinates) {
        ObjectNode root = mapper.createObjectNode();
        root.put("type", "Point");
        root.putPOJO("coordinates", coordinates);
        return serialize(root);
    }

    public static String createLineString(double[][] coordinates) {
        ObjectNode root = mapper.createObjectNode();
        root.put("type", "LineString");
        root.putPOJO("coordinates", coordinates);
        return serialize(root);
    }

    public static String createPolygon(double[][] coordinates) {
        ObjectNode root = mapper.createObjectNode();
        root.put("type", "Polygon");
        root.putPOJO("coordinates", new double[][][]{coordinates});
        return serialize(root);
    }

    private static String serialize(ObjectNode node) {
        try {
            return mapper.writeValueAsString(node);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("GeoJSON序列化失败", e);
        }
    }
}
