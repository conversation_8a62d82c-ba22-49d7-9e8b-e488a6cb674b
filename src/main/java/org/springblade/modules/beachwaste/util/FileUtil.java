package org.springblade.modules.beachwaste.util;

import org.springframework.web.multipart.MultipartFile;

/**
 * 文件处理工具类
 * 用于文件格式验证和相关常量定义
 *
 * <AUTHOR>
 */
public class FileUtil {

    /**
     * 处置图片存储桶名称
     */
    public static final String PROCESSED_IMAGES_BUCKET = "processed-images";
    
    /**
     * 支持的图片文件扩展名
     */
    private static final String[] SUPPORTED_IMAGE_EXTENSIONS = {
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"
    };
    
    /**
     * 支持的图片MIME类型
     */
    private static final String[] SUPPORTED_IMAGE_MIME_TYPES = {
        "image/jpeg", "image/jpg", "image/png", "image/gif", 
        "image/bmp", "image/webp"
    };
    
    /**
     * 验证文件是否为图片格式
     * @param file 文件
     * @return 是否为图片格式
     */
    public static boolean isImageFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }
        
        // 检查文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename != null) {
            String lowerCaseFilename = originalFilename.toLowerCase();
            for (String extension : SUPPORTED_IMAGE_EXTENSIONS) {
                if (lowerCaseFilename.endsWith(extension)) {
                    // 同时检查MIME类型
                    return isValidImageMimeType(file.getContentType());
                }
            }
        }
        
        return false;
    }
    
    /**
     * 验证MIME类型是否为支持的图片类型
     * @param mimeType MIME类型
     * @return 是否为支持的图片类型
     */
    private static boolean isValidImageMimeType(String mimeType) {
        if (mimeType == null) {
            return false;
        }
        
        String lowerCaseMimeType = mimeType.toLowerCase();
        for (String supportedType : SUPPORTED_IMAGE_MIME_TYPES) {
            if (lowerCaseMimeType.equals(supportedType)) {
                return true;
            }
        }
        
        return false;
    }
}