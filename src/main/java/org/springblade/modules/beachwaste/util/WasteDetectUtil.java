package org.springblade.modules.beachwaste.util;

import com.alibaba.fastjson.JSONArray;
import org.springblade.modules.beachwaste.enums.WasteSizeEnum;

/**
 * AI检测相关工具类
 *
 * <AUTHOR>
public class WasteDetectUtil {

    /**
     * 根据AI检测框计算垃圾尺寸
     * @param box AI检测框坐标数组 [x1, y1, x2, y2]
     * @return 垃圾尺寸枚举
     * @apiNote 当box为null时（通常是AI未检测到垃圾时），默认返回中块垃圾。
     * 这是因为中块垃圾（10-30cm）是海滩垃圾中最常见的尺寸，作为默认值可以减少误判。
     */
    public static WasteSizeEnum getWasteSizeByDetectBox(JSONArray box) {
        if (box == null) {
            return WasteSizeEnum.SMALL;
        }
        // 计算检测框的宽度和高度（像素）
        double width = Math.abs(box.getDouble(2) - box.getDouble(0));
        double height = Math.abs(box.getDouble(3) - box.getDouble(1));
        // 取最大值作为垃圾尺寸（像素）
        double pixelSize = Math.max(width, height);

        // 根据像素尺寸判断垃圾大小
		// 小于50像素，判定为小块垃圾
        if (pixelSize < 50) {
            return WasteSizeEnum.MICRO;
        } else if (pixelSize < 150) {
			// 50-150像素，判定为中块垃圾
            return WasteSizeEnum.SMALL;
        } else if (pixelSize < 300) {
			// 150-300像素，判定为大块垃圾
            return WasteSizeEnum.MEDIUM;
        } else {
			// 大于300像素，判定为特大块垃圾
            return WasteSizeEnum.LARGE;
        }
    }

}
