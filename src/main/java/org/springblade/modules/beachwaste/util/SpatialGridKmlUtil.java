package org.springblade.modules.beachwaste.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;
import org.springblade.modules.beachwaste.pojo.entity.SpatialGrid;
import org.springblade.modules.beachwaste.pojo.vo.SpatialGridInfoVo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 空间网格KML处理工具类
 * 负责KML文件的解析和生成相关逻辑
 *
 * <AUTHOR>
 */
@Slf4j
public class SpatialGridKmlUtil {

    /**
     * 浮点数比较的精度阈值
     */
    private static final double EPSILON = 1e-10;

    /**
     * 处理几何数据（WKT、GeoJSON和面积计算）
     *
     * @param entity      需要设置几何数据的实体对象（SpatialGrid或SpatialGridInfoVo）
     * @param geomType    几何类型
     * @param coordinates 坐标数组
     */
    public static void processGeometryData(Object entity, String geomType, String[] coordinates) {
        // 构造WKT格式数据
        String wktStr = GeometryUtil.createWkt(geomType, coordinates);

        // 构造GeoJSON格式数据
        String geoJsonStr = GeometryUtil.createGeoJson(geomType, coordinates);

        // 计算面积
        double area = GeometryUtil.calculateArea(geomType, coordinates);

        // 根据实体类型设置属性
        if (entity instanceof SpatialGrid) {
            SpatialGrid grid = (SpatialGrid) entity;
            // 将WKT字符串转换为JTS Geometry对象
            try {
                WKTReader wktReader = new WKTReader();
                Geometry geometry = wktReader.read(wktStr);
                grid.setGridGeom(geometry);
            } catch (ParseException e) {
                log.error("解析WKT字符串失败: {}", e.getMessage());
                grid.setGridGeom(null);
            }
            grid.setGeomJson(geoJsonStr);
            grid.setGridArea(BigDecimal.valueOf(area));
        } else if (entity instanceof SpatialGridInfoVo) {
            SpatialGridInfoVo info = (SpatialGridInfoVo) entity;
            info.setGridGeom(wktStr);
            info.setGeomJson(geoJsonStr);
            info.setGridArea(BigDecimal.valueOf(area));
        }
    }

    /**
     * 从WKT格式中提取坐标数组
     *
     * @param wkt WKT格式的几何数据
     * @return 坐标数组
     */
    public static String[] extractCoordinatesFromWkt(String wkt) {
        try {
            // 提取坐标部分
            int startIndex = wkt.indexOf("((");
            int endIndex = wkt.indexOf("))");

            if (startIndex >= 0 && endIndex >= 0) {
                String coordStr = wkt.substring(startIndex + 2, endIndex);
                // 分割坐标点，处理可能的空格分隔
                String[] rawCoords = coordStr.split("\\s+");

                // 将坐标转换为"x,y"格式
                List<String> formattedCoords = new ArrayList<>();
                for (int i = 0; i < rawCoords.length; i += 2) {
                    if (i + 1 < rawCoords.length) {
                        formattedCoords.add(rawCoords[i] + "," + rawCoords[i + 1]);
                    }
                }

                return formattedCoords.toArray(new String[0]);
            }
        } catch (Exception e) {
            log.error("从WKT提取坐标失败: {}", e.getMessage());
        }

        return new String[0];
    }

    /**
     * 为单个空间网格生成KML Placemark
     *
     * @param grid 空间网格对象
     * @return KML Placemark字符串
     */
    public static String generatePlacemark(SpatialGrid grid) {
        try {
            StringBuilder placemarkBuilder = new StringBuilder();
            placemarkBuilder.append("    <Placemark>\n");
            placemarkBuilder.append("      <name>").append(escapeXml(grid.getGridName())).append("</name>\n");

            // 构建描述信息
            StringBuilder description = new StringBuilder();
            description.append("<![CDATA[\n");
            description.append("        <table border=\"1\" cellpadding=\"5\">\n");
            description.append("          <tr><td><b>网格编码</b></td><td>").append(escapeXml(grid.getGridCode())).append("</td></tr>\n");
            description.append("          <tr><td><b>网格面积</b></td><td>").append(grid.getGridArea() != null ? grid.getGridArea() + " 平方米" : "未知").append("</td></tr>\n");
            description.append("          <tr><td><b>创建时间</b></td><td>").append(grid.getCreateTime() != null ? grid.getCreateTime().toString() : "未知").append("</td></tr>\n");
            description.append("        </table>\n");
            description.append("      ]]>");

            placemarkBuilder.append("      <description>").append(description.toString()).append("</description>\n");

            // 生成几何图形
            String geometry = generateGeometry(grid.getGeomJson(), grid.getGeomType());
            if (geometry != null && !geometry.trim().isEmpty()) {
                placemarkBuilder.append(geometry);
            } else {
                log.warn("网格 {} 的几何数据无效，跳过", grid.getId());
                return null;
            }

            placemarkBuilder.append("    </Placemark>\n");
            return placemarkBuilder.toString();

        } catch (Exception e) {
            log.error("生成网格 {} 的Placemark时发生错误: {}", grid.getId(), e.getMessage());
            return null;
        }
    }

    /**
     * 根据GeoJSON和几何类型生成KML几何图形
     *
     * @param geomJson GeoJSON字符串
     * @param geomType 几何类型
     * @return KML几何图形字符串
     */
    public static String generateGeometry(String geomJson, String geomType) {
        try {
            if (geomJson == null || geomJson.trim().isEmpty()) {
                return null;
            }

            // 解析GeoJSON
            ObjectMapper mapper = new ObjectMapper();
            JsonNode geoJsonNode = mapper.readTree(geomJson);

            // 检查是否为Feature对象，如果是则获取geometry节点
            JsonNode geometryNode = geoJsonNode;
            JsonNode typeNode = geoJsonNode.get("type");
            if (typeNode != null && "Feature".equals(typeNode.asText())) {
                geometryNode = geoJsonNode.get("geometry");
                if (geometryNode == null) {
                    log.warn("Feature对象中缺少geometry字段");
                    return null;
                }
            }

            JsonNode coordinatesNode = geometryNode.get("coordinates");
            if (coordinatesNode == null) {
                log.warn("几何对象中缺少coordinates字段");
                return null;
            }

            // 如果geomType为空或null，尝试从几何对象中获取type字段
            String actualGeomType = geomType;
            if (actualGeomType == null || actualGeomType.trim().isEmpty()) {
                JsonNode geomTypeNode = geometryNode.get("type");
                if (geomTypeNode != null && !geomTypeNode.isNull()) {
                    actualGeomType = geomTypeNode.asText();
                    log.info("从GeoJSON几何对象中推断几何类型: {}", actualGeomType);
                } else {
                    log.warn("无法确定几何类型，geomType为空且几何对象中无type字段");
                    return null;
                }
            }

            StringBuilder geometryBuilder = new StringBuilder();

            switch (actualGeomType) {
                case "Point":
                    geometryBuilder.append("      <Point>\n");
                    geometryBuilder.append("        <coordinates>");
                    if (coordinatesNode.isArray() && coordinatesNode.size() >= 2) {
                        double lon = coordinatesNode.get(0).asDouble();
                        double lat = coordinatesNode.get(1).asDouble();
                        geometryBuilder.append(lon).append(",").append(lat).append(",0");
                    }
                    geometryBuilder.append("</coordinates>\n");
                    geometryBuilder.append("      </Point>\n");
                    break;

                case "LineString":
                    geometryBuilder.append("      <LineString>\n");
                    geometryBuilder.append("        <coordinates>");
                    if (coordinatesNode.isArray()) {
                        for (int i = 0; i < coordinatesNode.size(); i++) {
                            JsonNode coord = coordinatesNode.get(i);
                            if (coord.isArray() && coord.size() >= 2) {
                                double lon = coord.get(0).asDouble();
                                double lat = coord.get(1).asDouble();
                                if (i > 0) geometryBuilder.append(" ");
                                geometryBuilder.append(lon).append(",").append(lat).append(",0");
                            }
                        }
                    }
                    geometryBuilder.append("</coordinates>\n");
                    geometryBuilder.append("      </LineString>\n");
                    break;

                case "Polygon":
                    geometryBuilder.append("      <Polygon>\n");
                    geometryBuilder.append("        <outerBoundaryIs>\n");
                    geometryBuilder.append("          <LinearRing>\n");
                    geometryBuilder.append("            <coordinates>");
                    if (coordinatesNode.isArray() && coordinatesNode.size() > 0) {
                        JsonNode outerRing = coordinatesNode.get(0);
                        if (outerRing.isArray()) {
                            // 提取坐标点
                            List<double[]> coordinates = new ArrayList<>();
                            for (int i = 0; i < outerRing.size(); i++) {
                                JsonNode coord = outerRing.get(i);
                                if (coord.isArray() && coord.size() >= 2) {
                                    double lon = coord.get(0).asDouble();
                                    double lat = coord.get(1).asDouble();
                                    coordinates.add(new double[]{lon, lat});
                                }
                            }

                            // 检测并修复自相交多边形
                            coordinates = fixSelfIntersectingPolygon(coordinates);

                            // 生成坐标字符串
                            for (int i = 0; i < coordinates.size(); i++) {
                                double[] coord = coordinates.get(i);
                                if (i > 0) geometryBuilder.append(" ");
                                geometryBuilder.append(coord[0]).append(",").append(coord[1]).append(",0");
                            }
                        }
                    }
                    geometryBuilder.append("</coordinates>\n");
                    geometryBuilder.append("          </LinearRing>\n");
                    geometryBuilder.append("        </outerBoundaryIs>\n");
                    geometryBuilder.append("      </Polygon>\n");
                    break;

                default:
                    log.warn("不支持的几何类型: {}", actualGeomType);
                    return null;
            }

            return geometryBuilder.toString();

        } catch (Exception e) {
            log.error("生成几何图形时发生错误: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 修复自相交多边形
     * 通过重新排序坐标点来消除自相交，生成有效的简单多边形
     *
     * @param coordinates 原始坐标点列表
     * @return 修复后的坐标点列表
     */
    private static List<double[]> fixSelfIntersectingPolygon(List<double[]> coordinates) {
        if (coordinates == null || coordinates.size() < 3) {
            return coordinates;
        }

        try {
            // 移除重复的坐标点（除了闭合点）
            List<double[]> uniqueCoords = new ArrayList<>();
            for (int i = 0; i < coordinates.size() - 1; i++) {
                double[] current = coordinates.get(i);
                boolean isDuplicate = false;
                for (double[] existing : uniqueCoords) {
                    if (Math.abs(existing[0] - current[0]) < 1e-10 && Math.abs(existing[1] - current[1]) < 1e-10) {
                        isDuplicate = true;
                        break;
                    }
                }
                if (!isDuplicate) {
                    uniqueCoords.add(current);
                }
            }

            if (uniqueCoords.size() < 3) {
                return coordinates;
            }

            // 检测是否存在自相交
            if (!hasSelfIntersection(uniqueCoords)) {
                // 如果没有自相交，确保多边形闭合并返回
                double[] first = uniqueCoords.get(0);
                double[] last = uniqueCoords.get(uniqueCoords.size() - 1);
                if (Math.abs(first[0] - last[0]) > 1e-10 || Math.abs(first[1] - last[1]) > 1e-10) {
                    uniqueCoords.add(new double[]{first[0], first[1]});
                }
                return uniqueCoords;
            }

            log.info("检测到自相交多边形，正在修复...");

            // 计算质心
            double[] centroid = calculateCentroid(uniqueCoords);

            // 按照相对于质心的角度排序坐标点
            uniqueCoords.sort((a, b) -> {
                double angleA = Math.atan2(a[1] - centroid[1], a[0] - centroid[0]);
                double angleB = Math.atan2(b[1] - centroid[1], b[0] - centroid[0]);
                return Double.compare(angleA, angleB);
            });

            // 确保多边形闭合
            double[] first = uniqueCoords.get(0);
            uniqueCoords.add(new double[]{first[0], first[1]});

            log.info("自相交多边形修复完成，坐标点数量: {}", uniqueCoords.size());
            return uniqueCoords;

        } catch (Exception e) {
            log.error("修复自相交多边形时发生错误: {}", e.getMessage());
            return coordinates;
        }
    }

    /**
     * 检测多边形是否存在自相交
     *
     * @param coordinates 坐标点列表
     * @return 是否存在自相交
     */
    private static boolean hasSelfIntersection(List<double[]> coordinates) {
        if (coordinates.size() < 4) {
            return false;
        }

        for (int i = 0; i < coordinates.size(); i++) {
            for (int j = i + 2; j < coordinates.size(); j++) {
                // 避免检查相邻边
                if (j == coordinates.size() - 1 && i == 0) {
                    continue;
                }

                double[] p1 = coordinates.get(i);
                double[] p2 = coordinates.get((i + 1) % coordinates.size());
                double[] p3 = coordinates.get(j);
                double[] p4 = coordinates.get((j + 1) % coordinates.size());

                if (doLinesIntersect(p1, p2, p3, p4)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 检测两条线段是否相交
     *
     * @param p1 线段1起点
     * @param p2 线段1终点
     * @param p3 线段2起点
     * @param p4 线段2终点
     * @return 是否相交
     */
    private static boolean doLinesIntersect(double[] p1, double[] p2, double[] p3, double[] p4) {
        double d1 = orientation(p3, p4, p1);
        double d2 = orientation(p3, p4, p2);
        double d3 = orientation(p1, p2, p3);
        double d4 = orientation(p1, p2, p4);

        // 一般情况：线段相交
        boolean line1CrossesLine2 = isOppositeOrientation(d1, d2);
        boolean line2CrossesLine1 = isOppositeOrientation(d3, d4);
        if (line1CrossesLine2 && line2CrossesLine1) {
            return true;
        }

        // 特殊情况：共线且重叠
        if (isCollinear(d1) && onSegment(p3, p1, p4)) {
            return true;
        }
        if (isCollinear(d2) && onSegment(p3, p2, p4)) {
            return true;
        }
        if (isCollinear(d3) && onSegment(p1, p3, p2)) {
            return true;
        }
        if (isCollinear(d4) && onSegment(p1, p4, p2)) {
            return true;
        }

        return false;
    }

    /**
     * 判断两个方向值是否表示相反的方向
     *
     * @param d1 方向值1
     * @param d2 方向值2
     * @return 是否相反方向
     */
    private static boolean isOppositeOrientation(double d1, double d2) {
        return (d1 > 0 && d2 < 0) || (d1 < 0 && d2 > 0);
    }

    /**
     * 判断方向值是否表示共线（接近零）
     *
     * @param orientation 方向值
     * @return 是否共线
     */
    private static boolean isCollinear(double orientation) {
        return Math.abs(orientation) < EPSILON;
    }

    /**
     * 计算三点的方向
     *
     * @param p 点p
     * @param q 点q
     * @param r 点r
     * @return 方向值（正数：逆时针，负数：顺时针，0：共线）
     */
    private static double orientation(double[] p, double[] q, double[] r) {
        return (q[1] - p[1]) * (r[0] - q[0]) - (q[0] - p[0]) * (r[1] - q[1]);
    }

    /**
     * 检查点q是否在线段pr上
     *
     * @param p 线段起点
     * @param q 待检查点
     * @param r 线段终点
     * @return 是否在线段上
     */
    private static boolean onSegment(double[] p, double[] q, double[] r) {
        return q[0] <= Math.max(p[0], r[0]) && q[0] >= Math.min(p[0], r[0]) &&
               q[1] <= Math.max(p[1], r[1]) && q[1] >= Math.min(p[1], r[1]);
    }

    /**
     * 计算坐标点的质心
     *
     * @param coordinates 坐标点列表
     * @return 质心坐标
     */
    private static double[] calculateCentroid(List<double[]> coordinates) {
        double sumX = 0, sumY = 0;
        for (double[] coord : coordinates) {
            sumX += coord[0];
            sumY += coord[1];
        }
        return new double[]{sumX / coordinates.size(), sumY / coordinates.size()};
    }

    /**
     * 生成完整的KML内容
     *
     * @param gridList 空间网格列表
     * @return 完整的KML内容字符串
     */
    public static String generateKmlContent(List<SpatialGrid> gridList) {
        try {
            if (gridList == null || gridList.isEmpty()) {
                throw new IllegalArgumentException("网格列表不能为空");
            }

            StringBuilder kmlBuilder = new StringBuilder();

            // KML文件头部
            kmlBuilder.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
            kmlBuilder.append("<kml xmlns=\"http://www.opengis.net/kml/2.2\">\n");
            kmlBuilder.append("  <Document>\n");
            kmlBuilder.append("    <name>海滩垃圾巡检网格数据</name>\n");
            kmlBuilder.append("    <description>导出的空间网格数据</description>\n");

            // 添加样式定义
            kmlBuilder.append("    <Style id=\"gridStyle\">\n");
            kmlBuilder.append("      <LineStyle>\n");
            kmlBuilder.append("        <color>ff0000ff</color>\n");
            kmlBuilder.append("        <width>2</width>\n");
            kmlBuilder.append("      </LineStyle>\n");
            kmlBuilder.append("      <PolyStyle>\n");
            kmlBuilder.append("        <color>7f00ff00</color>\n");
            kmlBuilder.append("        <fill>1</fill>\n");
            kmlBuilder.append("        <outline>1</outline>\n");
            kmlBuilder.append("      </PolyStyle>\n");
            kmlBuilder.append("    </Style>\n");

            // 生成每个网格的Placemark
            for (SpatialGrid grid : gridList) {
                String placemark = generatePlacemark(grid);
                if (placemark != null && !placemark.trim().isEmpty()) {
                    // 添加样式引用
                    placemark = placemark.replace("    <Placemark>\n",
                        "    <Placemark>\n      <styleUrl>#gridStyle</styleUrl>\n");
                    kmlBuilder.append(placemark);
                } else {
                    log.warn("跳过无效的网格数据: {}", grid.getId());
                }
            }

            // KML文件尾部
            kmlBuilder.append("  </Document>\n");
            kmlBuilder.append("</kml>\n");

            log.info("成功生成KML内容，包含 {} 个网格", gridList.size());
            return kmlBuilder.toString();

        } catch (Exception e) {
            log.error("生成KML内容时发生错误: {}", e.getMessage());
            throw new RuntimeException("生成KML内容失败: " + e.getMessage());
        }
    }

    /**
     * XML转义处理
     *
     * @param text 原始文本
     * @return 转义后的文本
     */
    private static String escapeXml(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&apos;");
    }
}
