package org.springblade.modules.beachwaste.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.pojo.dto.GridScheduleQueryDTO;
import org.springblade.modules.beachwaste.pojo.entity.GridSchedule;
import org.springblade.modules.beachwaste.pojo.entity.SpatialGrid;
import org.springblade.modules.beachwaste.pojo.vo.SpatialGridListVo;
import org.springblade.modules.beachwaste.service.IGridScheduleService;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 空间网格查询工具类
 * 负责复杂的数据查询和组装逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SpatialGridQueryUtil {

    private static IGridScheduleService gridScheduleService;
    private static IUserService userService;

    @Autowired
    public void setGridScheduleService(IGridScheduleService gridScheduleService) {
        SpatialGridQueryUtil.gridScheduleService = gridScheduleService;
    }

    @Autowired
    public void setUserService(IUserService userService) {
        SpatialGridQueryUtil.userService = userService;
    }

    /**
     * 查询今日排班信息
     *
     * @param gridIds 网格ID列表
     * @param today 当前日期
     * @return 排班信息列表
     */
    public static List<GridSchedule> queryTodaySchedules(List<Long> gridIds, LocalDate today) {
        LambdaQueryWrapper<GridSchedule> scheduleWrapper = new LambdaQueryWrapper<>();
        scheduleWrapper.eq(GridSchedule::getIsDeleted, 0)
            .eq(GridSchedule::getScheduleDate, today);

        if (!gridIds.isEmpty()) {
            scheduleWrapper.in(GridSchedule::getGridId, gridIds);
        }

        return gridScheduleService.list(scheduleWrapper);
    }

    /**
     * 查询管理员姓名信息
     *
     * @param gridList 网格列表
     * @return 管理员ID到姓名的映射
     */
    public static Map<Long, String> queryInspectorNames(List<SpatialGrid> gridList) {
        List<Long> userIds = gridList.stream()
            .map(SpatialGrid::getUserId)
            .distinct()
            .collect(Collectors.toList());

        Map<Long, String> inspectorNameMap = new HashMap<>();
        if (!userIds.isEmpty()) {
            List<User> inspectors = userService.listByIds(userIds);
            inspectorNameMap = inspectors.stream()
                .collect(Collectors.toMap(
                    User::getId,
                    User::getRealName,
                    // 如果有重复键，保留第一个值
                    (existing, replacement) -> existing
                ));
        }
        return inspectorNameMap;
    }

    /**
     * 构建网格ID到排班信息的映射
     *
     * @param allTodaySchedules 今日排班信息列表
     * @return 网格ID到排班人员姓名的映射
     */
    public static Map<Long, String> buildGridScheduleMap(List<GridSchedule> allTodaySchedules) {
        Map<Long, String> gridScheduleMap = new HashMap<>();
        Map<Long, Long> gridToStaffIdMap = new HashMap<>();

        // 先保存网格ID到员工ID的映射和初始排班信息
        for (GridSchedule schedule : allTodaySchedules) {
            gridToStaffIdMap.put(schedule.getGridId(), schedule.getStaffId());
            gridScheduleMap.put(schedule.getGridId(), schedule.getStaffName());
        }

        // 查询所有排班人员的最新信息
        List<Long> staffIds = allTodaySchedules.stream()
            .map(GridSchedule::getStaffId)
            .distinct()
            .collect(Collectors.toList());

        if (!staffIds.isEmpty()) {
            List<User> staffUsers = userService.listByIds(staffIds);
            Map<Long, String> staffNameMap = staffUsers.stream()
                .collect(Collectors.toMap(
                    User::getId,
                    User::getRealName,
                    // 如果有重复键，保留第一个值
                    (existing, replacement) -> existing
                ));

            // 使用最新的用户姓名更新排班信息
            for (Map.Entry<Long, Long> entry : gridToStaffIdMap.entrySet()) {
                Long gridId = entry.getKey();
                Long staffId = entry.getValue();
                String latestStaffName = staffNameMap.get(staffId);
                if (latestStaffName != null) {
                    // 使用最新的用户姓名替换排班表中的staffName
                    gridScheduleMap.put(gridId, latestStaffName);
                }
            }
        }

        return gridScheduleMap;
    }

    /**
     * 将网格列表转换为VO对象列表
     *
     * @param gridList 网格列表
     * @param inspectorNameMap 管理员姓名映射
     * @param gridScheduleMap 排班信息映射
     * @return VO对象列表
     */
    public static List<SpatialGridListVo> convertToGridListVo(List<SpatialGrid> gridList,
                                                              Map<Long, String> inspectorNameMap,
                                                              Map<Long, String> gridScheduleMap) {
        return gridList.stream().map(grid -> {
            SpatialGridListVo vo = new SpatialGridListVo();
            vo.setId(grid.getId());
            vo.setGridCode(grid.getGridCode());
            vo.setGridName(grid.getGridName());
            // 从网格排班映射中获取排班信息，不存在则返回空字符串
            vo.setTodaySchedule(gridScheduleMap.getOrDefault(grid.getId(), ""));
            // 从Map中获取管理员姓名，不存在则返回"-"
            vo.setInspectorName(inspectorNameMap.getOrDefault(grid.getUserId(), "-"));
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 根据排班状态筛选网格列表
     *
     * @param gridListVos 网格VO列表
     * @param scheduleStatus 排班状态
     * @return 筛选后的网格VO列表
     */
    public static List<SpatialGridListVo> filterByScheduleStatus(List<SpatialGridListVo> gridListVos, String scheduleStatus) {
        if (scheduleStatus == null || scheduleStatus.isEmpty()) {
            return gridListVos;
        }

        return gridListVos.stream().filter(vo -> {
            // 当日已排班
            if ("1".equals(scheduleStatus)) {
                return vo.getTodaySchedule() != null && !vo.getTodaySchedule().isEmpty();
            }
            // 当日未排班
            else if ("2".equals(scheduleStatus)) {
                return vo.getTodaySchedule() == null || vo.getTodaySchedule().isEmpty();
            }
            return true;
        }).collect(Collectors.toList());
    }

    /**
     * 构建KML文档头部
     *
     * @return KML文档头部字符串
     */
    public static String buildKmlHeader() {
        StringBuilder kmlBuilder = new StringBuilder();
        kmlBuilder.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
        kmlBuilder.append("<kml xmlns=\"http://www.opengis.net/kml/2.2\">\n");
        kmlBuilder.append("  <Document>\n");
        kmlBuilder.append("    <name>空间网格数据导出</name>\n");
        kmlBuilder.append("    <description>导出所有有效的空间网格数据</description>\n");
        return kmlBuilder.toString();
    }

    /**
     * 构建KML文档尾部
     *
     * @return KML文档尾部字符串
     */
    public static String buildKmlFooter() {
        StringBuilder kmlBuilder = new StringBuilder();
        kmlBuilder.append("  </Document>\n");
        kmlBuilder.append("</kml>\n");
        return kmlBuilder.toString();
    }

    /**
     * 获取网格列表数据（包含排班和管理员信息）
     *
     * @param dto 查询参数
     * @param spatialGridService 网格服务
     * @param gridScheduleService 排班服务
     * @param userService 用户服务
     * @return 网格列表VO
     */
    public static List<SpatialGridListVo> getGridListWithScheduleData(GridScheduleQueryDTO dto,
                                                                       Object spatialGridService,
                                                                       IGridScheduleService gridScheduleService,
                                                                       IUserService userService) {
        // 设置服务实例（如果还没有设置）
        if (SpatialGridQueryUtil.gridScheduleService == null) {
            SpatialGridQueryUtil.gridScheduleService = gridScheduleService;
        }
        if (SpatialGridQueryUtil.userService == null) {
            SpatialGridQueryUtil.userService = userService;
        }

        // 构建查询条件
        LambdaQueryWrapper<SpatialGrid> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpatialGrid::getIsDeleted, 0);

        // 根据网格名称模糊查询
        if (dto.getGridName() != null && !dto.getGridName().trim().isEmpty()) {
            queryWrapper.like(SpatialGrid::getGridName, dto.getGridName().trim());
        }

        // 根据管理员ID查询
        if (dto.getId() != null) {
            queryWrapper.eq(SpatialGrid::getUserId, dto.getId());
        }

        // 排序处理
        if ("0".equals(dto.getSortBy())) {
            queryWrapper.orderByAsc(SpatialGrid::getId);
        } else {
            queryWrapper.orderByDesc(SpatialGrid::getId);
        }

        // 执行查询（需要转换spatialGridService）
        @SuppressWarnings("unchecked")
        com.baomidou.mybatisplus.extension.service.IService<SpatialGrid> service =
            (com.baomidou.mybatisplus.extension.service.IService<SpatialGrid>) spatialGridService;
        List<SpatialGrid> gridList = service.list(queryWrapper);

        if (gridList.isEmpty()) {
            return java.util.Collections.emptyList();
        }

        // 获取网格ID列表
        List<Long> gridIds = gridList.stream()
            .map(SpatialGrid::getId)
            .collect(Collectors.toList());

        // 查询今日排班信息
        LocalDate today = LocalDate.now();
        List<GridSchedule> allTodaySchedules = queryTodaySchedules(gridIds, today);

        // 查询管理员姓名信息
        Map<Long, String> inspectorNameMap = queryInspectorNames(gridList);

        // 构建网格排班映射
        Map<Long, String> gridScheduleMap = buildGridScheduleMap(allTodaySchedules);

        // 转换为VO对象
        List<SpatialGridListVo> gridListVos = convertToGridListVo(gridList, inspectorNameMap, gridScheduleMap);

        // 根据排班状态筛选
        return filterByScheduleStatus(gridListVos, dto.getScheduleStatus());
    }

}
