package org.springblade.modules.beachwaste.util;

import com.vividsolutions.jts.geom.Coordinate;
import com.vividsolutions.jts.geom.GeometryFactory;
import com.vividsolutions.jts.geom.Polygon;
import com.vividsolutions.jts.io.WKTWriter;
import org.jetbrains.annotations.NotNull;

import java.util.Arrays;

/**
 * 几何计算和格式转换工具类
 *
 * <AUTHOR>
public class GeometryUtil {

    /**
     * 计算几何图形的面积
     *
     * @param geomType 几何类型
     * @param coordinates 坐标数组
     * @return 面积（平方米）
     */
    public static double calculateArea(String geomType, String[] coordinates) {
        double area = 0;
        if ("Polygon".equals(geomType)) {
            try {
                // 使用JTS库计算多边形面积
                GeometryFactory geometryFactory = new GeometryFactory();

                // 将坐标点转换为JTS坐标数组
                Coordinate[] jtsCoordinates = Arrays.stream(coordinates)
                    .map(coord -> coord.split(","))
                    .map(arr -> new Coordinate(Double.parseDouble(arr[0]), Double.parseDouble(arr[1])))
                    .toArray(Coordinate[]::new);

                // 确保多边形闭合
                if (!(jtsCoordinates[0].x == jtsCoordinates[jtsCoordinates.length - 1].x &&
                    jtsCoordinates[0].y == jtsCoordinates[jtsCoordinates.length - 1].y)) {
                    jtsCoordinates = Arrays.copyOf(jtsCoordinates, jtsCoordinates.length + 1);
                    jtsCoordinates[jtsCoordinates.length - 1] = jtsCoordinates[0];
                }

                // 创建多边形
                Polygon polygon = geometryFactory.createPolygon(jtsCoordinates);

                // 计算面积（平方米）
                area = calculateGeoArea(polygon);
            } catch (Exception e) {
                // 如果JTS计算失败，回退到原始算法
                double[] x = new double[coordinates.length];
                double[] y = new double[coordinates.length];
                for (int i = 0; i < coordinates.length; i++) {
                    String[] coords = coordinates[i].split(",");
                    x[i] = Double.parseDouble(coords[0]);
                    y[i] = Double.parseDouble(coords[1]);
                }
                for (int i = 0; i < coordinates.length - 1; i++) {
                    area += x[i] * y[i + 1] - x[i + 1] * y[i];
                }
                area += x[coordinates.length - 1] * y[0] - x[0] * y[coordinates.length - 1];
                area = Math.abs(area) / 2;
            }
        }
        return area;
    }

    /**
     * 计算地理坐标系下的多边形面积（平方米）
     * 使用Haversine公式近似计算
     *
     * @param polygon JTS多边形对象
     * @return 面积（平方米）
     */
    private static double calculateGeoArea(Polygon polygon) {
        // 地球半径（米）
        final double EARTH_RADIUS = 6371000;

        // 获取多边形的外环坐标
        Coordinate[] coordinates = polygon.getExteriorRing().getCoordinates();

        // 使用球面多边形面积公式计算
        double area = 0;
        for (int i = 0; i < coordinates.length - 1; i++) {
            double lat1 = Math.toRadians(coordinates[i].y);
            double lon1 = Math.toRadians(coordinates[i].x);
            double lat2 = Math.toRadians(coordinates[i + 1].y);
            double lon2 = Math.toRadians(coordinates[i + 1].x);

            // 计算两点之间的经度差
            double dLon = lon2 - lon1;

            // 球面多边形面积计算公式的一部分
            area += 2 * Math.atan2(
                Math.sin(dLon) * Math.cos(lat2),
                Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(dLon)
            );
        }

        // 计算最终面积（平方米）
        area = Math.abs(area) * EARTH_RADIUS * EARTH_RADIUS;
        return area;
    }

    /**
     * 构造GeoJSON格式数据
     *
     * @param geomType 几何类型
     * @param coordinates 坐标数组
     * @return GeoJSON字符串
     */
    public static @NotNull String createGeoJson(String geomType, String[] coordinates) {
        if ("Point".equals(geomType)) {
            String[] parts = coordinates[0].split(",");
            double[] coords = new double[]{Double.parseDouble(parts[0]), Double.parseDouble(parts[1])};
            return GeoJsonUtil.createPoint(coords);
        } else if ("LineString".equals(geomType)) {
            double[][] coords = new double[coordinates.length][];
            for (int i = 0; i < coordinates.length; i++) {
                String[] parts = coordinates[i].split(",");
                coords[i] = new double[]{Double.parseDouble(parts[0]), Double.parseDouble(parts[1])};
            }
            return GeoJsonUtil.createLineString(coords);
        } else if ("Polygon".equals(geomType)) {
            double[][] coords = new double[coordinates.length][];
            for (int i = 0; i < coordinates.length; i++) {
                String[] parts = coordinates[i].split(",");
                coords[i] = new double[]{Double.parseDouble(parts[0]), Double.parseDouble(parts[1])};
            }
            return GeoJsonUtil.createPolygon(coords);
        } else {
            throw new IllegalArgumentException("不支持的几何类型: " + geomType);
        }
    }

    /**
     * 构造WKT格式数据
     *
     * @param geomType 几何类型
     * @param coordinates 坐标数组
     * @return WKT字符串
     */
    public static @NotNull String createWkt(String geomType, String[] coordinates) {
        try {
            GeometryFactory geometryFactory = new GeometryFactory();
            WKTWriter writer = new WKTWriter();

            switch (geomType) {
                case "Point":
                    String[] pointCoords = coordinates[0].split(",");
                    Coordinate pointCoord = new Coordinate(
                        Double.parseDouble(pointCoords[0]),
                        Double.parseDouble(pointCoords[1])
                    );
                    return writer.write(geometryFactory.createPoint(pointCoord));

                case "LineString":
                    Coordinate[] lineCoords = Arrays.stream(coordinates)
                        .map(coord -> coord.split(","))
                        .map(arr -> new Coordinate(Double.parseDouble(arr[0]), Double.parseDouble(arr[1])))
                        .toArray(Coordinate[]::new);
                    return writer.write(geometryFactory.createLineString(lineCoords));

                case "Polygon":
                    Coordinate[] polyCoords = Arrays.stream(coordinates)
                        .map(coord -> coord.split(","))
                        .map(arr -> new Coordinate(Double.parseDouble(arr[0]), Double.parseDouble(arr[1])))
                        .toArray(Coordinate[]::new);
                    // 确保多边形闭合
                    if (!(polyCoords[0].x == polyCoords[polyCoords.length - 1].x && polyCoords[0].y == polyCoords[polyCoords.length - 1].y)) {
                        polyCoords = Arrays.copyOf(polyCoords, polyCoords.length + 1);
                        polyCoords[polyCoords.length - 1] = polyCoords[0];
                    }
                    return writer.write(geometryFactory.createPolygon(polyCoords));

                default:
                    throw new IllegalArgumentException("不支持的几何类型: " + geomType);
            }
        } catch (Exception e) {
            throw new RuntimeException("WKT生成失败: " + e.getMessage());
        }
    }
}