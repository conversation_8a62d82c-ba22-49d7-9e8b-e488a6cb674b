package org.springblade.modules.beachwaste.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 事件发现方式枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DiscoveryMethodEnum {

    /**
     * AI识别
     */
    AI(0L, "AI识别"),

    /**
     * 人工发现
     */
    MANUAL(1L, "人工发现");

    /**
     * 方式ID
     */
    private final Long id;

    /**
     * 方式名称
     */
    private final String name;

    /**
     * 根据ID获取枚举
     *
     * @param id ID
     * @return 枚举
     */
    public static DiscoveryMethodEnum getById(Long id) {
        if (id == null) {
            return null;
        }
        for (DiscoveryMethodEnum item : values()) {
            if (item.getId().equals(id)) {
                return item;
            }
        }
        return null;
    }

	/**
	 * 根据ID获取描述
	 *
	 * @param id ID
	 * @return 描述
	 */
	public static String getDesc(Long id) {
		DiscoveryMethodEnum item = getById(id);
		if (item == null) {
			return null;
		}
		return item.getName();
	}

}
