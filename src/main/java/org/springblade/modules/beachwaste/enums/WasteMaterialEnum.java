package org.springblade.modules.beachwaste.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 垃圾材质类型枚举
 * <AUTHOR>
@Getter
@AllArgsConstructor
public enum WasteMaterialEnum {

	/**
	 * 塑料类、 聚苯乙烯泡沫塑料类、玻璃类、金属类、
	 * 橡胶类、织物（布）类、木制品类、纸类和其他人造物品及无法辨识的材料
	 */
	PLASTIC(0L, "塑料类"),
    GLASS(1L, " 聚苯乙烯泡沫塑料类"),
//    METAL(2L, "玻璃类"),
    PAPER(2L, "金属类"),
    FABRIC(3L, "橡胶类"),
    RUBBER(4L, "织物（布）类"),
    WOOD(5L, "木制品类"),
    ORGANIC(6L, "纸类"),
    MIXED(7L, "其他人造物品"),
    OTHER(99L, "无法辨识的材料");

    private final Long code;
    private final String desc;

    /**
     * 根据编码获取枚举
     */
    public static WasteMaterialEnum getByCode(Long code) {
        if (code == null) {
            return null;
        }
        for (WasteMaterialEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 获取描述
     */
    public static String getDesc(Long code) {
        WasteMaterialEnum item = getByCode(code);
        return item == null ? null : item.getDesc();
    }

}
