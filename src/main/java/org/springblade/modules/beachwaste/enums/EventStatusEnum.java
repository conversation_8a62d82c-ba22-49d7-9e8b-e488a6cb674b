package org.springblade.modules.beachwaste.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 事件处理状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum EventStatusEnum {

	/**
     * 待处理
     */
    PENDING(0L, "待处理"),

    /**
     * 处理中
     */
    PROCESSING(1L, "处理中"),

    /**
     * 已处理
     */
    PROCESSED(2L, "已处理"),

    /**
     * 误报 误报包含：置信度低于预设，不通知。以及小程序上报未误报
     */
    FALSE_ALARM(3L, "误报"),

	/**
	 * 未找到
	 */
	NOT_FOUND(4L, "未找到")
	;

    /**
     * 状态ID
     */
    private final Long id;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据ID获取枚举
     *
     * @param id ID
     * @return 枚举
     */
    public static EventStatusEnum getById(Long id) {
        if (id == null) {
            return null;
        }
        for (EventStatusEnum item : values()) {
            if (item.getId().equals(id)) {
                return item;
            }
        }
        return null;
    }

	/**
	 * 根据ID获取枚举描述
	 *
	 * @param id ID
	 * @return 枚举描述
	 */
	public static String getDesc(Long id) {
		EventStatusEnum item = getById(id);
		if (item != null) {
			return item.getName();
		}
		return null;
	}

}
