package org.springblade.modules.beachwaste.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.modules.beachwaste.excel.GridScheduleImportExcel;
import org.springblade.modules.beachwaste.pojo.entity.SpatialGrid;
import org.springblade.modules.beachwaste.service.ISpatialGridService;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IRoleService;
import org.springblade.modules.system.service.IUserService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 网格排班信息导入监听器
 * 负责处理Excel导入时的数据校验逻辑
 *
 * <AUTHOR> AI
 */
@Slf4j
public class GridScheduleImportListener extends AnalysisEventListener<GridScheduleImportExcel> {

    private final IUserService userService;
    private final ISpatialGridService spatialGridService;
    private final IRoleService roleService;

	/**
	 * 缓存所有网格信息，key是网格编号，value是网格对象
 	 */
    private Map<String, SpatialGrid> gridCache;

	/**
	 * 缓存所有网格员信息，key是网格员姓名，value是网格员对象
	 */
	private Map<String, User> userCache;

	/**
	 * 记录Excel中所有出现的网格编号和网格员姓名，用于后续批量查询
	 */
	private final List<String> gridCodes = new ArrayList<>();
    private final List<String> staffNames = new ArrayList<>();

	/**
	 * 当前租户ID
	 */
	private final String tenantId;

    public GridScheduleImportListener() {
        this.userService = SpringUtil.getBean(IUserService.class);
        this.spatialGridService = SpringUtil.getBean(ISpatialGridService.class);
        this.roleService = SpringUtil.getBean(IRoleService.class);
        // 获取当前租户ID
        this.tenantId = AuthUtil.getTenantId();
    }

    /**
     * @return 数据列表
     */
    @Getter
    private final List<GridScheduleImportExcel> dataList = new ArrayList<>();

    /**
     * 解析表头后调用此方法
     * 用于在开始处理数据行之前预加载所有网格和用户数据
     *
     * @param headMap 表头映射
     * @param context 解析上下文
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        log.info("开始解析Excel，预加载所有网格和用户数据");
        // 预加载所有网格数据
        LambdaQueryWrapper<SpatialGrid> gridWrapper = new LambdaQueryWrapper<>();
        List<SpatialGrid> allGrids = spatialGridService.list(gridWrapper);
        gridCache = allGrids.stream().collect(Collectors.toMap(SpatialGrid::getGridCode, grid -> grid));

        // 获取网格员角色ID
        String gridStaffRoleIds = roleService.getRoleIdsByName(tenantId, "网格员");
        List<String> roleIdList = Arrays.stream(gridStaffRoleIds.split(",")).collect(Collectors.toList());

        // 预加载当前租户下所有网格员用户数据
        LambdaQueryWrapper<User> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.eq(User::getIsDeleted, 0)
            .eq(User::getTenantId, tenantId)
            .in(User::getRoleId, roleIdList);
        List<User> allUsers = userService.list(userWrapper);
        userCache = allUsers.stream().collect(Collectors.toMap(User::getRealName, user -> user, (k1, k2) -> k1));

        log.info("预加载完成，共加载 {} 个网格和 {} 个网格员用户", gridCache.size(), userCache.size());
    }

    /**
     * 每解析一行数据都会调用此方法
     *
     * @param data Excel行数据
     * @param context 解析上下文
     */
    @Override
    public void invoke(GridScheduleImportExcel data, AnalysisContext context) {
        // 获取当前行号
        int rowIndex = context.readRowHolder().getRowIndex();
        data.setRowIndex(rowIndex);
        try {
            // 收集所有网格编号和网格员姓名（仅用于记录，不再进行增量更新）
            if (data.getGridCode() != null && !data.getGridCode().trim().isEmpty()) {
                gridCodes.add(data.getGridCode());
            }
            if (data.getStaffName() != null && !data.getStaffName().trim().isEmpty()) {
                staffNames.add(data.getStaffName());
            }

            // 校验数据
            validateData(data);
            // 将校验通过的数据添加到列表
            dataList.add(data);
        } catch (RuntimeException e) {
            throw new RuntimeException("第 " + (rowIndex + 1) + " 行: " + e.getMessage(), e);
        }
    }

    /**
     * 所有数据解析完成后调用此方法
     *
     * @param context 解析上下文
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 校验数据间的关系（如时间段闭包关系）
        validateDataRelationships();
    }

    /**
     * 校验单条数据
     *
     * @param data 待校验的数据
     */
    private void validateData(GridScheduleImportExcel data) {
        // 检查网格编号是否为空
        if (data.getGridCode() == null || data.getGridCode().trim().isEmpty()) {
            throw new RuntimeException("网格编号不能为空");
        }

        // 检查网格编号是否存在并获取对应的gridId
        SpatialGrid grid = gridCache.get(data.getGridCode());
        if (grid == null) {
            throw new RuntimeException("网格编号 " + data.getGridCode() + " 不存在");
        }
        // 设置gridId
        data.setGridId(grid.getId());

        // 检查网格员姓名是否为空
        if (data.getStaffName() == null || data.getStaffName().trim().isEmpty()) {
            throw new RuntimeException("网格员姓名不能为空");
        }

        // 检查排班开始时间是否为空
        if (data.getStartDate() == null) {
            throw new RuntimeException("排班开始时间不能为空");
        }

        // 检查排班结束时间是否为空
        if (data.getEndDate() == null) {
            throw new RuntimeException("排班结束时间不能为空");
        }

        // 检查开始时间是否晚于结束时间
        if (data.getStartDate().isAfter(data.getEndDate())) {
            throw new RuntimeException("排班开始时间不能晚于结束时间");
        }

        // 检查网格员姓名是否存在 (网格员区分租户)
        User user = userCache.get(data.getStaffName());
        if (user == null) {
            throw new RuntimeException("网格员 " + data.getStaffName() + " 在当前租户中不存在");
        }

        // 设置网格员ID
        data.setStaffId(user.getId());
    }

    /**
     * 校验数据间的关系
     * 检查是否存在相同网格编号的排班时间段重叠
     */
    private void validateDataRelationships() {
        for (int i = 0; i < dataList.size(); i++) {
            GridScheduleImportExcel current = dataList.get(i);
            for (int j = i + 1; j < dataList.size(); j++) {
                GridScheduleImportExcel other = dataList.get(j);
                // 如果是相同网格编号，检查时间段是否存在闭包关系
                if (current.getGridCode().equals(other.getGridCode())) {
                    // 检查时间段是否有重叠
                    boolean hasOverlap = !(current.getEndDate().isBefore(other.getStartDate()) ||
                                          current.getStartDate().isAfter(other.getEndDate()));
                    if (hasOverlap) {
                        throw new RuntimeException("导入数据中存在相同网格编号的排班时间段重叠：" +
                                    "第 " + (current.getRowIndex() + 1) + " 行 (网格编号 " + current.getGridId() + "，时间段 " +
                                    current.getStartDate() + " 至 " + current.getEndDate() + ") 与 " +
                                    "第 " + (other.getRowIndex() + 1) + " 行 (时间段 " + other.getStartDate() + " 至 " + other.getEndDate() + ")");
                    }
                }
            }
        }
    }



}
