package org.springblade.modules.beachwaste.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springblade.modules.beachwaste.pojo.entity.SpatialGrid;

import java.util.Map;

/**
 * 空间网格数据访问层
 *
 * <AUTHOR> @since
 */
@Mapper
public interface SpatialGridMapper extends BaseMapper<SpatialGrid> {

	/**
	 * 获取网格基础统计数据
	 *
	 * 该方法用于收集并返回网格的基础统计数据，这些数据可能包括但不限于网格中的对象数量、
	 * 网格状态、更新时间等这些信息有助于监控网格的健康状况和性能表现
	 *
	 * @return Map<String, Object> 返回一个键值对映射，其中包含各种基础统计数据和信息
	 */
	Map<String, Object> getGridBaseStats();

}
