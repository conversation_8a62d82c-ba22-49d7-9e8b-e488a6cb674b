<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.beachwaste.mapper.SpatialGridMapper">

    <select id="getGridBaseStats" resultType="java.util.Map">
        SELECT
            COUNT(1) as grid_count,
            COALESCE(SUM(grid_area / 1000000), 0) as total_area
        FROM spatial_grid
        WHERE is_deleted = 0
    </select>

</mapper>
