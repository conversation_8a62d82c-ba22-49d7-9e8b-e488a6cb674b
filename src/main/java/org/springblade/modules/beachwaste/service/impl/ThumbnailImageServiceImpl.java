package org.springblade.modules.beachwaste.service.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.config.ThumbnailConfig;
import org.springblade.modules.beachwaste.service.IThumbnailImageService;
import org.springblade.modules.beachwaste.util.BeachWasteMinioClientUtil;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 缩略图服务实现类
 * 负责缩略图的上传、存储和管理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class ThumbnailImageServiceImpl implements IThumbnailImageService {

    private final BeachWasteMinioClientUtil beachWasteMinioClientUtil;
    private final ThumbnailConfig thumbnailConfig;

    /**
     * 缩略图文件扩展名
     */
    private static final String THUMBNAIL_EXTENSION = ".jpg";

    /**
     * 上传缩略图到MinIO
     *
     * @param thumbnailData 缩略图字节数组
     * @param eventId       事件ID
     * @return MinIO对象键，如果上传失败返回null
     */
    @Override
    public String uploadThumbnail(byte[] thumbnailData, Long eventId) {
        if (thumbnailData == null || thumbnailData.length == 0) {
            log.warn("缩略图数据为空，无法上传");
            return null;
        }

        if (eventId == null) {
            log.warn("事件ID为空，无法上传缩略图");
            return null;
        }

        try {
            // 生成唯一的对象键
            String objectKey = generateThumbnailObjectKey(eventId);
            
            // 获取存储桶名称
            String bucketName = beachWasteMinioClientUtil.getBucketName();
            
            // 上传到MinIO
            boolean uploadSuccess = beachWasteMinioClientUtil.uploadFile(
                    bucketName,
                    objectKey,
                    new ByteArrayInputStream(thumbnailData),
                    "image/jpeg"
            );

            if (uploadSuccess) {
                log.info("缩略图上传成功，事件ID: {}, 对象键: {}", eventId, objectKey);
                return objectKey;
            } else {
                log.error("缩略图上传失败，事件ID: {}", eventId);
                return null;
            }

        } catch (Exception e) {
            log.error("上传缩略图时发生异常，事件ID: {}, 错误信息: {}", eventId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取缩略图URL
     *
     * @param objectKey MinIO对象键
     * @return 缩略图URL
     */
    @Override
    public String getThumbnailUrl(String objectKey) {
        if (objectKey == null || objectKey.trim().isEmpty()) {
            return null;
        }

        try {
            return beachWasteMinioClientUtil.getObjectUrl(objectKey);
        } catch (Exception e) {
            log.error("获取缩略图URL失败，对象键: {}, 错误信息: {}", objectKey, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成缩略图对象键
     * 格式：thumbnails/yyyy/MM/dd/eventId_uuid.jpg
     *
     * @param eventId 事件ID
     * @return 对象键
     */
    private String generateThumbnailObjectKey(Long eventId) {
        if (eventId == null) {
            throw new IllegalArgumentException("事件ID不能为空");
        }

        // 获取当前日期，用于分目录存储
        LocalDateTime now = LocalDateTime.now();
        String datePath = now.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        
        // 生成唯一标识符
        String uuid = UUID.randomUUID().toString().replace("-", "");
        
        // 获取路径前缀，如果为空则使用默认值
        String pathPrefix = thumbnailConfig.getStoragePathPrefix();
        if (pathPrefix == null || pathPrefix.trim().isEmpty()) {
            pathPrefix = "thumbnails/";
        }
        
        // 确保路径前缀以"/"结尾
        if (!pathPrefix.endsWith("/")) {
            pathPrefix = pathPrefix + "/";
        }
        
        // 构建对象键
        return pathPrefix + datePath + "/" + eventId + "_" + uuid + THUMBNAIL_EXTENSION;
    }
}