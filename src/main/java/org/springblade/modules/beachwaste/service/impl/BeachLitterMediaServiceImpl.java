package org.springblade.modules.beachwaste.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.minio.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.modules.beachwaste.mapper.BeachLitterMediaMapper;
import org.springblade.modules.beachwaste.pojo.entity.BeachLitterMedia;
import org.springblade.modules.beachwaste.service.IBeachLitterMediaService;
import org.springblade.modules.beachwaste.util.BeachWasteMinioClientUtil;
import org.springblade.modules.beachwaste.util.FileUtil;
import org.springblade.modules.fh.service.IFhMinioService;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@AllArgsConstructor
public class BeachLitterMediaServiceImpl extends ServiceImpl<BeachLitterMediaMapper, BeachLitterMedia> implements IBeachLitterMediaService {

    private final IFhMinioService fhMinioService;
    private final BeachWasteMinioClientUtil beachWasteMinioClientUtil;

    @Override
    public R<String> uploadImage(MultipartFile file) {
        try {
            // 验证文件是否为空
            if (file == null || file.isEmpty()) {
                return R.fail("文件不能为空");
            }

            // 验证文件格式
            if (!FileUtil.isImageFile(file)) {
                return R.fail("只能上传图片文件（jpg、jpeg、png、gif、bmp、webp）");
            }

            // 验证文件大小（限制为10MB）
            long maxSize = 10 * 1024 * 1024;
            if (file.getSize() > maxSize) {
                return R.fail("文件大小不能超过10MB");
            }

            // 上传文件到MinIO
            R<Map<String, Object>> uploadResult = fhMinioService.uploadFile(file, FileUtil.PROCESSED_IMAGES_BUCKET);

            if (!uploadResult.isSuccess()) {
                return R.fail("文件上传失败：" + uploadResult.getMsg());
            }

            Map<String, Object> data = uploadResult.getData();
            String objectKey = (String) data.get("objectKey");

            if (objectKey == null || objectKey.isEmpty()) {
                return R.fail("文件上传失败，请重试");
            }

            return R.data(objectKey, "文件上传成功");

        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            return R.fail("文件上传失败：" + e.getMessage());
        }
    }

    @Override
    public R<Map<String, Object>> generatePresignedUrl(String fileType) {
        try {
            // 验证文件类型是否为空
            if (fileType == null || fileType.trim().isEmpty()) {
                return R.fail("文件类型不能为空");
            }

            // 验证文件类型是否支持
            if (!isValidFileType(fileType)) {
                return R.fail("只支持图片文件类型（jpg、jpeg、png、gif、bmp、webp）");
            }

            // 保留原始文件扩展名,这样下载时才能正确识别文件类型
            String fileExtension = "." + fileType.toLowerCase();

            // 获取当前日期用于分区（去掉斜杠）
            Date now = new Date();
            String datePartition = DateUtil.format(now, "yyyyMMdd");

            // 根据文件扩展名确定文件类型分类
            String fileTypeCategory = getFileTypeCategory(fileExtension);

            // 生成雪花ID确保唯一性
            String snowflakeId = String.valueOf(IdUtil.getSnowflakeNextId());

            // 生成随机UUID作为文件名，确保完全随机
            String randomFileName = IdUtil.fastSimpleUUID();

            // 构建简洁的ObjectKey格式（无斜杠）: beach-litter-{fileType}-{yyyyMMdd}-{randomFileName}-{snowflakeId}{extension}
            // 示例: beach-litter-image-20240115-a1b2c3d4e5f6-{snowflakeId}.jpg
            String objectKey = String.format("beach-litter-%s-%s-%s-%s%s",
                fileTypeCategory, datePartition, randomFileName, snowflakeId, fileExtension);


            // 生成预签名URL
            String presignedUrl = generatePresignedPutUrl(objectKey, beachWasteMinioClientUtil.getBucketName());

            if (presignedUrl == null || presignedUrl.isEmpty()) {
                return R.fail("生成预签名URL失败，请重试");
            }

            // 返回结果
            Map<String, Object> result = Map.of(
                "presignedUrl", presignedUrl,
                "objectKey", objectKey
            );

            return R.data(result, "预签名URL生成成功");

        } catch (Exception e) {
            log.error("生成预签名URL失败: {}", e.getMessage(), e);
            return R.fail("生成预签名URL失败：" + e.getMessage());
        }
    }

    /**
     * 验证文件类型是否为支持的图片格式
     * @param fileType 文件类型（不含点号，如：jpg、png等）
     * @return 是否为支持的图片格式
     */
    private boolean isValidFileType(String fileType) {
        if (fileType == null || fileType.trim().isEmpty()) {
            return false;
        }

        String lowerCaseFileType = fileType.toLowerCase();
        String[] supportedTypes = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};

        for (String type : supportedTypes) {
            if (lowerCaseFileType.equals(type)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 根据文件扩展名确定文件类型分类
     *
     * @param fileExtension 文件扩展名（包含点号）
     * @return 文件类型分类
     */
    private String getFileTypeCategory(String fileExtension) {
        if (fileExtension == null || fileExtension.isEmpty()) {
            return "unknown";
        }

        String ext = fileExtension.toLowerCase();

        // 图片文件
        if (ext.matches("\\.(jpg|jpeg|png|gif|bmp|webp|tiff|svg)$")) {
            return "image";
        }
        // 其他文件
        else {
            return "other";
        }
    }

    @Override
    public ResponseEntity<Resource> downloadFile(String objectKey) {
        InputStream inputStream = null;
        try {
            // 验证ObjectKey是否为空
            if (objectKey == null || objectKey.trim().isEmpty()) {
                log.error("ObjectKey不能为空");
                return ResponseEntity.badRequest().build();
            }

            MinioClient client = beachWasteMinioClientUtil.getMinioClient();
            String bucket = beachWasteMinioClientUtil.getBucketName();

            // 检查存储桶是否存在
            if (!client.bucketExists(BucketExistsArgs.builder().bucket(bucket).build())) {
                log.error("存储桶不存在: {}", bucket);
                return ResponseEntity.notFound().build();
            }

            // 获取文件流
            inputStream = client.getObject(GetObjectArgs.builder().bucket(bucket).object(objectKey).build());

            // 将InputStream转换为字节数组，确保完整读取文件内容
            byte[] fileBytes = IOUtils.toByteArray(inputStream);

            // 创建ByteArrayResource，避免流被提前关闭的问题
            Resource resource = new ByteArrayResource(fileBytes);

            // 从ObjectKey中提取文件名
            String fileName = extractFileName(objectKey);

            // 根据文件扩展名确定Content-Type
            MediaType mediaType = determineMediaType(fileName);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(mediaType);
            headers.setContentLength(fileBytes.length);
            headers.setContentDispositionFormData("attachment",
                URLEncoder.encode(fileName, StandardCharsets.UTF_8));
            headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");

            return ResponseEntity.ok().headers(headers).body(resource);

        } catch (Exception e) {
            log.error("文件下载失败，ObjectKey: {}, 错误: {}", objectKey, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        } finally {
            // 确保InputStream被正确关闭
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    log.warn("关闭InputStream时发生异常: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 从ObjectKey中提取文件名
     * @param objectKey 对象键
     * @return 文件名
     */
    private String extractFileName(String objectKey) {
        if (objectKey == null || objectKey.trim().isEmpty()) {
            return "download";
        }

        // 如果ObjectKey包含路径分隔符，取最后一部分作为文件名
        String fileName = objectKey;
        if (objectKey.contains("/")) {
            fileName = objectKey.substring(objectKey.lastIndexOf("/") + 1);
        }

        // 如果没有扩展名，默认添加.jpg
        if (!fileName.contains(".")) {
            fileName += ".jpg";
        }

        return fileName;
    }

    /**
     * 根据文件名确定MediaType
     * @param fileName 文件名
     * @return MediaType
     */
    private MediaType determineMediaType(String fileName) {
        if (fileName == null) {
            return MediaType.APPLICATION_OCTET_STREAM;
        }

        String lowerCaseFileName = fileName.toLowerCase();

        if (lowerCaseFileName.endsWith(".jpg") || lowerCaseFileName.endsWith(".jpeg")) {
            return MediaType.IMAGE_JPEG;
        } else if (lowerCaseFileName.endsWith(".png")) {
            return MediaType.IMAGE_PNG;
        } else if (lowerCaseFileName.endsWith(".gif")) {
            return MediaType.IMAGE_GIF;
        } else if (lowerCaseFileName.endsWith(".bmp")) {
            return MediaType.valueOf("image/bmp");
        } else if (lowerCaseFileName.endsWith(".webp")) {
            return MediaType.valueOf("image/webp");
        } else {
            return MediaType.APPLICATION_OCTET_STREAM;
        }
    }

    /**
     * 生成预签名PUT URL
     * @param objectKey 对象键
     * @param bucket 存储桶名称
     * @return 预签名URL
     */
    private String generatePresignedPutUrl(String objectKey, String bucket) {
        try {
            MinioClient client = beachWasteMinioClientUtil.getMinioClient();

            // 检查存储桶是否存在，如果不存在则创建
            if (!client.bucketExists(BucketExistsArgs.builder().bucket(bucket).build())) {
                client.makeBucket(MakeBucketArgs.builder().bucket(bucket).build());
            }

            return client.getPresignedObjectUrl(
                GetPresignedObjectUrlArgs.builder()
                    .method(io.minio.http.Method.PUT)
                    .bucket(bucket)
                    .object(objectKey)
                    .expiry(BeachWasteMinioClientUtil.EXPIRY, TimeUnit.HOURS)
                    .build());
        } catch (Exception e) {
            log.error("生成预签名PUT URL失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public R<Map<String, Object>> uniappUpload(MultipartFile file, String type) {
        try {
            // 验证文件是否为空
            if (file == null || file.isEmpty()) {
                return R.fail("文件不能为空");
            }

            // 获取原始文件名和扩展名
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                return R.fail("文件名不能为空");
            }

            // 获取文件扩展名
            String fileExtension = "";
            int lastDotIndex = originalFilename.lastIndexOf(".");
            if (lastDotIndex > 0 && lastDotIndex < originalFilename.length() - 1) {
                fileExtension = originalFilename.substring(lastDotIndex).toLowerCase();
            } else {
                return R.fail("文件必须包含有效的扩展名");
            }

            // 验证文件类型
            String fileType = fileExtension.substring(1);
            if (!isValidFileType(fileType)) {
                return R.fail("只支持图片文件类型（jpg、jpeg、png、gif、bmp、webp）");
            }

            // 验证文件大小（限制为10MB）
            long maxFileSize = 10 * 1024 * 1024;
            if (file.getSize() > maxFileSize) {
                return R.fail("文件大小不能超过10MB");
            }

            // 获取当前日期用于分区
            Date now = new Date();
            String datePartition = DateUtil.format(now, "yyyyMMdd");

            // 根据文件扩展名确定文件类型分类
            String fileTypeCategory = getFileTypeCategory(fileExtension);

            // 生成雪花ID确保唯一性
            String snowflakeId = String.valueOf(IdUtil.getSnowflakeNextId());

            // 生成随机UUID作为文件名
            String randomFileName = IdUtil.fastSimpleUUID();

            // 构建ObjectKey格式
            String objectKey = String.format("beach-litter-%s-%s-%s-%s%s",
                fileTypeCategory, datePartition, randomFileName, snowflakeId, fileExtension);

            // 上传文件到MinIO
            try {
                boolean uploadSuccess = beachWasteMinioClientUtil.uploadFile(
                    beachWasteMinioClientUtil.getBucketName(),
                    objectKey,
                    file.getInputStream(),
                    file.getContentType()
                );

                if (!uploadSuccess) {
                    return R.fail("文件上传失败，请重试");
                }

                // 构建返回结果，符合UniApp期望的格式
                Map<String, Object> result = Map.of(
                    "code", 200,
                    "message", "文件上传成功",
                    "data", Map.of(
                        "objectKey", objectKey,
                        "originalFilename", originalFilename,
                        "fileSize", file.getSize(),
                        "contentType", file.getContentType(),
                        "uploadTime", DateUtil.formatDateTime(now)
                    )
                );

                return R.data(result, "文件上传成功");

            } catch (Exception e) {
                log.error("MinIO文件上传失败: {}", e.getMessage(), e);
                return R.fail("文件上传失败: " + e.getMessage());
            }

        } catch (Exception e) {
            log.error("UniApp文件上传失败: {}", e.getMessage(), e);
            return R.fail("文件上传失败：" + e.getMessage());
        }
    }
}
