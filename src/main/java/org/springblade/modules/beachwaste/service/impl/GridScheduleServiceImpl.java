package org.springblade.modules.beachwaste.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.excel.GridScheduleImportExcel;
import org.springblade.modules.beachwaste.mapper.GridScheduleMapper;
import org.springblade.modules.beachwaste.pojo.dto.GridScheduleMonthQueryDTO;
import org.springblade.modules.beachwaste.pojo.dto.GridScheduleQueryDTO;
import org.springblade.modules.beachwaste.pojo.dto.GridScheduleRangeDTO;
import org.springblade.modules.beachwaste.pojo.entity.GridSchedule;
import org.springblade.modules.beachwaste.service.IGridScheduleService;
import org.springblade.modules.beachwaste.util.GridScheduleValidationUtil;
import org.springblade.modules.system.mapper.UserMapper;
import org.springblade.modules.system.pojo.entity.User;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.MonthDay;
import java.time.Year;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 网格排班信息服务实现类
 *
 * <AUTHOR> AI
 */
@Service
@AllArgsConstructor
public class GridScheduleServiceImpl extends ServiceImpl<GridScheduleMapper, GridSchedule> implements IGridScheduleService {

	@Resource
	private UserMapper userMapper;

    /**
     * 根据网格ID和日期查询排班信息
     * @param gridId 网格ID
     * @param scheduleDate 排班日期
     * @return 排班信息列表
     */
    @Override
    public List<GridSchedule> getScheduleByGridAndDate(Long gridId, LocalDate scheduleDate) {
        LambdaQueryWrapper<GridSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
			.eq(GridSchedule::getIsDeleted, 0)
			.eq(gridId != null, GridSchedule::getGridId, gridId)
			.eq(scheduleDate != null, GridSchedule::getScheduleDate, scheduleDate);
        return this.list(queryWrapper);
    }

    /**
     * 根据查询条件获取网格排班信息
     * @param queryDTO 查询参数DTO
     * @return 排班信息列表
     */
    @Override
    public R listByQueryDTO(GridScheduleQueryDTO queryDTO) {
        LambdaQueryWrapper<GridSchedule> queryWrapper = new LambdaQueryWrapper<>();

        // 根据管理员ID筛选
        if (queryDTO.getId() != null) {
            queryWrapper.eq(GridSchedule::getStaffId, queryDTO.getId());
        }

        // 根据排班状态筛选
        if (StringUtils.hasText(queryDTO.getScheduleStatus())) {
            LocalDate today = LocalDate.now();
            if ("1".equals(queryDTO.getScheduleStatus())) {
                // 当日已排班
                queryWrapper.eq(GridSchedule::getScheduleDate, today);
            } else if ("2".equals(queryDTO.getScheduleStatus())) {
                // 当日未排班 - 查询不在今天排班的记录
                queryWrapper.ne(GridSchedule::getScheduleDate, today);
            }
        }

        // 排序处理
        if (StringUtils.hasText(queryDTO.getSortBy())) {
            if ("0".equals(queryDTO.getSortBy())) {
                // 倒序
                queryWrapper.orderByDesc(GridSchedule::getScheduleDate);
            } else if ("1".equals(queryDTO.getSortBy())) {
                // 正序
                queryWrapper.orderByAsc(GridSchedule::getScheduleDate);
            }
        } else {
            // 默认按排班日期倒序
            queryWrapper.orderByDesc(GridSchedule::getScheduleDate);
        }

        return R.data(this.list(queryWrapper));
    }

    // 校验逻辑已移至GridScheduleValidationUtil工具类

    /**
     * 校验并保存日期范围内的网格排班信息
     *
     * @param rangeDTO 包含日期范围的排班信息DTO
     * @return 保存结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R checkAndSaveRange(GridScheduleRangeDTO rangeDTO) {
        // 基础校验，使用工具类进行校验
        R validateResult = GridScheduleValidationUtil.validateScheduleParams(rangeDTO);
        if (validateResult != null) {
            return validateResult;
        }

        // 处理单个时间范围的情况
        if (rangeDTO.getTimeRanges() == null || rangeDTO.getTimeRanges().isEmpty()) {
            if (rangeDTO.getStartDate() == null || rangeDTO.getEndDate() == null) {
                return R.fail("必须提供时间范围或时间范围列表");
            }
            return saveSingleTimeRange(rangeDTO, rangeDTO.getStartDate(), rangeDTO.getEndDate());
        }

        // 处理多个时间范围的情况
        for (GridScheduleRangeDTO.TimeRange timeRange : rangeDTO.getTimeRanges()) {
            R result = saveSingleTimeRange(rangeDTO, timeRange.getStartDate(), timeRange.getEndDate());
            if (!result.isSuccess()) {
                return result;
            }
        }

        return R.success("批量排班信息保存成功");
    }

    private R saveSingleTimeRange(GridScheduleRangeDTO rangeDTO, LocalDate startDate, LocalDate endDate) {
        // 1. 删除指定网格在日期范围内的现有排班记录
        LambdaQueryWrapper<GridSchedule> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper
            .eq(GridSchedule::getIsDeleted, 0)
            .eq(GridSchedule::getGridId, rangeDTO.getGridId())
            .between(GridSchedule::getScheduleDate, startDate, endDate);
        this.remove(deleteWrapper);

        // 2. 直接使用DTO中的staffName
        String staffName = rangeDTO.getStaffName();

        // 3. 生成新的排班记录列表
        List<GridSchedule> schedulesToSave = new ArrayList<>();
        Stream.iterate(startDate, date -> date.plusDays(1))
            .limit(ChronoUnit.DAYS.between(startDate, endDate) + 1)
            .forEach(currentDate -> {
                GridSchedule schedule = new GridSchedule();
                schedule.setGridId(rangeDTO.getGridId());
                schedule.setStaffId(rangeDTO.getStaffId());
                schedule.setStaffName(staffName);
                schedule.setScheduleDate(currentDate);
                schedule.setRemark(rangeDTO.getRemark());
                schedulesToSave.add(schedule);
            });

        // 4. 批量保存新的排班记录
        if (!schedulesToSave.isEmpty()) {
            boolean success = this.saveBatch(schedulesToSave);
            return success ? R.success("排班信息保存成功") : R.fail("排班信息保存失败");
        }
        return R.success("排班信息保存成功");
    }

    /**
     * 校验并更新日期范围内的网格排班信息
     * (实现为先删除旧范围，再插入新范围)
     *
     * @param rangeDTO 包含日期范围的排班信息DTO
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R checkAndUpdateRange(GridScheduleRangeDTO rangeDTO) {
        // 基础校验，使用工具类进行校验
        R validateResult = GridScheduleValidationUtil.validateScheduleParams(rangeDTO);
        if (validateResult != null) {
            return validateResult;
        }

        // 直接把范围内的记录删除并更新即可（因为调用更新接口传入的日期范围肯定是修改的具体某一天，故开始结束时间不需要进行切割）
        LambdaQueryWrapper<GridSchedule> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper
			.eq(GridSchedule::getIsDeleted, 0)
			.eq(GridSchedule::getGridId, rangeDTO.getGridId())
			.between(GridSchedule::getScheduleDate, rangeDTO.getStartDate(), rangeDTO.getEndDate());
        this.remove(deleteWrapper);

        GridSchedule schedule = new GridSchedule();
        schedule.setGridId(rangeDTO.getGridId());
        schedule.setStaffId(rangeDTO.getStaffId());
		schedule.setStaffName(rangeDTO.getStaffName());
        schedule.setScheduleDate(rangeDTO.getStartDate());
        schedule.setRemark(rangeDTO.getRemark());

        boolean success = this.save(schedule);
        return success? R.success("排班信息更新成功") : R.fail("排班信息更新失败");
    }

    /**
     * 获取指定网格指定月份的所有排班信息
     * 当某日没有排班数据时也需要返回
     * 按月查询时会返回前后各一个月的数据（例如查询4月，返回3月1日到5月31日的数据）
     *
     * @param queryDTO 包含网格ID、年份和月份的查询DTO
     * @return 日期-排班信息映射，包含指定月份所有日期
     */
    @Override
    public Map<String, GridSchedule> getMonthScheduleByQuery(GridScheduleMonthQueryDTO queryDTO) {
        // 计算查询范围
        LocalDate[] dateRange = calculateDateRange(queryDTO.getYear(), queryDTO.getMonth());
        LocalDate startDate = dateRange[0];
        LocalDate endDate = dateRange[1];

        // 查询指定范围该网格的所有排班记录
        List<GridSchedule> scheduleList = this.lambdaQuery()
            .eq(GridSchedule::getIsDeleted, 0)
            .eq(GridSchedule::getGridId, queryDTO.getGridId())
            .between(GridSchedule::getScheduleDate, startDate, endDate)
            .orderByAsc(GridSchedule::getScheduleDate)
            .list();

        // 优化：使用Set收集staffIds，避免重复
        Set<Long> staffIds = scheduleList.stream().map(GridSchedule::getStaffId).collect(Collectors.toSet());

        // 批量查询用户信息并建立映射关系
        Map<Long, String> staffIdToRealNameMap = new HashMap<>(staffIds.size());
        if (!staffIds.isEmpty()) {
			userMapper.selectList(Wrappers.<User>lambdaQuery().in(User::getId, staffIds))
                .forEach(user -> staffIdToRealNameMap.put(user.getId(), user.getRealName()));
        }

        // 优化：使用Map存储日期到排班记录的映射，避免重复查找
        Map<LocalDate, GridSchedule> dateToScheduleMap = scheduleList.stream()
            .peek(schedule -> schedule.setStaffName(staffIdToRealNameMap.get(schedule.getStaffId())))
            .collect(Collectors.toMap(GridSchedule::getScheduleDate, schedule -> schedule,
                (existing, replacement) -> existing));

        // 构建返回结果
        Map<String, GridSchedule> resultMap = new LinkedHashMap<>();
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            String dateStr = currentDate.toString();
            resultMap.put(dateStr, dateToScheduleMap.get(currentDate));
            currentDate = currentDate.plusDays(1);
        }

        return resultMap;
    }

    /**
     * 计算查询的日期范围
     *
     * @param year 年份
     * @param month 月份（可为null，表示按年查询）
     * @return 日期范围数组 [开始日期, 结束日期]
     */
    private LocalDate[] calculateDateRange(int year, Integer month) {
        if (month == null) {
            // 按年查询
            return new LocalDate[] {
                Year.of(year).atDay(1),
                Year.of(year).atMonthDay(MonthDay.of(12, 31))
            };
        }
        // 按月查询 - 返回前后各一个月的数据
        YearMonth targetMonth = YearMonth.of(year, month);
        return new LocalDate[] {
            targetMonth.minusMonths(1).atDay(1),
            targetMonth.plusMonths(1).atEndOfMonth()
        };
    }

    /**
     * 批量导入网格排班信息
     * 校验逻辑已经在GridScheduleImportListener中处理
     * 这里专注于业务逻辑实现：删除已存在的排班记录并新增
     *
     * @param dataList 导入的排班数据列表（已经过校验）
     * @return 导入结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R importGridSchedule(List<GridScheduleImportExcel> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return R.fail("导入数据为空");
        }

        // 处理每条导入数据
        for (GridScheduleImportExcel importData : dataList) {
            // 注意：这里假设importData中已经包含了staffId
            // 在导入监听器GridScheduleImportListener中已经完成了用户查询和验证
            if (importData.getStaffId() == null) {
                return R.fail("导入失败：网格员ID为空 " + importData.getStaffName());
            }

            // 删除库中存在的相同网格编号，相同时间段数据
            LambdaQueryWrapper<GridSchedule> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper
				.eq(GridSchedule::getIsDeleted, 0)
				.eq(GridSchedule::getGridId, importData.getGridId())
			 	.between(GridSchedule::getScheduleDate, importData.getStartDate(), importData.getEndDate());
            this.remove(deleteWrapper);

            // 生成新的排班记录列表
            List<GridSchedule> schedulesToSave = new ArrayList<>();
            Stream.iterate(importData.getStartDate(), date -> date.plusDays(1))
                    .limit(ChronoUnit.DAYS.between(importData.getStartDate(), importData.getEndDate()) + 1)
                    .forEach(currentDate -> {
                        GridSchedule schedule = new GridSchedule();
                        schedule.setGridId(importData.getGridId());
                        schedule.setStaffId(importData.getStaffId());
                        schedule.setStaffName(importData.getStaffName());
                        schedule.setScheduleDate(currentDate);
                        schedulesToSave.add(schedule);
                    });

            // 批量保存新的排班记录
            if (!schedulesToSave.isEmpty() && !this.saveBatch(schedulesToSave)) {
                return R.fail("保存排班信息失败：网格编号 " + importData.getGridId());
            }
        }

        return R.success("批量导入排班信息成功");
    }

    /**
     * 删除指定网格员从明天开始的所有排班任务
     *
     * @param staffId 网格员ID
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeGridStaffScheduleFromTomorrow(Long staffId) {
        if (staffId == null) {
            return false;
        }

        // 获取明天的日期
        LocalDate tomorrow = LocalDate.now().plusDays(1);

        // 构建查询条件：删除指定网格员从明天开始的所有排班任务
        LambdaQueryWrapper<GridSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
            .eq(GridSchedule::getStaffId, staffId)
            .ge(GridSchedule::getScheduleDate, tomorrow);

        // 执行删除操作
        return this.remove(queryWrapper);
    }
}
