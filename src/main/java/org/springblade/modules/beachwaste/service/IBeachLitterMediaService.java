package org.springblade.modules.beachwaste.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.pojo.entity.BeachLitterMedia;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.http.ResponseEntity;
import org.springframework.core.io.Resource;

import java.util.Map;

public interface IBeachLitterMediaService extends IService<BeachLitterMedia> {

    /**
     * 上传图片文件
     *
     * @param file 图片文件
     * @return 上传结果，包含ObjectKey
     */
    R<String> uploadImage(MultipartFile file);

    /**
     * 生成预签名 URL 并返回 ObjectKey
     *
     * @param fileType 文件类型（如：jpg、png、gif等，不含点号）
     * @return 预签名URL和ObjectKey
     */
    R<Map<String, Object>> generatePresignedUrl(String fileType);

    /**
     * 根据ObjectKey下载文件
     *
     * @param objectKey MinIO中的对象键
     * @return 文件流响应
     */
    ResponseEntity<Resource> downloadFile(String objectKey);

    /**
     * UniApp文件上传
     * 适配UniApp的uni.uploadFile组件
     *
     * @param file 上传的文件
     * @param type 文件类型（可选）
     * @return 上传结果
     */
    R<Map<String, Object>> uniappUpload(MultipartFile file, String type);

}
