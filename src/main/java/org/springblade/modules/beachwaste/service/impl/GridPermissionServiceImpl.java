package org.springblade.modules.beachwaste.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.service.IGridPermissionService;
import org.springblade.modules.system.pojo.entity.Role;
import org.springblade.modules.system.service.IRoleService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 网格权限服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class GridPermissionServiceImpl implements IGridPermissionService {

    private final IRoleService roleService;

    @Override
    public List<Long> getGridRoleIds(String tenantId) {
        log.info("开始查询租户 {} 下的网格员和网格管理员角色ID", tenantId);
        
        List<Long> roleIds = new ArrayList<>();
        
        try {
            // 查询网格员角色ID
            List<Role> gridStaffRoles = roleService.list(
                new LambdaQueryWrapper<Role>()
                    .eq(Role::getTenantId, tenantId)
                    .eq(Role::getRoleName, "网格员")
                    .eq(Role::getIsDeleted, 0)
            );
            
            // 查询网格管理员角色ID
            List<Role> gridManagerRoles = roleService.list(
                new LambdaQueryWrapper<Role>()
                    .eq(Role::getTenantId, tenantId)
                    .eq(Role::getRoleName, "网格管理员")
                    .eq(Role::getIsDeleted, 0)
            );
            
            // 收集网格员角色ID
            if (!gridStaffRoles.isEmpty()) {
                List<Long> staffIds = gridStaffRoles.stream()
                    .map(Role::getId)
                    .collect(Collectors.toList());
                roleIds.addAll(staffIds);
                log.info("找到网格员角色ID: {}", staffIds);
            } else {
                log.warn("租户 {} 下未找到网格员角色", tenantId);
            }
            
            // 收集网格管理员角色ID
            if (!gridManagerRoles.isEmpty()) {
                List<Long> managerIds = gridManagerRoles.stream()
                    .map(Role::getId)
                    .collect(Collectors.toList());
                roleIds.addAll(managerIds);
                log.info("找到网格管理员角色ID: {}", managerIds);
            } else {
                log.warn("租户 {} 下未找到网格管理员角色", tenantId);
            }
            
            log.info("租户 {} 下共找到 {} 个网格相关角色ID: {}", tenantId, roleIds.size(), roleIds);
            
        } catch (Exception e) {
            log.error("查询租户 {} 下的网格角色ID时发生异常: {}", tenantId, e.getMessage(), e);
            throw new RuntimeException("查询网格角色ID失败: " + e.getMessage());
        }
        
        return roleIds;
    }
}