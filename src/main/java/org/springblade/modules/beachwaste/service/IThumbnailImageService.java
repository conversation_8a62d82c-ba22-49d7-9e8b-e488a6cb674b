package org.springblade.modules.beachwaste.service;

/**
 * 缩略图服务接口
 * 提供缩略图的上传、存储和管理功能
 *
 * <AUTHOR>
 */
public interface IThumbnailImageService {

    /**
     * 上传缩略图到MinIO
     *
     * @param thumbnailData 缩略图字节数组
     * @param eventId       事件ID
     * @return MinIO对象键，如果上传失败返回null
     */
    String uploadThumbnail(byte[] thumbnailData, Long eventId);

    /**
     * 获取缩略图URL
     *
     * @param objectKey MinIO对象键
     * @return 缩略图URL
     */
    String getThumbnailUrl(String objectKey);
}