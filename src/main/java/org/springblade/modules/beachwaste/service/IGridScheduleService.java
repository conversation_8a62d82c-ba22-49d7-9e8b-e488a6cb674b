package org.springblade.modules.beachwaste.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.excel.GridScheduleImportExcel;
import org.springblade.modules.beachwaste.pojo.dto.GridScheduleMonthQueryDTO;
import org.springblade.modules.beachwaste.pojo.dto.GridScheduleQueryDTO;
import org.springblade.modules.beachwaste.pojo.dto.GridScheduleRangeDTO;
import org.springblade.modules.beachwaste.pojo.entity.GridSchedule;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 网格排班信息服务接口
 *
 * <AUTHOR> AI
 */
public interface IGridScheduleService extends IService<GridSchedule> {

    /**
     * 根据网格ID和日期查询排班信息
     *
     * @param gridId       网格ID
     * @param scheduleDate 排班日期
     * @return 排班信息列表
     */
    List<GridSchedule> getScheduleByGridAndDate(Long gridId, LocalDate scheduleDate);

    /**
     * 根据查询条件获取网格排班信息
     *
     * @param queryDTO 查询参数DTO
     * @return 排班信息列表
     */
    R listByQueryDTO(GridScheduleQueryDTO queryDTO);

    /**
     * 校验并保存日期范围内的网格排班信息
     *
     * @param gridScheduleRangeDTO 包含日期范围的排班信息DTO
     * @return 保存结果
     */
    R checkAndSaveRange(GridScheduleRangeDTO gridScheduleRangeDTO);

    /**
     * 校验并更新日期范围内的网格排班信息
     * (通常实现为先删除旧范围，再插入新范围)
     *
     * @param gridScheduleRangeDTO 包含日期范围的排班信息DTO
     * @return 更新结果
     */
    R checkAndUpdateRange(GridScheduleRangeDTO gridScheduleRangeDTO);

    /**
     * 获取指定网格当月的所有排班信息
     * 当某日没有排班数据时也需要返回
     *
     * @param queryDTO 包含网格ID、年份和月份的查询DTO
     * @return 日期-排班信息映射，包含指定月份所有日期
     */
    Map<String, GridSchedule> getMonthScheduleByQuery(GridScheduleMonthQueryDTO queryDTO);

    /**
     * 批量导入网格排班信息
     * 如果遇到有相同网格编号的两条数据，开始结束时间存在闭包关系则提示报错，
     * 否则删除库中存在的相同网格编号，相同时间段数据并新增
     *
     * @param dataList 导入的排班数据列表
     * @return 导入结果
     */
    R importGridSchedule(List<GridScheduleImportExcel> dataList);

    /**
     * 删除指定网格员从明天开始的所有排班任务
     *
     * @param staffId 网格员ID
     * @return 删除结果
     */
    boolean removeGridStaffScheduleFromTomorrow(Long staffId);

}
