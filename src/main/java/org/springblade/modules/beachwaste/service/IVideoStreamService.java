package org.springblade.modules.beachwaste.service;

import org.springblade.modules.beachwaste.pojo.vo.CameraVO;
import org.springblade.modules.beachwaste.pojo.vo.CloseStreamsVO;
import org.springblade.modules.beachwaste.pojo.vo.MediaListVO;

import java.util.List;

/**
 * 视频流媒体数据服务接口
 *
 * <AUTHOR>
public interface IVideoStreamService {

    /**
     * 获取所有摄像头列表
     *
     * @return 获取所有摄像头列表
     */
    List<CameraVO> getCameraList();

    /**
     * 获取流媒体列表
     *
     * @param schema 筛选协议，例如 rtsp 或 rtmp
     * @param vhost 筛选虚拟主机，例如 __defaultVhost__
     * @param app 筛选应用名，例如 live
     * @param stream 筛选流id，例如 test
     * @return 流媒体列表数据
     */
    MediaListVO getMediaList(String schema, String vhost, String app, String stream);

    /**
     * 关闭流媒体
     *
     * @param schema 筛选协议，例如 rtsp 或 rtmp
     * @param vhost 筛选虚拟主机，例如 __defaultVhost__
     * @param app 筛选应用名，例如 live
     * @param stream 筛选流id，例如 test
     * @param force 是否强制关闭所有在线观看者
     * @return 关闭流响应数据
     */
    CloseStreamsVO closeStreams(String schema, String vhost, String app, String stream, Boolean force);
    
}
