package org.springblade.modules.beachwaste.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.config.AiServerConfig;
import org.springblade.modules.beachwaste.pojo.dto.FrameRecognitionTaskDTO;
import org.springblade.modules.beachwaste.pojo.vo.FrameRecognitionTaskVO;
import org.springblade.modules.beachwaste.service.IFrameRecognitionTaskService;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

/**
 * 帧识别任务服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FrameRecognitionTaskServiceImpl implements IFrameRecognitionTaskService {

	private final AiServerConfig aiServerConfig;
	private final RestTemplate restTemplate;

	@Override
	public FrameRecognitionTaskVO startTask(FrameRecognitionTaskDTO taskDTO) {
		long startTime = System.currentTimeMillis();
		
		try {
			log.info("开始调用AI服务器帧识别接口，参数：{}", taskDTO);
			
			// 构建请求头
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);
			
			// 构建请求实体
			HttpEntity<FrameRecognitionTaskDTO> requestEntity = new HttpEntity<>(taskDTO, headers);
			
			// 获取AI服务器接口URL
			String aiServerUrl = aiServerConfig.getFrameRecognitionUrl();
			log.info("调用AI服务器接口：{}", aiServerUrl);
			
			// 发送POST请求到AI服务器
			ResponseEntity<FrameRecognitionTaskVO> response = restTemplate.postForEntity(
					aiServerUrl,
					requestEntity,
					FrameRecognitionTaskVO.class
			);
			
			// 计算实际耗时
			long endTime = System.currentTimeMillis();
			float consumingTime = (endTime - startTime) / 1000.0f;
			
			FrameRecognitionTaskVO result = response.getBody();
			if (result != null) {
				// 更新实际耗时
				result.setConsumingTime(consumingTime);
				log.info("AI服务器调用成功，耗时：{}秒，响应：{}", consumingTime, result);
				return result;
			} else {
				log.error("AI服务器返回空响应");
				return createErrorResponse("AI服务器返回空响应", consumingTime);
			}
			
		} catch (RestClientException e) {
			long endTime = System.currentTimeMillis();
			float consumingTime = (endTime - startTime) / 1000.0f;
			
			log.error("调用AI服务器失败，耗时：{}秒，错误：{}", consumingTime, e.getMessage(), e);
			return createErrorResponse("调用AI服务器失败：" + e.getMessage(), consumingTime);
			
		} catch (Exception e) {
			long endTime = System.currentTimeMillis();
			float consumingTime = (endTime - startTime) / 1000.0f;
			
			log.error("帧识别任务处理异常，耗时：{}秒，错误：{}", consumingTime, e.getMessage(), e);
			return createErrorResponse("系统内部错误：" + e.getMessage(), consumingTime);
		}
	}
	
	/**
	 * 创建错误响应
	 *
	 * @param errorMessage 错误信息
	 * @param consumingTime 耗时
	 * @return 错误响应对象
	 */
	private FrameRecognitionTaskVO createErrorResponse(String errorMessage, float consumingTime) {
		FrameRecognitionTaskVO errorResult = new FrameRecognitionTaskVO();
		errorResult.setCode(-1);
		errorResult.setMessage(errorMessage);
		errorResult.setConsumingTime(consumingTime);
		errorResult.setStreamUrlOut(null);
		errorResult.setData(null);
		return errorResult;
	}
}
