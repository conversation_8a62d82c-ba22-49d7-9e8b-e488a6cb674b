package org.springblade.modules.beachwaste.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.pojo.dto.GridScheduleQueryDTO;
import org.springblade.modules.beachwaste.pojo.entity.SpatialGrid;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 空间网格服务接口
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
public interface ISpatialGridService extends IService<SpatialGrid> {

	/**
	 * 获取网格详细信息
	 * @param id 网格ID
	 * @param yearMonth 查询年月
	 * @return 网格详情和关联的事件信息
	 */
	R getGridDetailWithEvents(Long id, String yearMonth);

	/**
	 * 检查并保存网格信息
	 * @param spatialGrid 空间网格实体对象
	 * @return 保存结果（成功或失败原因）
	 */
	R checkToSave(SpatialGrid spatialGrid);

	/**
	 * 检查并更新网格信息
	 * @param spatialGrid 空间网格实体对象
	 * @return 保存结果（成功或失败原因）
	 */
	R checkToUpdateById(SpatialGrid spatialGrid);

	/**
	 * 解析KML文件
	 * @param file 上传的KML文件对象
	 * @param inspectorId 管理员id
	 * @param gridName 网格名称
	 * @param gridCode 网格编码
	 * @return 解析结果（成功或失败原因）
	 */
	R parseKmlFile(MultipartFile file, Long inspectorId, String gridName, String gridCode);

	/**
	 * 解析多个地理要素
	 * @param file 上传的KML文件对象
	 * @return 解析结果（包含多个地理要素信息的列表）
	 */
	R parseMultipleFeatures(MultipartFile file);

	/**
	 * 批量保存网格数据
	 * @param gridList 网格数据列表
	 * @return 保存结果（成功或失败原因）
	 */
	R batchSaveGrids(List<SpatialGrid> gridList);

	/**
	 * 获取网格列表数据（包含排班和管理员信息）
	 * @param dto 排序方式
	 * @return 网格列表数据
	 */
	R getGridListWithSchedule(GridScheduleQueryDTO dto);

	/**
	 * 导出所有有效空间网格数据为KML文件
	 * @param filename 自定义文件名（可选）
	 * @return KML文件内容的字符串
	 */
	String exportAllGridsToKml(String filename);

}
