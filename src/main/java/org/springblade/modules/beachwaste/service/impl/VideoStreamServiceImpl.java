package org.springblade.modules.beachwaste.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.FhOpenapiPathConstant;
import org.springblade.common.constant.StreamMediaPathConstant;
import org.springblade.modules.beachwaste.pojo.vo.*;
import org.springblade.modules.beachwaste.service.IVideoStreamService;
import org.springblade.modules.fh.pojo.vo.FhOADeviceVO;
import org.springblade.modules.fh.pojo.vo.FhOAPrjDeviceVO;
import org.springblade.modules.fh.service.IFhOrgService;
import org.springblade.modules.fh.utils.FhOpenApiHttpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 视频流媒体数据服务实现类
 *
 * <p>提供视频流媒体相关功能，包括：</p>
 * <ul>
 *     <li>获取摄像头列表</li>
 *     <li>获取流媒体列表</li>
 *     <li>关闭流媒体</li>
 * </ul>
 *
 * <AUTHOR>
@Slf4j
@Service
public class VideoStreamServiceImpl implements IVideoStreamService {

    private final IFhOrgService fhOrgService;
    private final RestTemplate restTemplate;

    @Value("${stream.media.base-url}")
    private String mediaBaseUrl;

    @Value("${stream.media.live-suffix}")
    private String liveSuffix;

    @Value("${stream.media.suffix}")
    private String streamSuffix;

    @Value("${stream.media.ai-prefix}")
    private String aiPrefix;

    @Value("${stream.media.secret}")
    private String mediaApiSecret;

    /**
     * 构造函数注入依赖
     */
    @Autowired
    public VideoStreamServiceImpl(IFhOrgService fhOrgService, RestTemplate restTemplate) {
        this.fhOrgService = fhOrgService;
        this.restTemplate = restTemplate;
    }

    /**
     * 获取摄像头列表
     *
     * @return 摄像头列表
     * @throws RuntimeException 获取失败时抛出异常
     */
    @Override
    public List<CameraVO> getCameraList() {
        try {
            // 获取设备SN号列表
            List<String> deviceSnList = extractDeviceSnList();

            // 获取码流转发器列表
            List<StreamForwarderVO> streamForwarderList = getStreamForwarderList();

            // 构建摄像头列表
            return buildCameraList(deviceSnList, streamForwarderList);

        } catch (Exception e) {
            log.error("获取摄像头列表失败", e);
            throw new RuntimeException("获取摄像头列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 提取设备SN号列表
     *
     * @return SN号列表
     */
    private List<String> extractDeviceSnList() {
        List<FhOAPrjDeviceVO> deviceList = fhOrgService.prjDevices();
        List<String> snList = new ArrayList<>();

        if (deviceList == null || deviceList.isEmpty()) {
            return snList;
        }

        for (FhOAPrjDeviceVO device : deviceList) {
            addDeviceSnIfPresent(snList, device.getGateway());
            addDeviceSnIfPresent(snList, device.getDrone());
        }

        return snList;
    }

    /**
     * 添加设备SN号到列表（如果存在）
     *
     * @param snList SN号列表
     * @param device 设备信息
     */
    private void addDeviceSnIfPresent(List<String> snList, FhOADeviceVO device) {
        if (device != null && device.getSn() != null) {
            snList.add(device.getSn());
        }
    }

    /**
     * 获取码流转发器列表
     *
     * @return 码流转发器列表
     */
    private List<StreamForwarderVO> getStreamForwarderList() {
        String response = FhOpenApiHttpUtil.get(FhOpenapiPathConstant.LIVE_STREAM_CONVERTER_LIST);
        log.info("获取码流转发器列表成功，响应: {}", response);
        return parseStreamForwarderResponse(response);
    }

    /**
     * 构建摄像头列表
     *
     * @param deviceSnList 设备SN号列表
     * @param streamForwarderList 码流转发器列表
     * @return 摄像头列表
     */
    private List<CameraVO> buildCameraList(List<String> deviceSnList, List<StreamForwarderVO> streamForwarderList) {
        if (streamForwarderList == null || streamForwarderList.isEmpty()) {
            return new ArrayList<>();
        }

        return streamForwarderList.stream()
                .filter(forwarder -> deviceSnList.contains(forwarder.getSn()))
                .map(this::convertToCameraVO)
                .sorted((c1, c2) -> {
                    String name1 = c1.getConverterName() != null ? c1.getConverterName() : "";
                    String name2 = c2.getConverterName() != null ? c2.getConverterName() : "";
                    return name1.compareTo(name2);
                })
                .collect(Collectors.toList());
    }

    /**
     * 转换StreamForwarderVO为CameraVO
     *
     * @param forwarder 码流转发器信息
     * @return 摄像头信息
     */
    private CameraVO convertToCameraVO(StreamForwarderVO forwarder) {
        CameraVO camera = new CameraVO();
        camera.setSn(forwarder.getSn());
        camera.setConverterName(forwarder.getConverterName());
        camera.setConverterId(forwarder.getConverterId());
        camera.setCameraIndex(forwarder.getCameraIndex());
        camera.setUrl(forwarder.getUrl());

        // 设置流地址
        setStreamUrls(camera, forwarder);

        return camera;
    }

    /**
     * 设置流地址
     *
     * @param camera 摄像头信息
     * @param forwarder 码流转发器信息
     */
    private void setStreamUrls(CameraVO camera, StreamForwarderVO forwarder) {
        String sn = forwarder.getSn();
        if (sn != null) {
            camera.setOriginStream(mediaBaseUrl + liveSuffix + "/" + sn + streamSuffix);
            camera.setAiStream(mediaBaseUrl + liveSuffix + "/" + aiPrefix + sn + streamSuffix);
        }

        // 设置AI URL
        setAiUrl(camera, forwarder.getUrl());
    }

    /**
     * 设置AI URL地址
     *
     * @param camera 摄像头信息
     * @param originalUrl 原始URL
     */
    private void setAiUrl(CameraVO camera, String originalUrl) {
        if (originalUrl != null && originalUrl.contains("/live/")) {
            int liveIndex = originalUrl.indexOf("/live/") + 6;
            String afterLive = originalUrl.substring(liveIndex);
            String baseUrl = originalUrl.substring(0, liveIndex);
            camera.setAiUrl(baseUrl + aiPrefix + afterLive);
        }
    }

    /**
     * 解析码流转发器响应JSON并转换为StreamForwarderVO列表
     *
     * @param response API响应
     * @return 码流转发器列表
     */
    private List<StreamForwarderVO> parseStreamForwarderResponse(String response) {
        try {
            // 解析响应JSON
            JSONObject responseJson = JSONUtil.parseObj(response);
            Integer code = responseJson.getInt("code");
            String message = responseJson.getStr("message");

            // 检查API调用是否成功
            if (code == null || code != 0) {
                log.error("大疆API返回错误，code: {}, message: {}", code, message);
                throw new RuntimeException("获取码流转发器列表失败: " + message);
            }

            // 提取data字段并转换为StreamForwarderVO列表
            Object dataObject = responseJson.get("data");
            List<StreamForwarderVO> resultList = new ArrayList<>();

            if (dataObject != null) {
                if (dataObject instanceof JSONArray) {
                    // data字段是数组类型
                    JSONArray dataArray = (JSONArray) dataObject;
                    for (Object item : dataArray) {
                        StreamForwarderVO vo = parseStreamForwarderItem((JSONObject) item);
                        resultList.add(vo);
                    }
                } else if (dataObject instanceof JSONObject) {
                    // data字段是对象类型，检查是否包含list字段
                    JSONObject dataJsonObject = (JSONObject) dataObject;
                    if (dataJsonObject.containsKey("list")) {
                        // 包含list字段，提取list数组
                        JSONArray listArray = dataJsonObject.getJSONArray("list");
                        if (listArray != null) {
                            for (Object item : listArray) {
                                StreamForwarderVO vo = parseStreamForwarderItem((JSONObject) item);
                                resultList.add(vo);
                            }
                        }
                        log.info("成功解析包装响应，包含{}个转发器项目", resultList.size());
                    } else {
                        // 不包含list字段，作为单个项目处理
                        StreamForwarderVO vo = parseStreamForwarderItem(dataJsonObject);
                        resultList.add(vo);
                    }
                } else {
                    log.warn("data字段类型不支持: {}", dataObject.getClass().getSimpleName());
                }
            }

            log.info("成功解析码流转发器列表，共{}条记录", resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("解析码流转发器响应失败，错误信息: {}", e.getMessage(), e);
            throw new RuntimeException("解析码流转发器响应失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析单个码流转发器项目，处理嵌套的schema_option结构
     *
     * @param item JSON对象
     * @return 码流转发器信息
     */
    private StreamForwarderVO parseStreamForwarderItem(JSONObject item) {
        StreamForwarderVO vo = new StreamForwarderVO();

        // 设置基本字段
        vo.setConverterName(item.getStr("converter_name"));
        vo.setConverterId(item.getStr("converter_id"));
        vo.setSn(item.getStr("sn"));
        vo.setCameraIndex(item.getStr("camera_index"));

        // 从schema_option中提取url字段
        JSONObject schemaOption = item.getJSONObject("schema_option");
        if (schemaOption != null) {
            String url = schemaOption.getStr("url");
            vo.setUrl(url);
            log.debug("从schema_option中提取URL: {}", url);
        }

        return vo;
    }

    /**
     * 获取流媒体列表
     *
     * @param schema 协议类型
     * @param vhost 虚拟主机
     * @param app 应用名称
     * @param stream 流名称
     * @return 流媒体列表
     * @throws RuntimeException 获取失败时抛出异常
     */
    @Override
    public MediaListVO getMediaList(String schema, String vhost, String app, String stream) {
        try {
            String url = buildMediaListUrl(schema, vhost, app, stream);
            log.info("调用流媒体API获取媒体列表，URL: {}", url);

            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            String responseBody = response.getBody();
            log.info("获取流媒体列表成功，响应: {}", responseBody);

            return parseMediaListResponse(responseBody);

        } catch (Exception e) {
            log.error("获取流媒体列表失败", e);
            throw new RuntimeException("获取流媒体列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建获取媒体列表的URL
     *
     * @param schema 协议类型
     * @param vhost 虚拟主机
     * @param app 应用名称
     * @param stream 流名称
     * @return 构建的URL
     */
    private String buildMediaListUrl(String schema, String vhost, String app, String stream) {
        StringBuilder urlBuilder = new StringBuilder(mediaBaseUrl + StreamMediaPathConstant.GET_MEDIA_LIST);
        urlBuilder.append("?secret=").append(mediaApiSecret);

        appendParameterIfNotEmpty(urlBuilder, "schema", schema);
        appendParameterIfNotEmpty(urlBuilder, "vhost", vhost);
        appendParameterIfNotEmpty(urlBuilder, "app", app);
        appendParameterIfNotEmpty(urlBuilder, "stream", stream);

        return urlBuilder.toString();
    }

    /**
     * 构建关闭流的URL
     *
     * @param schema 协议类型
     * @param vhost 虚拟主机
     * @param app 应用名称
     * @param stream 流名称
     * @param force 是否强制关闭
     * @return 构建的URL
     */
    private String buildCloseStreamsUrl(String schema, String vhost, String app, String stream, Boolean force) {
        StringBuilder urlBuilder = new StringBuilder(mediaBaseUrl + StreamMediaPathConstant.CLOSE_STREAMS);
        urlBuilder.append("?secret=").append(mediaApiSecret);

        appendParameterIfNotEmpty(urlBuilder, "schema", schema);
        appendParameterIfNotEmpty(urlBuilder, "vhost", vhost);
        appendParameterIfNotEmpty(urlBuilder, "app", app);
        appendParameterIfNotEmpty(urlBuilder, "stream", stream);

        if (force != null) {
            urlBuilder.append("&force=").append(force ? 1 : 0);
        }

        return urlBuilder.toString();
    }

    /**
     * 添加非空参数到URL
     *
     * @param urlBuilder URL构建器
     * @param paramName 参数名
     * @param paramValue 参数值
     */
    private void appendParameterIfNotEmpty(StringBuilder urlBuilder, String paramName, String paramValue) {
        if (paramValue != null && !paramValue.trim().isEmpty()) {
            urlBuilder.append("&").append(paramName).append("=").append(paramValue);
        }
    }

    /**
     * 解析流媒体列表响应JSON并转换为MediaListVO
     *
     * @param response API响应
     * @return 流媒体列表
     */
    private MediaListVO parseMediaListResponse(String response) {
        try {
            // 解析响应JSON
            JSONObject responseJson = JSONUtil.parseObj(response);
            MediaListVO mediaListVO = new MediaListVO();

            // 设置基本字段
            mediaListVO.setCode(responseJson.getInt("code"));
            mediaListVO.setMessage(responseJson.getStr("message"));

            // 解析data字段
            Object dataObject = responseJson.get("data");
            List<MediaItemVO> mediaItems = new ArrayList<>();

            if (dataObject != null) {
                if (dataObject instanceof JSONArray) {
                    // data字段是数组类型
                    JSONArray dataArray = (JSONArray) dataObject;
                    for (Object item : dataArray) {
                        MediaItemVO mediaItem = parseMediaItem((JSONObject) item);
                        mediaItems.add(mediaItem);
                    }
                } else if (dataObject instanceof JSONObject) {
                    // data字段是对象类型，检查是否包含list字段
                    JSONObject dataJsonObject = (JSONObject) dataObject;
                    if (dataJsonObject.containsKey("list")) {
                        // 包含list字段，提取list数组
                        JSONArray listArray = dataJsonObject.getJSONArray("list");
                        if (listArray != null) {
                            for (Object item : listArray) {
                                MediaItemVO mediaItem = parseMediaItem((JSONObject) item);
                                mediaItems.add(mediaItem);
                            }
                        }
                    } else {
                        // 不包含list字段，作为单个项目处理
                        MediaItemVO mediaItem = parseMediaItem(dataJsonObject);
                        mediaItems.add(mediaItem);
                    }
                }
            }

            mediaListVO.setData(mediaItems);
            log.info("成功解析流媒体列表，共{}条记录", mediaItems.size());
            return mediaListVO;

        } catch (Exception e) {
            log.error("解析流媒体列表响应失败，错误信息: {}", e.getMessage(), e);
            throw new RuntimeException("解析流媒体列表响应失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析单个媒体项目
     *
     * @param item JSON对象
     * @return 媒体项目信息
     */
    private MediaItemVO parseMediaItem(JSONObject item) {
        MediaItemVO mediaItem = new MediaItemVO();

        // 设置基本字段
        mediaItem.setApp(item.getStr("app"));
        mediaItem.setReaderCount(item.getInt("readerCount"));
        mediaItem.setTotalReaderCount(item.getInt("totalReaderCount"));
        mediaItem.setSchema(item.getStr("schema"));
        mediaItem.setStream(item.getStr("stream"));
        mediaItem.setOriginUrl(item.getStr("originUrl"));
        mediaItem.setCreateStamp(item.getLong("createStamp"));
        mediaItem.setAliveSecond(item.getInt("aliveSecond"));
        mediaItem.setBytesSpeed(item.getInt("bytesSpeed"));
        mediaItem.setVhost(item.getStr("vhost"));

        // 解析originType数组
        JSONArray originTypeArray = item.getJSONArray("originType");
        if (originTypeArray != null) {
            List<OriginTypeVO> originTypes = new ArrayList<>();
            for (Object originTypeObj : originTypeArray) {
                JSONObject originTypeJson = (JSONObject) originTypeObj;
                OriginTypeVO originType = new OriginTypeVO();
                originType.setIdentifier(originTypeJson.getStr("identifier"));
                originType.setLocalIp(originTypeJson.getStr("local_ip"));
                originType.setLocalPort(originTypeJson.getInt("local_port"));
                originType.setPeerIp(originTypeJson.getStr("peer_ip"));
                originType.setPeerPort(originTypeJson.getInt("peer_port"));
                originType.setOriginTypeStr(originTypeJson.getStr("originTypeStr"));
                originType.setOriginUrl(originTypeJson.getStr("originUrl"));
                originTypes.add(originType);
            }
            mediaItem.setOriginType(originTypes);
        }

        // 解析tracks数组
        JSONArray tracksArray = item.getJSONArray("tracks");
        if (tracksArray != null) {
            List<TrackVO> tracks = new ArrayList<>();
            for (Object trackObj : tracksArray) {
                JSONObject trackJson = (JSONObject) trackObj;
                TrackVO track = new TrackVO();
                track.setChannels(trackJson.getInt("channels"));
                track.setCodecId(trackJson.getInt("codec_id"));
                track.setCodecIdName(trackJson.getStr("codec_id_name"));
                track.setCodecType(trackJson.getInt("codec_type"));
                track.setFps(trackJson.getInt("fps"));
                track.setReady(trackJson.getBool("ready"));
                track.setFrames(trackJson.getInt("frames"));
                track.setSampleBit(trackJson.getInt("sample_bit"));
                track.setSampleRate(trackJson.getInt("sample_rate"));
                track.setGopIntervalMs(trackJson.getInt("gop_interval_ms"));
                track.setGopSize(trackJson.getInt("gop_size"));
                track.setKeyFrames(trackJson.getInt("key_frames"));
                track.setHeight(trackJson.getInt("height"));
                track.setReady2(trackJson.getBool("ready"));
                track.setWidth(trackJson.getInt("width"));
                tracks.add(track);
            }
            mediaItem.setTracks(tracks);
        }

        return mediaItem;
    }

    /**
     * 关闭流媒体
     *
     * @param schema 协议类型
     * @param vhost 虚拟主机
     * @param app 应用名称
     * @param stream 流名称
     * @param force 是否强制关闭
     * @return 关闭结果
     * @throws RuntimeException 关闭失败时抛出异常
     */
    @Override
    public CloseStreamsVO closeStreams(String schema, String vhost, String app, String stream, Boolean force) {
        try {
            String url = buildCloseStreamsUrl(schema, vhost, app, stream, force);
            log.info("调用流媒体API关闭流，URL: {}", url);

            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            String responseBody = response.getBody();
            log.info("关闭流成功，响应: {}", responseBody);

            return parseCloseStreamsResponse(responseBody);

        } catch (Exception e) {
            log.error("关闭流失败", e);
            throw new RuntimeException("关闭流失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析关闭流响应JSON并转换为CloseStreamsVO
     *
     * @param response API响应
     * @return 关闭流结果
     */
    private CloseStreamsVO parseCloseStreamsResponse(String response) {
        try {
            // 解析响应JSON
            JSONObject responseJson = JSONUtil.parseObj(response);
            CloseStreamsVO closeStreamsVO = new CloseStreamsVO();

            // 设置字段
            closeStreamsVO.setCode(responseJson.getInt("code"));
            closeStreamsVO.setCountHit(responseJson.getInt("count_hit"));
            closeStreamsVO.setCountClosed(responseJson.getInt("count_closed"));
            closeStreamsVO.setMessage(responseJson.getStr("message"));

            log.info("成功解析关闭流响应，命中{}个流，关闭{}个流", closeStreamsVO.getCountHit(), closeStreamsVO.getCountClosed());
            return closeStreamsVO;

        } catch (Exception e) {
            log.error("解析关闭流响应失败，错误信息: {}", e.getMessage(), e);
            throw new RuntimeException("解析关闭流响应失败: " + e.getMessage(), e);
        }
    }

}
