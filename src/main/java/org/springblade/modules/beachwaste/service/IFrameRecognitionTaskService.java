package org.springblade.modules.beachwaste.service;

import org.springblade.modules.beachwaste.pojo.dto.FrameRecognitionTaskDTO;
import org.springblade.modules.beachwaste.pojo.vo.FrameRecognitionTaskVO;

/**
 * 帧识别任务服务接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface IFrameRecognitionTaskService {

    /**
     * 启动帧识别任务
     *
     * @param taskDTO 任务参数
     * @return 启动结果
     */
    FrameRecognitionTaskVO startTask(FrameRecognitionTaskDTO taskDTO);

}
