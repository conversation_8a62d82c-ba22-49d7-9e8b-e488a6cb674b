package org.springblade.modules.beachwaste.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户事件统计VO
 * 用于封装当前登录用户的事件处理统计数据
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "用户事件统计结果")
public class UserEventStatsVO {

    /**
     * 该用户累计处理的事件数量
     */
    @Schema(description = "累计处理事件数量")
    private Integer totalEvents;

    /**
     * 该用户在当前自然月内处理的事件数量
     */
    @Schema(description = "当前月处理事件数量")
    private Integer monthlyEvents;

}
