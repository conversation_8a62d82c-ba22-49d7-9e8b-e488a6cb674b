package org.springblade.modules.beachwaste.pojo.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 关闭流响应VO
 * <AUTHOR>
@Data
public class CloseStreamsVO {

    /**
     * 响应状态码
     */
    @JsonProperty("code")
    private Integer code;

    /**
     * 命中的流数量
     */
    @JsonProperty("count_hit")
    private Integer countHit;

    /**
     * 被关闭的流数量
     */
    @JsonProperty("count_closed")
    private Integer countClosed;

    /**
     * 响应消息
     */
    @JsonProperty("message")
    private String message;

}