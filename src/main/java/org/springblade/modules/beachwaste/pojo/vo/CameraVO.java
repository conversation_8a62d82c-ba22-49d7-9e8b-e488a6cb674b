package org.springblade.modules.beachwaste.pojo.vo;

import lombok.Data;

/**
 * 摄像头信息VO
 * <AUTHOR>
@Data
public class CameraVO {

    /**
     * 设备SN号
     */
    private String sn;

    /**
     * 转换器名称
     */
    private String converterName;

    /**
     * 转换器ID
     */
    private String converterId;

    /**
     * 摄像头索引
     */
    private String cameraIndex;

    /**
     * 嵌套的URL
     */
    private String url;

	/**
	 * AI处理URL地址
	 */
	private String aiUrl;

    /**
     * 原始流地址
     */
    private String originStream;

    /**
     * AI处理流地址
     */
    private String aiStream;


}
