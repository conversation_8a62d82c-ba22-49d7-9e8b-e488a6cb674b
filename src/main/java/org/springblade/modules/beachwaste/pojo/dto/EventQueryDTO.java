package org.springblade.modules.beachwaste.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * 事件查询条件DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "事件查询条件")
public class EventQueryDTO {

    @Schema(description = "发现方式ID")
    private Long discoveryMethod;

    @Schema(description = "事件状态ID")
    private Long eventStatus;

    @Schema(description = "垃圾材质类型ID")
    private Long wasteMaterial;

    @Schema(description = "垃圾尺寸分类ID")
    private Long wasteSize;

    @Schema(description = "网格ID")
    private Long gridId;

    @Schema(description = "开始日期（格式：yyyy-MM-dd）")
    private LocalDate startDate;

    @Schema(description = "结束日期（格式：yyyy-MM-dd）")
    private LocalDate endDate;

    @Schema(description = "处理人员ID")
    private Long handlerStaffId;

    @Schema(description = "当前页码", defaultValue = "1")
    private Integer current = 1;

    @Schema(description = "每页数量", defaultValue = "10")
    private Integer size = 10;

	// 是否加载预览图
	@Schema(description = "是否加载预览图", defaultValue = "false")
	private Boolean loadPreview = false;

}
