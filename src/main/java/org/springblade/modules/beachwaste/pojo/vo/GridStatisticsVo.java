package org.springblade.modules.beachwaste.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 网格统计数据视图对象
 *
 * <AUTHOR>
@Data
public class GridStatisticsVo {

    /**
     * 网格总数
     */
    private Integer gridCount;

    /**
     * 网格总面积(km²)
     */
    private BigDecimal totalArea;

	/**
	 * 完成任务（次）
	 */
	private Long completedTaskCount;

	/**
	 * 总飞行时长（小时）
	 */
	private BigDecimal totalFlightTime;

	/**
	 * 总飞行里程（公里）
	 */
	private BigDecimal totalFlightDistance;

    /**
     * 发现事件总数
     */
    private Integer totalEvents;

    /**
     * 已处理事件数
     */
    private Integer processedEvents;

    /**
     * 平均处理时长(小时)
     */
    private BigDecimal avgProcessTime;

    /**
     * 网格员人数
     */
    private Integer gridStaffCount;

}
