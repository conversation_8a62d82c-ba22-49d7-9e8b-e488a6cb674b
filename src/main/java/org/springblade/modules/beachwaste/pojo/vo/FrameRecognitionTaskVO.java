package org.springblade.modules.beachwaste.pojo.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 帧识别任务响应对象
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Schema(description = "帧识别任务响应对象")
public class FrameRecognitionTaskVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 拉流状态码（例：0 成功；其他失败）
     */
    @Schema(description = "拉流状态码（例：0 成功；其他失败）", example = "0")
    @JsonProperty("code")
    private Integer code;

    /**
     * 返回执行状态信息
     */
    @Schema(description = "返回执行状态信息", example = "success")
    @JsonProperty("message")
    private String message;

    /**
     * 响应耗时（单位秒）
     */
    @Schema(description = "响应耗时（单位秒）", example = "0.01")
    @JsonProperty("consuming_time")
    private Float consumingTime;

    /**
     * 结果集，switch为false时，不提供data
     */
    @Schema(description = "结果集，switch为false时，不提供data")
    @JsonProperty("data")
    private Map<String, Object> data;

    /**
     * 提供视频推流地址（目前仅支持rtmp、rtsp格式）
     */
    @Schema(description = "提供视频推流地址（目前仅支持rtmp、rtsp格式）", example = "rtmp://127.0.0.1:1935/live/stream_002")
    @JsonProperty("stream_url_out")
    private String streamUrlOut;

}
