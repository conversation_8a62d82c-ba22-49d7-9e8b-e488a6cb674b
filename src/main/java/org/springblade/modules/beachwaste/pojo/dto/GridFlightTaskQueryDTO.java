package org.springblade.modules.beachwaste.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * 网格飞行任务查询参数DTO
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "网格飞行任务查询参数")
public class GridFlightTaskQueryDTO {

    /**
     * 年份
     */
    @Schema(description = "年份", example = "2023")
    @NotNull(message = "年份不能为空")
    @Min(value = 2000, message = "年份不能小于2000")
    @Max(value = 2100, message = "年份不能大于2100")
    private Integer year;

    /**
     * 月份
     */
    @Schema(description = "月份", example = "6")
    @NotNull(message = "月份不能为空")
    @Min(value = 1, message = "月份不能小于1")
    @Max(value = 12, message = "月份不能大于12")
    private Integer month;

}
