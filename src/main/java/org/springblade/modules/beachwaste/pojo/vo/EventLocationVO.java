package org.springblade.modules.beachwaste.pojo.vo;

import lombok.Data;

/**
 * 事件位置信息VO
 * 用于返回事件的经纬度坐标和ID信息，专门用于地图标注
 *
 * <AUTHOR>
 */
@Data
public class EventLocationVO {

    /**
     * 事件ID
     */
    private Long id;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 事件状态
     * 用于在地图上显示不同状态的标记样式
     */
    private Long eventStatus;

}
