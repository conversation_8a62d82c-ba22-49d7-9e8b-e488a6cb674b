package org.springblade.modules.beachwaste.pojo.entity.BaseEntity;

import java.util.List;

import de.micromata.opengis.kml.v_2_2_0.Coordinate;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 点对象
 * 空间对象常见的类型包括点（Point）、线（Polyline）、面（Polygon）三种类型
 * 这里我们将根据需要定义不同的空间对象
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KmlPoint extends KmlBaseEntity {
	private String color;

	public KmlPoint(List<Coordinate> points, String name, String description, String color) {
		super(points, name, description);
		this.color = color;
	}

	public KmlPoint() {
		super();
	}

}
