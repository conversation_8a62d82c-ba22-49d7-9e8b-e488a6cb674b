package org.springblade.modules.beachwaste.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 海滩垃圾媒体文件表
 *
 * <AUTHOR>
@Data
@TableName("beach_litter_media")
public class BeachLitterMedia implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 对象存储key
	 */
	@TableField("object_key")
	private String objectKey;

	/**
	 * 文件名称
	 */
	@TableField("file_name")
	private String fileName;

	/**
	 * 文件访问URL
	 */
	@TableField("file_url")
	private String fileUrl;

	/**
	 * 文件MD5值
	 */
	@TableField("file_md5")
	private String fileMd5;

	/**
	 * 文件类型
	 */
	@TableField("file_type")
	private String fileType;

	/**
	 * AI处理状态
	 */
	@TableField("ai_status")
	private Boolean aiStatus;

	/**
	 * AI处理结果
	 */
	@TableField("ai_result")
	private String aiResult;

	/**
	 * 图片像素尺寸
	 */
	@TableField("pixel_size")
	private Long pixelSize;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 状态
	 */
	@TableField("status")
	private Integer status;

	/**
	 * 是否删除
	 */
	@TableField("is_deleted")
	@TableLogic
	private Integer isDeleted;

	/**
	 * 地理坐标（经度，纬度）
	 */
	@TableField("location")
	private String location;

	@TableField("task_name")
	private String taskName;

	@TableField("task_uuid")
	private String taskUuid;

	@TableField("fh_create_time")
	private LocalDateTime fhCreateTime;

	@TableField("device_name")
	private String deviceName;

}
