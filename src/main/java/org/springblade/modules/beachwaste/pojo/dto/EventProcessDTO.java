package org.springblade.modules.beachwaste.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 事件处置请求DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "事件处置请求参数")
public class EventProcessDTO {

    /**
     * 事件ID
     */
    @NotNull(message = "事件ID不能为空")
    @Schema(description = "事件ID", required = true)
    private Long eventId;

    /**
     * 处置状态：2-已处理，3-误报，4-未找到
     */
    @NotNull(message = "处置状态不能为空")
    @Schema(description = "处置状态：2-已处理，3-误报，4-未找到", required = true)
    private Long status;

    /**
     * 当前位置经度
     */
    @NotNull(message = "经度不能为空")
    @Schema(description = "当前位置经度", required = true)
    private BigDecimal longitude;

    /**
     * 当前位置纬度
     */
    @NotNull(message = "纬度不能为空")
    @Schema(description = "当前位置纬度", required = true)
    private BigDecimal latitude;

    /**
     * 处置后的图片ObjectKey（仅当状态为已处理时需要）
     */
    @Schema(description = "处置后的图片ObjectKey")
    private String processedImageObjectKey;

}