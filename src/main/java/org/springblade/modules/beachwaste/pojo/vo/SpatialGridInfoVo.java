package org.springblade.modules.beachwaste.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 空间网格信息（用于KML文件解析结果）
 */
@Data
public class SpatialGridInfoVo {

    /**
     * 要素名称（从KML的name标签获取）
     */
    private String gridName;

    /**
     * 几何类型（Point, LineString, Polygon）
     */
    private String geomType;

    /**
     * 几何类型中文名称（点、线、面）
     */
    private String geomName;

    /**
     * 网格几何形状(WGS84坐标系) geometry数据
     */
    private String gridGeom;

    /**
     * 地理位置信息的 JSON 字符串
     */
    private String geomJson;

    /**
     * 网格面积(平方米)
     */
    private BigDecimal gridArea;

    /**
     * 原始坐标数据
     */
    private String[] coordinates;
}
