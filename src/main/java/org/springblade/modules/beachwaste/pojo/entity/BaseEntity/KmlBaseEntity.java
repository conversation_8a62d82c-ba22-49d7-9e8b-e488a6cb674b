package org.springblade.modules.beachwaste.pojo.entity.BaseEntity;

import de.micromata.opengis.kml.v_2_2_0.Coordinate;
import lombok.Data;

import java.util.List;

/**
 * kml 基类，将name、description、List<Coordinate>进行统一封装
 *
 * <AUTHOR>
 */
@Data
public class KmlBaseEntity {

	private List<Coordinate> points;
	private String name;
	private String description;

	public KmlBaseEntity(List<Coordinate> points, String name, String description) {
		super();
		this.points = points;
		this.name = name;
		this.description = description;
	}

	public KmlBaseEntity() {
		super();
	}
}
