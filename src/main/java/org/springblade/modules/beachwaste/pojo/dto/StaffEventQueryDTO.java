package org.springblade.modules.beachwaste.pojo.dto;

import lombok.Data;
import jakarta.validation.constraints.Pattern;

/**
 * 网格员事件查询参数DTO
 * 用于封装当前网格员查询关联事件的查询条件
 *
 * <AUTHOR>
@Data
public class StaffEventQueryDTO {

    /**
     * 事件处置状态（可选）
     */
    private Long eventStatus;

    /**
     * 开始日期（可选，格式：yyyy-MM-dd）
     */
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "开始日期格式不正确，应为yyyy-MM-dd")
    private String startDate;

    /**
     * 结束日期（可选，格式：yyyy-MM-dd）
     */
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "结束日期格式不正确，应为yyyy-MM-dd")
    private String endDate;

    /**
     * 当前页码，默认为1
     */
    private Integer page = 1;

    /**
     * 每页记录数，默认为10
     */
    private Integer size = 10;
}