package org.springblade.modules.beachwaste.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springblade.modules.beachwaste.pojo.entity.GridSchedule;

import java.util.List;

/**
 * 网格排班多时间段数据传输对象
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "网格排班多时间段数据传输对象")
public class GridScheduleMultiRangeDTO {

    @Schema(description = "网格ID")
    private Long gridId;

    @Schema(description = "人员ID")
    private Long staffId;

    @Schema(description = "人员姓名")
    private String staffName;

    @Schema(description = "备注信息")
    private String remark;

    @Schema(description = "时间段列表")
    private List<DateRange> dateRanges;

    /**
     * 时间段内部类
     */
    @Data
    @Schema(description = "时间段")
    public static class DateRange {
        @Schema(description = "开始日期 (格式: yyyy-MM-dd)")
        private String startDate;

        @Schema(description = "结束日期 (格式: yyyy-MM-dd)")
        private String endDate;
    }
}