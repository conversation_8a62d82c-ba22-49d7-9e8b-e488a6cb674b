package org.springblade.modules.beachwaste.pojo.dto;

import lombok.Data;

import java.util.List;

/**
 * 垃圾检测结果数据传输对象
 */
@Data
public class DetectResultDTO {

    /**
     * 垃圾分类ID
     */
    private Integer classId;

    /**
     * 文件MD5值
     */
    private String fileMd5;

    /**
     * 垃圾标识框 [起点X坐标值,起点Y坐标值,终点X坐标值,终点Y坐标值]
     */
    private List<Integer> box;

    /**
     * 垃圾标识框-水平宽-像素
     */
    private Integer bboxWidth;

    /**
     * 垃圾标识框-垂直高度-像素
     */
    private Integer bboxHeight;

    /**
     * 垃圾可信度
     */
    private Double confidence;

    /**
     * 垃圾分类英文名
     */
    private String enName;

    /**
     * 垃圾分类名称
     */
    private String cnName;

	/**
	 * 垃圾尺寸ID
	 */
	private Integer sizeClassId;

	/**
	 * 经度
	 */
	private String lat;

	/**
	 * 纬度
	 */
	private String lon;

    /**
     * AI检测结果信息数组
     */
    private List<Object> info;

}
