package org.springblade.modules.beachwaste.pojo.vo;

import lombok.Data;

/**
 * 流媒体源类型信息VO
 * 
 * <p>用于描述流媒体的来源信息，包括网络连接参数、IP地址、端口等网络配置</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class OriginTypeVO {

    /**
     * 唯一标识符
     */
    private String identifier;

    /**
     * 本地IP地址
     */
    private String localIp;

    /**
     * 本地端口号
     */
    private Integer localPort;

    /**
     * 对端IP地址
     */
    private String peerIp;

    /**
     * 对端端口号
     */
    private Integer peerPort;

    /**
     * 源类型描述字符串
     */
    private String originTypeStr;

    /**
     * 源URL地址
     */
    private String originUrl;
}