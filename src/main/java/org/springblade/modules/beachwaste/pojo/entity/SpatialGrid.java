package org.springblade.modules.beachwaste.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.WKTWriter;
import org.springblade.core.mp.base.BaseEntity;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 网格信息表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("spatial_grid")
public class SpatialGrid extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 网格编码
     */
    private String gridCode;

    /**
     * 网格名称
     */
    private String gridName;

    /**
     * 网格几何形状(WGS84坐标系) geometry数据
     * 使用JsonIgnore避免Jackson序列化时的循环引用问题
     */
    @JsonIgnore
    private Geometry gridGeom;

    /**
     * 地理位置信息的 JSON 字符串
     */
    private String geomJson;

    /**
     * 几何类型（Point, LineString, Polygon）
     */
    private String geomType;

    /**
     * 网格面积(平方米)
     */
    private BigDecimal gridArea;

    /**
     * 管理员 ID
     */
    private Long userId;

    /**
     * 获取可序列化的几何信息
     * 返回包含WKT、几何类型、面积等信息的Map对象，用于JSON序列化
     *
     * @return 包含几何信息的Map，如果gridGeom为null则返回null
     */
    @JsonProperty("gridGeomInfo")
    public Map<String, Object> getGridGeomInfo() {
        if (gridGeom == null || gridGeom.isEmpty()) {
            return null;
        }

        try {
            Map<String, Object> geomInfo = new HashMap<>();

            // 添加几何类型
            geomInfo.put("type", gridGeom.getGeometryType());

            // 添加WKT格式的几何数据
            WKTWriter wktWriter = new WKTWriter();
            geomInfo.put("wkt", wktWriter.write(gridGeom));

            // 添加SRID信息
            if (gridGeom.getSRID() != 0) {
                geomInfo.put("srid", gridGeom.getSRID());
            }

            // 添加边界框信息
            if (gridGeom.getEnvelopeInternal() != null) {
                Map<String, Double> envelope = new HashMap<>();
                envelope.put("minX", gridGeom.getEnvelopeInternal().getMinX());
                envelope.put("minY", gridGeom.getEnvelopeInternal().getMinY());
                envelope.put("maxX", gridGeom.getEnvelopeInternal().getMaxX());
                envelope.put("maxY", gridGeom.getEnvelopeInternal().getMaxY());
                geomInfo.put("envelope", envelope);
            }

            // 添加面积信息（如果已计算）
            if (gridArea != null) {
                geomInfo.put("area", gridArea);
            }

            return geomInfo;
        } catch (Exception e) {
            return null;
        }
    }

}
