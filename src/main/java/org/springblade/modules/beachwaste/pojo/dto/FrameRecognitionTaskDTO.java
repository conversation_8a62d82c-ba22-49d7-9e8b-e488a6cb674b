package org.springblade.modules.beachwaste.pojo.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.logging.log4j.core.config.plugins.validation.constraints.NotBlank;

import java.io.Serializable;

/**
 * 帧识别任务数据传输对象
 *
 * <AUTHOR> @since
 */
@Data
@Schema(description = "帧识别任务数据传输对象")
public class FrameRecognitionTaskDTO implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
     * 算法编码（只支持单一算法）
     */
	@Schema(description = "算法编码（只支持单一算法）", required = true, example = "string")
	@NotBlank(message = "算法编码不能为空")
	@JsonProperty("kind")
	private String kind;


    /**
     * 提供待处理的视频流地址
     */
    @Schema(description = "提供待处理的视频流地址（目前仅支持rtmp、rtsp格式）", required = true, example = "rtsp://*************:554/stream")
    @NotBlank(message = "提供待处理的视频流地址不能为空")
	@JsonProperty("stream_url_in")
    private String streamUrlIn;

	/**
	 * 提供视频推流地址
	 */
	@Schema(description = "提供视频推流地址（目前仅支持rtmp、rtsp格式）", required = true, example = "rtmp://*************:1935/live/stream")
	@NotBlank(message = "提供视频推流地址不能为空")
	@JsonProperty("stream_url_out")
	private String streamUrlOut;

	/**
	 * 无人机sn码
	 */
	@Schema(description = "无人机sn码", required = true, example = "string")
	@NotBlank(message = "无人机sn码不能为空")
	@JsonProperty("sn")
	private String sn;

	/**
	 * 视频推送开关
	 * true-开启推流服务
	 * false-关闭推流服务
	 */
	@Schema(description = "视频推送开关", required = true, example = "true")
	@NotBlank(message = "视频推送开关不能为空")
	@JsonProperty("switch")
	private Boolean streamSwitch;

}
