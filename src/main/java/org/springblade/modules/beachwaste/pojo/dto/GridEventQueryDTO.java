package org.springblade.modules.beachwaste.pojo.dto;

import lombok.Data;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.math.BigDecimal;

/**
 * 网格事件查询参数DTO
 * 用于封装根据网格ID查询相关事件的查询条件
 *
 * <AUTHOR>
@Data
public class GridEventQueryDTO {

    /**
     * 网格ID
     */
    @NotNull(message = "网格ID不能为空")
    private Long gridId;

    /**
     * 事件处置状态（可选）
     */
    private Long eventStatus;

    /**
     * 发现方式（可选）
     */
    private Long discoveryMethod;

    /**
     * 开始日期（可选，格式：yyyy-MM-dd）
     */
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "开始日期格式不正确，应为yyyy-MM-dd")
    private String startDate;

    /**
     * 结束日期（可选，格式：yyyy-MM-dd）
     */
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "结束日期格式不正确，应为yyyy-MM-dd")
    private String endDate;

    /**
     * 置信度（可选）
     */
    private BigDecimal confidence;
}
