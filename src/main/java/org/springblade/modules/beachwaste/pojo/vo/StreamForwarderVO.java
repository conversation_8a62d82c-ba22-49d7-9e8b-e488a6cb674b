package org.springblade.modules.beachwaste.pojo.vo;

import lombok.Data;

/**
 * 码流转发器响应VO
 * <AUTHOR>
@Data
public class StreamForwarderVO {

    /**
     * 设备SN号
     */
    private String sn;

    /**
     * 转换器名称
     */
    private String converterName;

    /**
     * 转换器ID
     */
    private String converterId;

    /**
     * 摄像头索引
     */
    private String cameraIndex;

    /**
     * 嵌套的URL
     */
    private String url;

}
