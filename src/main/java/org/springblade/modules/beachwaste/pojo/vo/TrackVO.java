package org.springblade.modules.beachwaste.pojo.vo;

import lombok.Data;

/**
 * 流媒体轨道信息VO
 * 
 * <p>用于描述流媒体的轨道详细信息，包括音视频编码参数、帧率、分辨率等技术参数</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class TrackVO {

    /**
     * 通道数
     */
    private Integer channels;

    /**
     * 编解码器ID
     */
    private Integer codecId;

    /**
     * 编解码器ID名称
     */
    private String codecIdName;

    /**
     * 编解码器类型
     */
    private Integer codecType;

    /**
     * 帧率（FPS）
     */
    private Integer fps;

    /**
     * 是否就绪
     */
    private Boolean ready;

    /**
     * 总帧数
     */
    private Integer frames;

    /**
     * 采样位数
     */
    private Integer sampleBit;

    /**
     * 采样率
     */
    private Integer sampleRate;

    /**
     * GOP间隔时间（毫秒）
     */
    private Integer gopIntervalMs;

    /**
     * GOP大小
     */
    private Integer gopSize;

    /**
     * 关键帧数量
     */
    private Integer keyFrames;

    /**
     * 视频高度（像素）
     */
    private Integer height;

    /**
     * 备用就绪状态
     */
    private Boolean ready2;

    /**
     * 视频宽度（像素）
     */
    private Integer width;
}