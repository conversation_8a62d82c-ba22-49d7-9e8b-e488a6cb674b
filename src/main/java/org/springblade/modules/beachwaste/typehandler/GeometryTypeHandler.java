package org.springblade.modules.beachwaste.typehandler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.io.WKTWriter;
import org.postgis.PGgeometry;
import org.postgresql.util.PGobject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;

/**
 * 用于JTS几何形状和后GIS几何转换的Mybatis TypeHandler处理JTS几何对象和GostGIS几何类型之间的转换，
 * 支持所有几何类型：点，linestring，linestring，polygon，polygon，多边形，多重点，MultiNeString，Multipolygon等
 */
@MappedJdbcTypes(JdbcType.OTHER)
@MappedTypes(Geometry.class)
public class GeometryTypeHandler extends BaseTypeHandler<Geometry> {

    private static final Logger logger = LoggerFactory.getLogger(GeometryTypeHandler.class);
    private static final WKTReader wktReader = new WKTReader();
    private static final WKTWriter wktWriter = new WKTWriter();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Geometry parameter, JdbcType jdbcType) throws SQLException {
        try {
            // Convert JTS Geometry to WKT string and create PGobject
            String wkt = wktWriter.write(parameter);
            PGobject pgObject = new PGobject();
            pgObject.setType("geometry");
            pgObject.setValue(wkt);
            ps.setObject(i, pgObject);

            if (logger.isDebugEnabled()) {
                logger.debug("Set Geometry parameter: {} as WKT: {}", parameter.getGeometryType(), wkt);
            }
        } catch (Exception e) {
            logger.error("Error setting Geometry parameter: {}", e.getMessage(), e);
            throw new SQLException("Cannot convert Geometry to PostGIS geometry", e);
        }
    }

    @Override
    public Geometry getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Object obj = rs.getObject(columnName);
        if (obj == null) {
            return null;
        }
        return convertToGeometry(obj, "column[" + columnName + "]");
    }

    @Override
    public Geometry getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Object obj = rs.getObject(columnIndex);
        if (obj == null) {
            return null;
        }
        return convertToGeometry(obj, "column[" + columnIndex + "]");
    }

    @Override
    public Geometry getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Object obj = cs.getObject(columnIndex);
        if (obj == null) {
            return null;
        }
        return convertToGeometry(obj, "callable[" + columnIndex + "]");
    }

    /**
     * Convert database object to JTS Geometry
     * @param obj Database returned object
     * @param context Context information for logging
     * @return JTS Geometry object
     * @throws SQLException Thrown when conversion fails
     */
    private Geometry convertToGeometry(Object obj, String context) throws SQLException {
        if (obj == null) {
            return null;
        }

        try {
            String wktString = null;

            if (obj instanceof PGgeometry) {
                // Handle PGgeometry objects
                PGgeometry pgGeometry = (PGgeometry) obj;
                org.postgis.Geometry postgisGeom = pgGeometry.getGeometry();

                // Convert PostGIS Geometry to WKT
                wktString = postgisGeom.toString();

                if (logger.isDebugEnabled()) {
                    logger.debug("PostGIS geometry from {}: type={}, wkt={}",
                               context, postgisGeom.getType(), wktString);
                }
            } else if (obj instanceof PGobject) {
                // Handle PGobject (common case for PostGIS)
                PGobject pgObject = (PGobject) obj;
                if ("geometry".equals(pgObject.getType())) {
                    wktString = pgObject.getValue();
                } else {
                    logger.warn("PGobject from {} is not geometry type: {}", context, pgObject.getType());
                    throw new SQLException("Expected geometry PGobject, but got: " + pgObject.getType());
                }
            } else if (obj instanceof String) {
                // Handle direct WKT string
                wktString = (String) obj;
            } else if (obj instanceof byte[]) {
                // Handle binary geometry data (WKB format)
                try {
                    byte[] wkbData = (byte[]) obj;
                    if (logger.isDebugEnabled()) {
                        logger.debug("Converting WKB data from {}, length: {}", context, wkbData.length);
                    }

                    // Try to parse as WKB using PostGIS
                    PGgeometry pgGeometry = new PGgeometry(Arrays.toString(wkbData));
                    wktString = pgGeometry.getGeometry().toString();

                    if (logger.isDebugEnabled()) {
                        logger.debug("Converted WKB to WKT from {}: {}", context, wktString);
                    }
                } catch (Exception wkbException) {
                    logger.error("Failed to parse WKB data from {}: {}", context, wkbException.getMessage());
                    throw new SQLException("Failed to parse WKB data: " + wkbException.getMessage(), wkbException);
                }
            } else {
                logger.error("Unsupported database object type: {} from {}", obj.getClass().getName(), context);
                throw new SQLException("Unsupported database object type: " + obj.getClass().getName());
            }

            // Parse WKT string to JTS Geometry
            if (wktString != null && !wktString.trim().isEmpty()) {
                // Clean up the WKT string - remove any potential problematic characters
                String cleanWkt = cleanWktString(wktString);

                if (logger.isDebugEnabled()) {
                    logger.debug("Parsing WKT from {}: {}", context, cleanWkt);
                }

                Geometry geometry = wktReader.read(cleanWkt);

                if (logger.isDebugEnabled()) {
                    logger.debug("Successfully converted Geometry from {}: type={}, area={}",
                               context, geometry.getGeometryType(),
                               geometry.getArea() > 0 ? geometry.getArea() : "N/A");
                }
                return geometry;
            } else {
                logger.warn("Empty or null WKT string from {}", context);
                return null;
            }

        } catch (ParseException e) {
            logger.error("Error parsing WKT string from {}: {}", context, e.getMessage(), e);
            throw new SQLException("Failed to parse WKT string: " + e.getMessage(), e);
        } catch (SQLException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Unexpected error converting Geometry from {}: {}", context, e.getMessage(), e);
            throw new SQLException("Error converting Geometry: " + e.getMessage(), e);
        }
    }

    /**
     * Clean WKT string to remove potential problematic characters
     * @param wktString Original WKT string
     * @return Cleaned WKT string
     */
    private String cleanWktString(String wktString) {
        if (wktString == null) {
            return null;
        }

        // Remove any SRID prefix if present (e.g., "SRID=4326;POLYGON(...)")
        String cleaned = wktString;
        if (cleaned.contains("SRID=")) {
            int semiColonIndex = cleaned.indexOf(';');
            if (semiColonIndex > 0 && semiColonIndex < cleaned.length() - 1) {
                cleaned = cleaned.substring(semiColonIndex + 1);
                if (logger.isDebugEnabled()) {
                    logger.debug("Removed SRID prefix, cleaned WKT: {}", cleaned);
                }
            }
        }

        // Trim whitespace
        cleaned = cleaned.trim();

        return cleaned;
    }
}
