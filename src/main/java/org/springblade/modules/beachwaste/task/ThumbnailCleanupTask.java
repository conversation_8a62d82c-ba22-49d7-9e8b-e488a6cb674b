package org.springblade.modules.beachwaste.task;

import io.minio.ListObjectsArgs;
import io.minio.MinioClient;
import io.minio.RemoveObjectArgs;
import io.minio.Result;
import io.minio.messages.Item;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.config.ThumbnailConfig;
import org.springblade.modules.beachwaste.util.BeachWasteMinioClientUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 缩略图清理定时任务
 * 定期清理过期的缩略图文件
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
@ConditionalOnProperty(name = "beachwaste.thumbnail.cleanup-enabled", havingValue = "true", matchIfMissing = true)
public class ThumbnailCleanupTask {

    private final BeachWasteMinioClientUtil beachWasteMinioClientUtil;
    private final ThumbnailConfig thumbnailConfig;

    /**
     * 定时清理缩略图
     * 每天凌晨2点执行，删除当天所有缩略图
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredThumbnails() {
        log.info("开始执行缩略图清理任务");
        
        try {
            MinioClient minioClient = beachWasteMinioClientUtil.getMinioClient();
            String bucketName = beachWasteMinioClientUtil.getBucketName();
            String thumbnailPrefix = thumbnailConfig.getStoragePathPrefix();
            
            log.info("清理缩略图文件夹: {}", thumbnailPrefix);
            
            // 扫描所有缩略图文件
            List<String> allFiles = new ArrayList<>();
            Iterable<Result<Item>> results = minioClient.listObjects(
                ListObjectsArgs.builder()
                    .bucket(bucketName)
                    .prefix(thumbnailPrefix)
                    .recursive(true)
                    .build()
            );
            
            for (Result<Item> result : results) {
                Item item = result.get();
                allFiles.add(item.objectName());
            }
            
            log.info("发现{}个缩略图文件", allFiles.size());
            
            // 删除所有缩略图文件
            int deletedCount = 0;
            for (String objectName : allFiles) {
                try {
                    minioClient.removeObject(
                        RemoveObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build()
                    );
                    deletedCount++;
                } catch (Exception e) {
                    log.error("删除文件失败: {}", objectName, e);
                }
            }
            
            log.info("缩略图清理任务完成，成功删除{}个文件", deletedCount);
            
        } catch (Exception e) {
            log.error("缩略图清理任务执行失败", e);
        }
    }

    /**
     * 手动触发清理任务（用于测试或紧急清理）
     */
    public void manualCleanup() {
        log.info("手动触发缩略图清理任务");
        cleanupExpiredThumbnails();
    }
}
