package org.springblade.modules.beachwaste.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.pojo.vo.CloseStreamsVO;
import org.springblade.modules.beachwaste.pojo.vo.MediaItemVO;
import org.springblade.modules.beachwaste.pojo.vo.MediaListVO;
import org.springblade.modules.beachwaste.service.IVideoStreamService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 流媒体列表定时任务
 * 定时获取流媒体列表数据
 *
 * <AUTHOR>
@Slf4j
@Component
@RequiredArgsConstructor
public class MediaListScheduledTask {

    private final IVideoStreamService videoStreamService;

    /**
     * 定时获取流媒体列表并关闭观看人数为0的流
     * 每5分钟执行一次
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void fetchMediaListAndCloseZeroViewerStreams() {
        try {
            log.info("开始执行流媒体列表定时任务并关闭零观看者流");

            // 获取所有流媒体列表（不传参数获取全部）
            MediaListVO mediaList = videoStreamService.getMediaList(null, null, null, null);

            if (mediaList != null && mediaList.getCode() == 0 && mediaList.getData() != null) {
                log.info("成功获取流媒体列表，数据条数: {}", mediaList.getData().size());

                // 筛选出观看人数为0的流
                List<MediaItemVO> zeroViewerStreams = mediaList.getData().stream()
                    .filter(item -> item.getReaderCount() != null && item.getReaderCount() == 0)
                    .collect(Collectors.toList());

                log.info("发现{}个观看人数为0的流", zeroViewerStreams.size());

                // 关闭观看人数为0的流
                for (MediaItemVO stream : zeroViewerStreams) {
                    try {
                        log.info("准备关闭零观看者流: schema={}, vhost={}, app={}, stream={}",
                            stream.getSchema(), stream.getVhost(), stream.getApp(), stream.getStream());

                        CloseStreamsVO closeResult = videoStreamService.closeStreams(
                            stream.getSchema(),
                            stream.getVhost(),
                            stream.getApp(),
                            stream.getStream(),
                            false // 不强制关闭
                        );

                        if (closeResult != null && closeResult.getCode() == 0) {
                            log.info("成功关闭流: {}/{}/{}/{}, 命中{}个，关闭{}个",
                                stream.getSchema(), stream.getVhost(), stream.getApp(), stream.getStream(),
                                closeResult.getCountHit(), closeResult.getCountClosed());
                        } else {
                            log.warn("关闭流失败: {}/{}/{}/{}, 返回码: {}, 消息: {}",
                                stream.getSchema(), stream.getVhost(), stream.getApp(), stream.getStream(),
                                closeResult != null ? closeResult.getCode() : "null",
                                closeResult != null ? closeResult.getMessage() : "响应为空");
                        }
                    } catch (Exception e) {
                        log.error("关闭流时发生异常: {}/{}/{}/{}",
                            stream.getSchema(), stream.getVhost(), stream.getApp(), stream.getStream(), e);
                    }
                }

            } else {
                log.warn("获取流媒体列表失败，返回码: {}, 消息: {}",
                    mediaList != null ? mediaList.getCode() : "null",
                    mediaList != null ? mediaList.getMessage() : "响应为空");
            }

        } catch (Exception e) {
            log.error("执行流媒体列表定时任务时发生异常", e);
        }
    }

}
