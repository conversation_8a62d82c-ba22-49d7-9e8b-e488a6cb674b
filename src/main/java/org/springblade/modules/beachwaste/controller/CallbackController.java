package org.springblade.modules.beachwaste.controller;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.constant.AuthConstant;
import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.mapper.BeachLitterMediaMapper;
import org.springblade.modules.beachwaste.pojo.entity.BeachLitterMedia;
import org.springblade.modules.fh.mqtt.handler.events.IDetectCallbackService;
import org.springblade.modules.fh.mqtt.handler.util.GeometryUtil;
import org.springblade.modules.fh.service.IFhMinioService;
import org.springblade.modules.fh.utils.FhOpenApiHttpUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 第三方回调接口控制器
 */
@Slf4j
@RestController
@RequestMapping("/detect/callback")
@RequiredArgsConstructor
@PreAuth(AuthConstant.PERMIT_ALL)
@Tag(name = "AI识别回调接口", description = "处理AI识别结果回调相关接口")
public class CallbackController {

	@Resource
	private IDetectCallbackService detectCallbackService;

	@Resource
	private IFhMinioService fhMinioService;

	@Resource
	private BeachLitterMediaMapper beachLitterMediaMapper;

	@Value("${ai-detect.url}")
    private String aiDetectUrl;

    @Value("${ai-detect.callback-url}")
    private String aiCallbackUrl;

	@Value("${media.pixel-size:2}")
	private Long mediaPixelSize;

    /**
     * 处理检测批量回调，接收"/detect_batch"服务的回调数据
     * 支持两种格式：
     * 1. 数组格式：直接传递DetectResultDTO对象的数组
     * 2. 对象格式：包含records字段的对象，records字段值为DetectResultDTO对象的数组
     *
     * @param requestBody 请求体，可以是数组或对象
     * @return 处理结果
     */
    @PostMapping("/detect-batch")
	public R<String> handleDetectBatchCallback(@RequestBody Object requestBody) {
		return detectCallbackService.handleDetectBatchCallback(requestBody);
	}

	/**
	 * 测试AI识别接口
	 *
	 * @param objectKey 文件对象Key
	 * @return 处理结果
	 */
	@Deprecated
	@PostMapping("/sendAiTest")
	public R<String> AIConnect(@RequestParam("objectKey") String objectKey) {
		if (objectKey == null || objectKey.trim().isEmpty()) {
			return R.fail("objectKey参数不能为空");
		}
		try {
            String fileUrl = fhMinioService.getObjectUrl(objectKey);

			 // 获取文件MD5值
            String fileMd5 = DigestUtils.md5Hex(objectKey);

            // 从objectKey中提取文件名和文件类型
            String fileName = objectKey.substring(objectKey.lastIndexOf("/") + 1);
            String fileType = fileName.substring(fileName.lastIndexOf(".") + 1);

            // 保存媒体文件信息
            try {
                BeachLitterMedia mediaData = new BeachLitterMedia();
                mediaData.setObjectKey(objectKey);
                mediaData.setFileUrl(fileUrl);
                mediaData.setFileName(fileName);
                mediaData.setFileMd5(fileMd5);
                mediaData.setFileType(fileType);
                mediaData.setPixelSize(2L);
                // AI状态默认为false
                mediaData.setAiStatus(false);

                beachLitterMediaMapper.insert(mediaData);
                log.info("媒体数据持久化成功，objectKey: {}", objectKey);
            } catch (Exception e) {
                log.error("媒体数据持久化异常，objectKey: {}, fileUrl: {}", objectKey, fileUrl, e);
                // 继续执行AI检测，不因数据保存失败而中断
            }

            // 构建AI识别接口请求参数，符合OpenAPI文档定义
            List<Map<String, Object>> detectRequestList = new ArrayList<>(1);
            Map<String, Object> detectRequest = new HashMap<>(4);
            detectRequest.put("filePath", fileUrl);
            // 实际应为文件MD5字符串
            detectRequest.put("fileMd5", fileMd5);
            // 使用配置的回调地址
            detectRequest.put("callbackUrl", aiCallbackUrl);
            detectRequest.put("pixelSize", mediaPixelSize);
            detectRequestList.add(detectRequest);

            try {
                // 直接使用完整URL地址发送请求
                String post = HttpRequest.post(aiDetectUrl)
                    .header(FhOpenApiHttpUtil.X_REQUEST_ID, UUID.randomUUID().toString())
                    .body(JSONObject.toJSONString(detectRequestList))
                    .timeout(60000)
                    .execute().body();
				log.info("post:>>  " + post);
				return R.success("AI检测请求已发送");
			} catch (Exception e) {
				log.error("AI检测接口请求异常", e);
				return R.fail("AI检测接口请求异常");
			}

		} catch (Exception e) {
			log.error("获取文件URL失败", e);
			return R.fail("获取文件URL失败: " + e.getMessage());
		}
	}


	/**
	 * 根据经纬度获取网格ID
	 * 示例: /getId?lon=116.404&lat=39.915
	 */
	@Deprecated
	@RequestMapping(value = "/getId", method = RequestMethod.GET)
	public String getGridIdAndLocation(@RequestParam(name = "lon", required = true) String lon,
                                   @RequestParam(name = "lat", required = true) String lat)
	{

        try {
            // 参数校验
            if (lon == null || lat == null || lon.isEmpty() || lat.isEmpty()) {
                return "经纬度参数不能为空";
            }

            double longitude = Double.parseDouble(lon);
            double latitude = Double.parseDouble(lat);

            // 使用几何工具类计算网格ID并转换为长整型，设置到事件对象中
            Long l = Long.valueOf(GeometryUtil.getGridId(String.valueOf(longitude), String.valueOf(latitude)));

            return l.toString();
        } catch (NumberFormatException e) {
            return "经纬度格式不正确";
        } catch (Exception e) {
            return "发生未知错误";
        }
	}


}
