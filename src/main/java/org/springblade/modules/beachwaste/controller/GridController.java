package org.springblade.modules.beachwaste.controller;

import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.pojo.dto.GridFlightTaskQueryDTO;
import org.springblade.modules.beachwaste.pojo.vo.GridStatisticsVo;
import org.springblade.modules.beachwaste.service.IGridService;
import org.springblade.modules.fh.pojo.entity.FhFlightTask;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/beach-waste/grid")
@RequiredArgsConstructor
public class GridController {

    private final IGridService gridService;

    /**
     * 获取网格统计数据
     *
     * @return 统计数据包装对象
     */
	@GetMapping("/statistics")
    public R<GridStatisticsVo> getGridStatistics() {
        return R.data(gridService.getGridStatistics());
    }


    /**
     * 根据年份、月份和网格ID获取飞行任务数据
     *
     * @param queryDTO 包含年份、月份和网格ID的查询参数
     * @return 按月份分组的飞行任务列表
     */
    @PostMapping("/flight-tasks")
    @Operation(summary = "获取网格飞行任务", description = "根据年份、月份和网格ID获取飞行任务数据")
    public R<Map<String, List<FhFlightTask>>> getGridFlightTasksByYearMonth(@RequestBody @Validated GridFlightTaskQueryDTO queryDTO) {
        return R.data(gridService.getGridFlightTasksByYearMonth(queryDTO));
    }

}
