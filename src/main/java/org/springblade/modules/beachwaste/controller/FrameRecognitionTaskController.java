package org.springblade.modules.beachwaste.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.pojo.dto.FrameRecognitionTaskDTO;
import org.springblade.modules.beachwaste.pojo.vo.FrameRecognitionTaskVO;
import org.springblade.modules.beachwaste.service.IFrameRecognitionTaskService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 帧识别任务管理控制器
 * 提供帧识别任务的启动功能
 *
 * <AUTHOR>
@Slf4j
@RestController
@RequestMapping("/frame")
@RequiredArgsConstructor
@Validated
@Tag(name = "帧识别任务管理", description = "帧识别任务的启动操作接口")
public class FrameRecognitionTaskController {

    private final IFrameRecognitionTaskService frameRecognitionTaskService;

    /**
     * 启动帧识别任务
     *
     * @param taskDTO 任务参数
     * @return 启动结果
     */
    @PostMapping("/frame_recognition")
    @Operation(summary = "启动帧识别任务", description = "启动指定的帧识别任务")
    public FrameRecognitionTaskVO frameRecognition(@RequestBody FrameRecognitionTaskDTO taskDTO) {
        return frameRecognitionTaskService.startTask(taskDTO);
    }


}
