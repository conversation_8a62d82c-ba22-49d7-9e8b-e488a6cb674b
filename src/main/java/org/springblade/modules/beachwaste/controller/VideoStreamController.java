package org.springblade.modules.beachwaste.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.pojo.vo.CameraVO;
import org.springblade.modules.beachwaste.pojo.vo.CloseStreamsVO;
import org.springblade.modules.beachwaste.pojo.vo.MediaListVO;
import org.springblade.modules.beachwaste.service.IVideoStreamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 视频流媒体数据处理控制器
 * <AUTHOR>
@RestController
@RequestMapping("/video-stream")
@Tag(name = "视频流媒体数据", description = "视频流媒体数据处理相关接口")
public class VideoStreamController {

    @Autowired
    private IVideoStreamService videoStreamService;

    /**
     * 获取所有摄像头列表
     */
    @GetMapping("/camera-list")
    @Operation(summary = "获取所有摄像头列表")
    public R<List<CameraVO>> getCameraList() {
        try {
            List<CameraVO> cameraList = videoStreamService.getCameraList();
            return R.data(cameraList);
        } catch (Exception e) {
            return R.fail("获取摄像头列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取流媒体列表
     */
    @GetMapping("/media-list")
    @Operation(summary = "获取流媒体列表", description = "获取流媒体服务器上的媒体流列表，支持筛选条件")
    public R<MediaListVO> getMediaList(
            @Parameter(description = "筛选协议，例如 rtsp 或 rtmp")
            @RequestParam(value = "schema", required = false) String schema,
            
            @Parameter(description = "筛选虚拟主机，例如 __defaultVhost__")
            @RequestParam(value = "vhost", required = false) String vhost,
            
            @Parameter(description = "筛选应用名，例如 live")
            @RequestParam(value = "app", required = false) String app,
            
            @Parameter(description = "筛选流id，例如 test")
            @RequestParam(value = "stream", required = false) String stream
    ) {
        try {
            MediaListVO mediaList = videoStreamService.getMediaList(schema, vhost, app, stream);
            return R.data(mediaList);
        } catch (Exception e) {
            return R.fail("获取流媒体列表失败: " + e.getMessage());
        }
    }

    /**
     * 关闭流媒体
     */
    @GetMapping("/close-streams")
    @Operation(summary = "关闭流媒体", description = "关闭流媒体服务器上的媒体流，支持筛选条件")
    public R<CloseStreamsVO> closeStreams(
            @Parameter(description = "筛选协议，例如 rtsp 或 rtmp")
            @RequestParam(value = "schema", required = false) String schema,
            
            @Parameter(description = "筛选虚拟主机，例如 __defaultVhost__")
            @RequestParam(value = "vhost", required = false) String vhost,
            
            @Parameter(description = "筛选应用名，例如 live")
            @RequestParam(value = "app", required = false) String app,
            
            @Parameter(description = "筛选流id，例如 test")
            @RequestParam(value = "stream", required = false) String stream,
            
            @Parameter(description = "是否强制关闭所有在线观看者")
            @RequestParam(value = "force", required = false) Boolean force
    ) {
        try {
            CloseStreamsVO result = videoStreamService.closeStreams(schema, vhost, app, stream, force);
            return R.data(result);
        } catch (Exception e) {
            return R.fail("关闭流媒体失败: " + e.getMessage());
        }
    }
    
}
