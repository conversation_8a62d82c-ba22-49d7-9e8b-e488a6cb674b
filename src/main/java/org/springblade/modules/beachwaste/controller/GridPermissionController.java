package org.springblade.modules.beachwaste.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.constant.AuthConstant;
import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.service.IGridPermissionService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 网格权限管理控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/gridPermission")
@PreAuth(AuthConstant.PERMIT_ALL)
@Tag(name = "网格权限管理", description = "网格员和网格管理员权限查询")
public class GridPermissionController extends BladeController {

    private final IGridPermissionService gridPermissionService;

    /**
     * 获取网格员和网格管理员的权限ID列表
     *
     * @param tenantId 租户ID（可选参数，如果不传则使用默认租户）
     * @return 权限ID数组
     */
    @GetMapping("/roleIds")
    @Operation(summary = "获取网格权限ID", description = "查询数据库中网格员和网格管理员的权限ID，返回数组")
    public R<List<Long>> getGridRoleIds(@RequestParam(value = "tenantId", required = false, defaultValue = "952213") String tenantId) {
        log.info("查询网格员和网格管理员权限ID，租户ID: {}", tenantId);
        List<Long> roleIds = gridPermissionService.getGridRoleIds(tenantId);
        return R.data(roleIds);
    }

}
