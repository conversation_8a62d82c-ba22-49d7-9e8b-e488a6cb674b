package org.springblade.modules.beachwaste.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.pojo.entity.BeachLitterMedia;
import org.springblade.modules.beachwaste.service.IBeachLitterMediaService;
import org.springblade.modules.fh.service.IFhMinioService;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 海滩垃圾媒体管理控制器
 *
 * <AUTHOR> @since
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/beach-litter-media")
@Tag(name = "海滩垃圾媒体管理", description = "海滩垃圾媒体文件上传和管理")
public class BeachLitterMediaController extends BladeController {

    private final IBeachLitterMediaService beachLitterMediaService;
    private final IFhMinioService fhMinioService;

    /**
     * 获取媒体详情
     *
     * @param id 媒体ID
     * @return 媒体详情
     */
    @GetMapping("/detail")
    public R<BeachLitterMedia> detail(@RequestParam Long id) {
        return R.data(beachLitterMediaService.getById(id));
    }

    /**
     * 获取媒体分页列表
     *
     * @param beachLitterMedia 查询条件
     * @param query 分页参数
     * @return 分页结果
     */
    @GetMapping("/list")
    public R<IPage<BeachLitterMedia>> list(BeachLitterMedia beachLitterMedia, Query query) {
        IPage<BeachLitterMedia> pages = beachLitterMediaService.page(
            Condition.getPage(query),
            Condition.getQueryWrapper(beachLitterMedia)
        );
        return R.data(pages);
    }

    /**
     * 新增媒体
     *
     * @param beachLitterMedia 媒体信息
     * @return 操作结果
     */
    @PostMapping("/save")
    @Operation(summary = "新增媒体")
    public R save(@RequestBody BeachLitterMedia beachLitterMedia) {
        return R.status(beachLitterMediaService.save(beachLitterMedia));
    }

    /**
     * 上传图片文件
     *
     * @param file 图片文件
     * @return 上传结果，包含ObjectKey
     */
    @SneakyThrows
    @PostMapping("/upload-image")
    @Operation(summary = "上传图片", description = "上传图片文件到MinIO")
    public R<String> uploadImage(@RequestParam("file") MultipartFile file) {
        return beachLitterMediaService.uploadImage(file);
    }

    /**
     * 生成预签名 URL 并返回 ObjectKey
     *
     * @param fileType 文件类型（如：jpg、png、gif等，不需要包含点号）
     * @return 预签名URL和ObjectKey
     */
    @PostMapping("/generate-presigned-url")
    @Operation(summary = "生成预签名URL", description = "生成MinIO的预签名PUT请求URL，并返回随机生成的ObjectKey。" +
        "ObjectKey格式：beach-litter-{fileType}-{yyyyMMdd}-{randomUUID}-{snowflakeId}.{extension}（无斜杠，避免路径错误）")
    public R<Map<String, Object>> generatePresignedUrl(@RequestParam String fileType) {
        return beachLitterMediaService.generatePresignedUrl(fileType);
    }

    /**
     * UniApp文件上传接口
     * 适配UniApp的uni.uploadFile组件
     *
     * @param file 上传的文件
     * @return 上传结果
     */
    @PostMapping("/uniapp-upload")
    @Operation(summary = "UniApp文件上传", description = "适配UniApp的uni.uploadFile组件的文件上传接口")
    public R<Map<String, Object>> uniappUpload(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "type", required = false) String type) {
        return beachLitterMediaService.uniappUpload(file, type);
    }

    /**
     * 根据ObjectKey下载文件
     *
     * @param objectKey MinIO中的对象键
     * @return 文件流响应
     */
    @GetMapping("/download")
    @Operation(summary = "下载文件", description = "根据ObjectKey从MinIO下载文件")
    public ResponseEntity<Resource> downloadFile(@RequestParam String objectKey) {
        return beachLitterMediaService.downloadFile(objectKey);
    }

}
