package org.springblade.modules.beachwaste.controller;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.pojo.dto.*;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springblade.modules.beachwaste.service.IEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 事件数据
 * <AUTHOR>
@RestController
@RequestMapping("/event")
public class EventController {

	@Autowired
	private IEventService eventService;

	/**
	 * 根据ID获取事件信息
	 *
	 * @param id 事件ID
	 * @return 事件对象
	 */
	@GetMapping("/{id}")
	@Operation(summary = "获取事件详情", description = "根据ID获取事件信息")
	public R getById(@PathVariable Long id) {
		return R.data(eventService.getEventDetailById(id));
	}

	/**
	 * 新增事件信息
	 *
	 * @param event 事件实体对象
	 * @return 操作是否成功
	 */
	@PostMapping("/save")
	@Operation(summary = "新增事件", description = "新增事件信息")
	public R save(@RequestBody Event event) {
		return eventService.checkToSave(event);
	}

	/**
	 * 更新事件信息
	 *
	 * @param event 事件实体对象
	 * @return 操作是否成功
	 */
	@PostMapping("/update")
	@Operation(summary = "更新事件", description = "更新事件信息")
	public R update(@RequestBody Event event) {
		return eventService.checkToUpdateById(event);
	}

	/**
	 * 删除事件信息
	 *
	 * @param id 事件ID
	 * @return 操作是否成功
	 */
	@PostMapping("/delete/{id}")
	@Operation(summary = "删除事件", description = "删除事件信息")
	public R delete(@PathVariable Long id) {
		return R.status(eventService.removeById(id));
	}

	/**
	 * 获取事件列表
	 * 当分页参数 current=-1 或 size=-1 时，将返回所有符合条件的数据（不分页）
	 *
	 * @param queryDTO 查询条件
	 * @return 事件列表
	 */
	@PostMapping("/list")
	@Operation(summary = "获取事件列表", description = "根据查询条件获取事件列表，当分页参数 current=-1 或 size=-1 时，将返回所有符合条件的数据（不分页）")
	public R list(@RequestBody(required = false) EventQueryDTO queryDTO) {
		if (queryDTO == null) {
			queryDTO = new EventQueryDTO();
		}

		// 检测是否需要查询所有数据（不分页）
		Integer current = queryDTO.getCurrent();
		Integer size = queryDTO.getSize();

		boolean isCurrentInvalid = current != null && current == -1;
		boolean isSizeInvalid = size != null && size == -1;

		if (isCurrentInvalid || isSizeInvalid) {
			return eventService.listAllByQueryDTO(queryDTO);
		}

		return eventService.listByQueryDTO(queryDTO);
	}

	/**
	 * 大屏右侧事件列表
	 *
	 * @param discoveryMethod 发现方式 @link org.springblade.modules.beachwaste.enums.DiscoveryMethodEnum
	 * @param eventStatus     事件状态 @link org.springblade.modules.beachwaste.enums.EventStatusEnum
	 * @param startDate       开始日期（可选，格式：yyyy-MM-dd）
	 * @param endDate         结束日期（可选，格式：yyyy-MM-dd）
	 * @return 筛选后的事件列表
	 */
	@GetMapping("/listByCondition")
	@Operation(summary = "大屏右侧事件列表", description = "根据条件查询事件列表")
	public R listByCondition(@RequestParam(required = false) Long discoveryMethod,
							 @RequestParam(required = false) Long eventStatus,
							 @RequestParam(required = false) Long id,
							 @RequestParam(required = false) String startDate,
							 @RequestParam(required = false) String endDate) {
		return eventService.listByCondition(discoveryMethod, eventStatus, id, startDate, endDate);
	}

	/**
	 * 根据条件导出事件列表Excel
	 *
	 * @param discoveryMethod 发现方式 @link org.springblade.modules.beachwaste.enums.DiscoveryMethodEnum
	 * @param eventStatus     事件状态 @link org.springblade.modules.beachwaste.enums.EventStatusEnum
	 * @param startDate       开始日期（可选，格式：yyyy-MM-dd）
	 * @param endDate         结束日期（可选，格式：yyyy-MM-dd）
	 * @param response        HTTP响应对象
	 */
	@GetMapping("/exportByCondition")
	@Operation(summary = "根据条件导出事件列表", description = "根据条件导出事件列表Excel文件")
	public void exportByCondition(@RequestParam(required = false) Long discoveryMethod,
								  @RequestParam(required = false) Long eventStatus,
								  @RequestParam(required = false) Long id,
								  @RequestParam(required = false) String startDate,
								  @RequestParam(required = false) String endDate,
								  HttpServletResponse response) {
		eventService.exportByCondition(discoveryMethod, eventStatus, id, startDate, endDate, response);
	}

	/**
	 * 事件垃圾材质分类
	 *
	 * @param timeRange 时间范围：1-近一周，2-近一月，3-近一年
	 * @return 各类垃圾材质的数量统计
	 */
	@GetMapping("/wasteMaterialStats")
	@Operation(summary = "垃圾材质统计", description = "获取指定时间范围内的垃圾材质分布统计")
	public R getWasteMaterialStats(@RequestParam(required = true) Integer timeRange) {
		return eventService.getWasteMaterialStats(timeRange);
	}

	/**
	 * 根据事件ID导出Excel
	 *
	 * @param ids      事件ID数组
	 * @param response HTTP响应对象
	 */
	@PostMapping("/exportExcel")
	@Operation(summary = "导出事件详情", description = "根据事件ID数组导出Excel文件")
	public void exportEventExcel(@RequestBody Long[] ids, HttpServletResponse response) {
		eventService.exportEventExcel(ids, response);
	}

	/**
	 * 获取所有事件发生的年份列表
	 *
	 * @return 年份列表，按照降序排序
	 */
	@GetMapping("/years")
	@Operation(summary = "获取事件年份列表", description = "获取所有事件发生的年份列表")
	public R getEventYears() {
		return eventService.getEventYears();
	}

	/**
	 * 获取指定年份后各月份事件数量统计
	 *
	 * @param year 年份
	 * @return 各月份事件数量统计
	 */
	@GetMapping("/monthlyStats")
	@Operation(summary = "月度事件统计", description = "获取指定年份后各月份事件数量统计")
	public R getMonthlyStats(@RequestParam(required = true) Integer year) {
		return eventService.getMonthlyStats(year);
	}

	/**
	 * 根据网格ID查询相关事件列表
	 *
	 * @param queryDTO 网格事件查询参数DTO
	 * @return 事件列表
	 */
	@PostMapping("/byGrid")
	@Operation(summary = "网格关联事件查询", description = "根据网格ID查询相关事件列表")
	public R listByGridId(@Valid @RequestBody GridEventQueryDTO queryDTO) {
		return eventService.getEventsByGridId(queryDTO);
	}

	/**
	 * 根据时间范围获取事件位置信息
	 *
	 * @param startDate 开始日期（格式：yyyy-MM-dd）
	 * @param endDate   结束日期（格式：yyyy-MM-dd）
	 * @return 事件位置信息列表
	 */
	@GetMapping("/locations")
	@Operation(summary = "获取事件位置信息", description = "根据时间范围获取事件的经纬度坐标和ID信息")
	public R getEventLocations(@RequestParam(required = true) String startDate,
							   @RequestParam(required = true) String endDate) {
		return R.data(eventService.getEventLocationsByDateRange(startDate, endDate));
	}

	/**
	 * 获取指定年份已处置事件的处理人员统计
	 *
	 * @param year 年份
	 * @return 处理人员及其处理事件数量，从高到低排序
	 */
	@GetMapping("/processedStaffStats")
	@Operation(summary = "已处置事件人员统计", description = "获取指定年份已处置事件的处理人员统计，从高到低排序")
	public R getProcessedStaffStats(@RequestParam(required = true) Integer year) {
		return eventService.getProcessedStaffStats(year);
	}

	/**
	 * 获取当前网格员关联的事件列表
	 *
	 * @param queryDTO 查询条件
	 * @return 事件列表
	 */
	@PostMapping("/staff/list")
	@Operation(summary = "网格员事件列表", description = "获取当前网格员关联的事件列表")
	public R getStaffEvents(@RequestBody(required = false) StaffEventQueryDTO queryDTO) {
		if (queryDTO == null) {
			queryDTO = new StaffEventQueryDTO();
		}
		return eventService.getEventsByStaffId(queryDTO);
	}

	/**
	 * 根据时间范围导出事件Excel
	 *
	 * @param startDate 开始日期（格式：yyyy-MM-dd）
	 * @param endDate   结束日期（格式：yyyy-MM-dd）
	 * @param response  HTTP响应对象
	 */
	@GetMapping("/exportByDateRange")
	@Operation(summary = "根据时间范围导出事件", description = "根据传入的开始和结束时间导出时间范围内的事件Excel文件")
	public void exportEventsByDateRange(@RequestParam(required = true) String startDate,
										@RequestParam(required = true) String endDate,
										HttpServletResponse response) {
		eventService.exportEventsByDateRange(startDate, endDate, response);
	}

	/**
	 * 获取当前登录用户的事件处理统计数据
	 *
	 * @return 用户事件统计结果
	 */
	@GetMapping("/user-stats")
	@Operation(summary = "获取当前用户事件统计")
	public R getUserEventStats() {
		return eventService.getUserEventStats();
	}

	/**
	 * 事件处置接口
	 *
	 * @param processDTO 事件处置请求参数（包含图片ObjectKey）
	 * @return 处置结果
	 */
	@PostMapping("/process")
	@Operation(summary = "事件处置", description = "处置事件，支持已处理、误报、未找到三种状态。" +
		"已处理状态需要先通过/beach-litter-media/generate-presigned-url接口获取预签名URL和ObjectKey，" +
		"然后上传图片到MinIO，最后将ObjectKey作为processedImageObjectKey字段传入")
	public R processEvent(@Valid @RequestBody EventProcessDTO processDTO) {
		return eventService.processEvent(processDTO);
	}

	/**
	 * 获取地图事件点位数据
	 * 返回符合GeoJSON规范的事件点数据，供前端地图聚类展示
	 *
	 * @param queryDTO 地图事件查询条件，包含边界框、时间范围、事件状态等筛选条件
	 * @return GeoJSON FeatureCollection格式的事件点数据
	 */
	@PostMapping("/mapData")
	@Operation(summary = "获取地图事件点位数据", description = "获取符合GeoJSON规范的事件点数据")
	public R getMapEventData(@Valid @RequestBody MapEventQueryDTO queryDTO) {
		return R.data(eventService.getMapEventData(queryDTO));
	}

}
