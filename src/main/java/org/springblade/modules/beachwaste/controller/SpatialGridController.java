package org.springblade.modules.beachwaste.controller;

import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.pojo.dto.GridScheduleQueryDTO;
import org.springblade.modules.beachwaste.pojo.entity.SpatialGrid;
import org.springblade.modules.beachwaste.service.ISpatialGridService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 网格信息数据管理
 * <AUTHOR>
 */
@RestController
@RequestMapping("/spatialGrid")
public class SpatialGridController {

    @Autowired
    private ISpatialGridService spatialGridService;

    /**
     * 根据ID获取空间网格信息
     *
     * @param id 空间网格ID
     * @return 空间网格对象
     */
    @GetMapping("/{id}")
    public R getById(@PathVariable Long id) {
        return R.data(spatialGridService.getById(id));
    }

    /**
     * 根据ID获取空间网格详细信息
     *
     * @param id 空间网格ID
     * @return 包含网格信息、管理员联系方式和本月已处理事件的复合对象
     */
    @GetMapping("/detail/{id}")
    public R getGridDetail(@PathVariable Long id) {
        // 获取当前时间的年月格式
        String yearMonth = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        return spatialGridService.getGridDetailWithEvents(id, yearMonth);
    }

    /**
     * 新增空间网格信息
     * <p>
     * 如果网格编码为空，系统会自动生成格式为YYMMDD+XX的编码
     * （例如：24120501表示2024年12月5日+随机数01）
     * </p>
     *
     * @param spatialGrid 空间网格实体对象
     * @return 操作是否成功
     */
    @PostMapping
    public R save(@RequestBody SpatialGrid spatialGrid) {
        return spatialGridService.checkToSave(spatialGrid);
    }

    /**
     * 更新空间网格信息
     *
     * @param spatialGrid 空间网格实体对象
     * @return 操作是否成功
     */
    @PostMapping("/update")
    public R update(@RequestBody SpatialGrid spatialGrid) {
        return spatialGridService.checkToUpdateById(spatialGrid);
    }

    /**
     * 删除空间网格信息
     *
     * @param id 空间网格ID
     * @return 操作是否成功
     */
    @GetMapping("/delete/{id}")
    public boolean delete(@PathVariable Long id) {
        return spatialGridService.removeById(id);
    }

    /**
     * 获取空间网格列表
     *
     * @return 空间网格列表
     */
    @GetMapping("/list")
    public R list() {
        return R.data(spatialGridService.list());
    }

	/**
	 * 网格数据根据 KML文件导入
	 * <p>
	 * 如果网格编码为空，系统会自动生成格式为YYMMDD+XX的编码
	 * （例如：24120501表示2024年12月5日+随机数01）
	 * </p>
	 * 
	 * @param file KML格式的空间坐标文件
	 * @param inspectorId 网格管理员 ID
	 * @param gridName 网格名称
	 * @param gridCode 网格编码（可选，为空时自动生成）
	 * @return 导入结果（成功/失败提示）
	 */
	@PostMapping("/import")
	 public R importKml(@RequestParam("file") MultipartFile file, @RequestParam("inspectorId") Long inspectorId,
					 @RequestParam("gridName") String gridName, @RequestParam(value = "gridCode", required = false) String gridCode) {
		try {
			if (file.isEmpty()) {
				return R.fail("请上传有效的KML文件");
			}

			return spatialGridService.parseKmlFile(file, inspectorId, gridName, gridCode);
		} catch (Exception e) {
			return R.fail("KML解析失败: " + e.getMessage());
		}
	}

    /**
     * 解析KML文件中的多个地理要素并返回解析结果数组
     * @param file KML格式的空间坐标文件
     * @return 解析结果（包含多个地理要素信息的列表）
     */
    @PostMapping("/parseMultiple")
    public R parseMultipleFeatures(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return R.fail("请上传有效的KML文件");
            }

            return spatialGridService.parseMultipleFeatures(file);
        } catch (Exception e) {
            return R.fail("KML解析失败: " + e.getMessage());
        }
    }

    /**
     * 批量导入网格数据
     * <p>
     * 前端在解析KML文件后，补充网格编码和管理员ID等信息，然后批量导入。
     * 如果网格编码为空，系统会自动生成格式为YYMMDD+XX的编码
     * （例如：24120501表示2024年12月5日+随机数01）
     * </p>
     *
     * @param gridList 网格数据列表，包含网格编码、网格名称、管理员ID等信息
     * @return 导入结果（成功/失败提示）
     */
    @PostMapping("/batchImport")
    public R batchImportGrids(@RequestBody List<SpatialGrid> gridList) {
        try {
            if (gridList == null || gridList.isEmpty()) {
                return R.fail("请提供有效的网格数据列表");
            }

            return spatialGridService.batchSaveGrids(gridList);
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail("批量导入网格数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取网格（排班）列表数据
     *
     * @param queryDTO 查询参数 DTO，包含排序字段 sortBy、排班状态 scheduleStatus 和管理员ID id
     * @return 网格列表数据，包含编号、名称、今日排班和管理员信息
     */
    @PostMapping("/gridList")
    public R getGridList(@RequestBody GridScheduleQueryDTO queryDTO) {
        return spatialGridService.getGridListWithSchedule(queryDTO);
    }

    /**
     * 导出所有有效空间网格数据为KML文件
     *
     * @param filename 自定义文件名（可选），默认为 full_grid_export.kml
     * @return KML文件下载响应
     */
    @GetMapping("/export/all")
    public ResponseEntity<String> exportAllGridsToKml(@RequestParam(value = "filename", required = false, defaultValue = "full_grid_export.kml") String filename) {
        try {
            String kmlContent = spatialGridService.exportAllGridsToKml(filename);

            if (kmlContent == null || kmlContent.trim().isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            // 确保文件名以.kml结尾
            if (!filename.toLowerCase().endsWith(".kml")) {
                filename += ".kml";
            }

            return ResponseEntity.ok()
                    .header("Content-Type", "application/vnd.google-earth.kml+xml")
                    .header("Content-Disposition", "attachment; filename=\"" + filename + "\"")
                    .body(kmlContent);
        } catch (Exception e) {
            return ResponseEntity.status(500).body("导出KML文件失败: " + e.getMessage());
        }
    }

}
