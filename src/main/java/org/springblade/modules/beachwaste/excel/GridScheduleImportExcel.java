package org.springblade.modules.beachwaste.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.ExcelIgnore;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 网格排班信息Excel导入实体类
 *
 * <AUTHOR> AI
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class GridScheduleImportExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 网格编号
     */
    @ColumnWidth(15)
    @ExcelProperty("网格编号")
    private String gridCode;

    /**
     * 网格员姓名
     */
    @ColumnWidth(15)
    @ExcelProperty("网格员")
    private String staffName;

    /**
     * 排班开始时间
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "开始日期", format = "yyyy/MM/dd")
    private LocalDate startDate;

    /**
     * 排班结束时间
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "结束日期", format = "yyyy/MM/dd")
    private LocalDate endDate;

    /**
     * 原始行号 (用于错误提示)
     */
    @ExcelIgnore // 导入时不处理此列
    private Integer rowIndex;

    /**
     * 网格ID
     */
    @ExcelIgnore // 导入时不处理此列
    private Long gridId;

    /**
     * 网格员ID
     */
    @ExcelIgnore // 导入时不处理此列
    private Long staffId;

}