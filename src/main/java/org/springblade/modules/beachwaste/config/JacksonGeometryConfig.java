package org.springblade.modules.beachwaste.config;

import com.fasterxml.jackson.databind.module.SimpleModule;
import org.locationtech.jts.geom.Point;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Jackson配置类，用于处理JTS几何对象的序列化和反序列化
 * 解决JTS几何对象在JSON序列化时的循环引用问题
 *
 * 注意：对于包含JTS几何字段的实体类，推荐使用@JsonIgnore注解标记几何字段，
 * 并提供@JsonProperty方法返回可序列化的几何信息，参考Event.java和SpatialGrid.java的实现
 *
 * <AUTHOR> @since
 */
@Configuration
public class JacksonGeometryConfig {

    /**
     * 配置Jackson ObjectMapper以处理JTS Point对象
     * 注册Point对象的自定义序列化器和反序列化器
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jacksonGeometryCustomizer() {
        return builder -> {
            SimpleModule geometryModule = new SimpleModule("GeometryModule");

            // 注册Point的专用序列化器
            geometryModule.addSerializer(Point.class, new PointSerializer());
            geometryModule.addDeserializer(Point.class, new PointDeserializer());

            builder.modules(geometryModule);
        };
    }
}
