package org.springblade.modules.beachwaste.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 海滩垃圾模块MinIO配置类
 * 用于配置新的MinIO连接参数
 *
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "minio")
@Component
@Data
public class BeachWasteMinioConfiguration {

    /**
     * MinIO服务主机地址
     */
    private String host;

    /**
     * MinIO服务端口
     */
    private Integer port = 9000;

    /**
     * SSL配置
     */
    private Ssl ssl = new Ssl();

    /**
     * SSL配置类
     */
    @Data
    public static class Ssl {
        /**
         * 是否启用SSL
         */
        private Boolean enabled = false;
    }

    /**
     * 访问密钥
     */
    private String accessKey;

    /**
     * 秘密密钥
     */
    private String secretKey;

    /**
     * 存储桶名称
     */
    private String bucketName;

    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectTimeout = 10000;

    /**
     * 读取超时时间（毫秒）
     */
    private Integer readTimeout = 10000;

    /**
     * 获取完整的MinIO端点URL
     * @return 完整的端点URL
     */
    public String getEndpoint() {
        String protocol = ssl.enabled ? "https" : "http";
        return String.format("%s://%s:%d", protocol, host, port);
    }
}