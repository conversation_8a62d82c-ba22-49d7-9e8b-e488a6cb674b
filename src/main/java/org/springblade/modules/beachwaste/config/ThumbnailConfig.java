package org.springblade.modules.beachwaste.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 缩略图配置类
 * 管理缩略图生成和存储的相关配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "beachwaste.thumbnail")
public class ThumbnailConfig {

    /**
     * 缩略图默认宽度（3:3比例）
     */
    private int width = 300;

    /**
     * 缩略图默认高度（3:3比例）
     */
    private int height = 300;

    /**
     * 图片质量（0.0-1.0）
     */
    private double quality = 0.8;

    /**
     * bbox区域放大系数
     */
    private double scaleFactor = 1.5;



    /**
     * 是否启用缩略图功能
     */
    private boolean enabled = true;

    /**
     * 是否启用定时清理
     */
    private boolean cleanupEnabled = true;

    /**
     * 清理任务执行时间（cron表达式）
     */
    private String cleanupCron = "0 0 2 * * ?";

    /**
     * 缩略图存储路径前缀
     */
    private String storagePathPrefix = "thumbnails/";

    /**
     * 支持的图片格式
     */
    private String[] supportedFormats = {"jpg", "jpeg", "png", "bmp", "gif"};

    /**
     * 最大处理图片大小（字节）
     */
    private long maxImageSize = 10 * 1024 * 1024;

    /**
     * 处理超时时间（毫秒）
     */
    private long processingTimeoutMs = 30000;


}