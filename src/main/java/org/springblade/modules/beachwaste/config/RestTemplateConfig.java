package org.springblade.modules.beachwaste.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate配置类
 * 用于配置HTTP客户端，调用AI服务器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Configuration
@RequiredArgsConstructor
public class RestTemplateConfig {

    private final AiServerConfig aiServerConfig;

    /**
     * 配置RestTemplate Bean
     *
     * @return RestTemplate实例
     */
    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        
        // 设置连接超时时间
        factory.setConnectTimeout(aiServerConfig.getConnectTimeout());
        
        // 设置读取超时时间
        factory.setReadTimeout(aiServerConfig.getReadTimeout());
        
        return new RestTemplate(factory);
    }
}