package org.springblade.modules.beachwaste.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * AI服务器配置类
 * 用于读取application.yml中的AI服务器配置参数
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Component
@ConfigurationProperties(prefix = "ai-server")
public class AiServerConfig {

    /**
     * AI服务器地址
     */
    private String host;

    /**
     * AI服务器端口
     */
    private Integer port;

    /**
     * 协议类型 (http/https)
     */
    private String protocol;

    /**
     * 完整的服务地址
     */
    private String baseUrl;

    /**
     * 帧识别接口路径
     */
    private String frameRecognitionPath;

    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectTimeout;

    /**
     * 读取超时时间（毫秒）
     */
    private Integer readTimeout;

    /**
     * 写入超时时间（毫秒）
     */
    private Integer writeTimeout;

    /**
     * 最大重试次数
     */
    private Integer maxRetry;

    /**
     * 重试间隔时间（毫秒）
     */
    private Integer retryInterval;

    /**
     * 获取完整的帧识别接口URL
     *
     * @return 完整的帧识别接口URL
     */
    public String getFrameRecognitionUrl() {
        return baseUrl + frameRecognitionPath;
    }
}