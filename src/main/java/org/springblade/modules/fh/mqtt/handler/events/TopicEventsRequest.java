package org.springblade.modules.fh.mqtt.handler.events;


import org.springblade.modules.fh.mqtt.CommonTopicRequest;

public class TopicEventsRequest<T> extends CommonTopicRequest<T> {

    private String method;

    private String gateway;

    private String from;

    private boolean needReply;

    public TopicEventsRequest() {
    }

    @Override
    public String toString() {
        return "TopicRequestsRequest{" +
                "method='" + method + '\'' +
                ", gateway='" + gateway + '\'' +
                ", from='" + from + '\'' +
                ", needReply=" + needReply +
                ", tid='" + tid + '\'' +
                ", bid='" + bid + '\'' +
                ", timestamp=" + timestamp +
                ", data=" + data +
                '}';
    }

    public String getTid() {
        return tid;
    }

    public TopicEventsRequest<T> setTid(String tid) {
        this.tid = tid;
        return this;
    }

    public String getBid() {
        return bid;
    }

    public TopicEventsRequest<T> setBid(String bid) {
        this.bid = bid;
        return this;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public TopicEventsRequest<T> setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
        return this;
    }

    public T getData() {
        return data;
    }

    public TopicEventsRequest<T> setData(T data) {
        this.data = data;
        return this;
    }

    public String getGateway() {
        return gateway;
    }

    public TopicEventsRequest<T> setGateway(String gateway) {
        this.gateway = gateway;
        return this;
    }

    public String getFrom() {
        return from;
    }

    public TopicEventsRequest<T> setFrom(String from) {
        this.from = from;
        return this;
    }

    public boolean isNeedReply() {
        return needReply;
    }

    public TopicEventsRequest<T> setNeedReply(boolean needReply) {
        this.needReply = needReply;
        return this;
    }

    public String getMethod() {
        return method;
    }

    public TopicEventsRequest<T> setMethod(String method) {
        this.method = method;
        return this;
    }
}
