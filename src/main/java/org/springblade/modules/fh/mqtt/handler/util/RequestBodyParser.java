package org.springblade.modules.fh.mqtt.handler.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.pojo.dto.DetectResultDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 请求体解析工具类
 * 负责解析检测回调的请求体数据
 */
@Slf4j
public class RequestBodyParser {

    private static final String RECORDS_FIELD = "records";
    private static final String INFO_FIELD = "info";

    /**
     * 解析请求体数据
     *
     * @param requestBody 请求体对象
     * @return 检测结果DTO列表
     */
    public static List<DetectResultDTO> parseRequestBody(Object requestBody) {
        if (requestBody == null) {
            return new ArrayList<>();
        }

        try {
            List<Object> rawList = extractRawListFromRequestBody(requestBody);
            return parseDetectResultDTOs(rawList);
        } catch (ClassCastException e) {
            log.error("请求体类型转换失败: {}", e.getMessage());
            throw new IllegalArgumentException("请求体格式错误，类型转换失败: " + e.getMessage());
        }
    }

    /**
     * 从请求体中提取原始对象列表
     *
     * @param requestBody 请求体对象
     * @return 原始对象列表
     */
    @SuppressWarnings("unchecked")
    private static List<Object> extractRawListFromRequestBody(Object requestBody) {
        if (requestBody instanceof List) {
            log.info("检测到数组格式的请求体，已转换为DetectResultDTO对象列表");
            return (List<Object>) requestBody;
        } else if (requestBody instanceof Map) {
            return extractRecordsFromMap((Map<String, Object>) requestBody);
        } else {
            log.warn("不支持的请求体格式: {}", requestBody.getClass().getName());
            throw new IllegalArgumentException("不支持的请求体格式，仅支持数组或包含records字段的对象");
        }
    }

    /**
     * 从Map格式的请求体中提取records字段
     *
     * @param requestMap 请求体Map
     * @return records字段的值
     */
    @SuppressWarnings("unchecked")
    private static List<Object> extractRecordsFromMap(Map<String, Object> requestMap) {
        if (requestMap.containsKey(RECORDS_FIELD) && requestMap.get(RECORDS_FIELD) instanceof List) {
            log.info("检测到对象格式的请求体，包含records字段，已转换为DetectResultDTO对象列表");
            return (List<Object>) requestMap.get(RECORDS_FIELD);
        } else {
            log.warn("请求体格式不正确，缺少records字段或格式错误: {}", JSON.toJSONString(requestMap));
            throw new IllegalArgumentException("请求体格式不正确，缺少records字段或格式错误");
        }
    }

    /**
     * 将原始对象列表解析为DetectResultDTO对象列表
     *
     * @param rawList 原始对象列表
     * @return DetectResultDTO对象列表
     */
    private static List<DetectResultDTO> parseDetectResultDTOs(List<Object> rawList) {
        if (rawList == null || rawList.isEmpty()) {
            return new ArrayList<>();
        }

        List<DetectResultDTO> dtoList = new ArrayList<>(rawList.size());
        int successCount = 0;
        int failureCount = 0;

        for (int i = 0; i < rawList.size(); i++) {
            Object item = rawList.get(i);
            try {
                DetectResultDTO dto = parseDetectResultDTO(item);
                if (dto != null) {
                    dtoList.add(dto);
                    successCount++;
                } else {
                    failureCount++;
                }
            } catch (Exception e) {
                failureCount++;
                log.error("解析DetectResultDTO时发生异常, 索引: {}, item: {}", i, safeJsonString(item), e);
            }
        }

        log.info("解析DetectResultDTO完成，成功: {}, 失败: {}, 总计: {}", successCount, failureCount, rawList.size());
        return dtoList;
    }

    /**
     * 解析单个DetectResultDTO对象
     *
     * @param item 原始对象
     * @return DetectResultDTO对象，解析失败时返回null
     */
    private static DetectResultDTO parseDetectResultDTO(Object item) {
        if (item == null) {
            return null;
        }

        try {
            JSONObject jsonObject;
            if (item instanceof JSONObject) {
                jsonObject = (JSONObject) item;
            } else {
                jsonObject = JSON.parseObject(JSON.toJSONString(item));
            }

            // 处理info字段的特殊情况
            processInfoField(jsonObject);

            // 将处理后的JSONObject转换为DetectResultDTO
            return jsonObject.toJavaObject(DetectResultDTO.class);
        } catch (Exception e) {
            log.warn("解析单个DetectResultDTO失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 处理info字段的特殊情况
     *
     * @param jsonObject JSON对象
     */
    private static void processInfoField(JSONObject jsonObject) {
        Object infoObject = jsonObject.get(INFO_FIELD);
        if (infoObject instanceof String) {
            String infoStr = (String) infoObject;
            // 检查字符串是否看起来像JSON数组
            if (infoStr.trim().startsWith("[") && infoStr.trim().endsWith("]")) {
                try {
                    List<Object> parsedInfoList = JSON.parseArray(infoStr, Object.class);
                    jsonObject.put(INFO_FIELD, parsedInfoList);
                    log.debug("成功将info字段从字符串解析为JSON数组");
                } catch (Exception e) {
                    log.warn("无法将info字段的字符串内容解析为JSON数组: {}", infoStr, e);
                }
            }
        }
    }

    /**
     * 安全地将对象转换为JSON字符串，用于日志记录
     *
     * @param obj 要转换的对象
     * @return JSON字符串，转换失败时返回对象的toString()
     */
    private static String safeJsonString(Object obj) {
        if (obj == null) {
            return "null";
        }
        try {
            return JSON.toJSONString(obj);
        } catch (Exception e) {
            return obj.toString();
        }
    }
}