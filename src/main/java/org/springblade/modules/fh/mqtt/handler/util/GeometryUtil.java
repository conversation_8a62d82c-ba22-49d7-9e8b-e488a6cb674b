package org.springblade.modules.fh.mqtt.handler.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.pojo.entity.SpatialGrid;
import org.springblade.modules.beachwaste.service.ISpatialGridService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;

/**
 * 几何工具类 - 用于处理地理坐标相关操作
 *
 * <AUTHOR>
@Slf4j
@Component
public class GeometryUtil {

    @Autowired
    private ISpatialGridService spatialGridService;

    private static ISpatialGridService staticSpatialGridService;

    @PostConstruct
    public void init() {
        staticSpatialGridService = spatialGridService;
    }

    /**
     * 根据经纬度获取所在网格ID
     *
     * @param lon 经度
     * @param lat 纬度
     * @return 网格ID，如果找不到则返回0
     */
    public static String getGridId(String lon, String lat) {
        try {
            double longitude = Double.parseDouble(lon);
            double latitude = Double.parseDouble(lat);

            // 获取所有网格
            List<SpatialGrid> grids = staticSpatialGridService.list(new LambdaQueryWrapper<SpatialGrid>()
                .eq(SpatialGrid::getIsDeleted, 0)
                .eq(SpatialGrid::getStatus, 1));

            // 遍历所有网格，检查点是否在网格内
            for (SpatialGrid grid : grids) {
                if (isPointInGrid(longitude, latitude, grid)) {
                    return String.valueOf(grid.getId());
                }
            }

            // 如果没有找到匹配的网格，返回默认值0
            log.warn("未找到包含坐标点({}, {})的网格", lon, lat);
            return "0";
        } catch (Exception e) {
            log.error("获取网格ID时发生错误", e);
            return "0";
        }
    }

    /**
     * 根据经纬度生成点位置字符串
     *
     * @param lon 经度
     * @param lat 纬度
     * @return 格式化的点位置字符串 "经度,纬度"
     */
    public static String getPoint(String lon, String lat) {
        try {
            double longitude = Double.parseDouble(lon);
            double latitude = Double.parseDouble(lat);

            // 返回标准格式的点位置字符串
            return String.format("%.6f,%.6f", longitude, latitude);
        } catch (Exception e) {
            log.error("生成点位置字符串时发生错误", e);
            return "0,0";
        }
    }

    /**
     * 判断点是否在网格内
     *
     * @param lon 经度
     * @param lat 纬度
     * @param grid 网格对象
     * @return 是否在网格内
     */
    private static boolean isPointInGrid(double lon, double lat, SpatialGrid grid) {
        try {

            // 解析网格的GeoJSON数据
            String geoJson = grid.getGeomJson();
            if (geoJson == null || geoJson.isEmpty()) {
                return false;
            }

            // 从GeoJSON中提取坐标数组
            List<double[]> coordinates = extractCoordinatesFromGeoJson(geoJson);
            if (coordinates == null || coordinates.isEmpty()) {
                return false;
            }

            // 使用射线法判断点是否在多边形内
            return isPointInPolygon(lon, lat, coordinates);
        } catch (Exception e) {
            log.error("判断点是否在网格内时发生错误", e);
            return false;
        }
    }

    /**
     * 从GeoJSON中提取坐标数组
     *
     * @param geoJson GeoJSON字符串
     * @return 坐标数组
     */
    private static List<double[]> extractCoordinatesFromGeoJson(String geoJson) {
        try {
            List<double[]> result = new ArrayList<>();

            JSONObject jsonObject = JSON.parseObject(geoJson);
            if (jsonObject == null) {
                return result;
            }

            // 获取geometry部分
            JSONObject geometry = jsonObject.getJSONObject("geometry");
            if (geometry == null) {
                return result;
            }

            // 获取类型
            String type = geometry.getString("type");
            if (!"Polygon".equals(type)) {
                return result;
            }

            // 获取坐标数组
            JSONArray coordinates = geometry.getJSONArray("coordinates");
            if (coordinates == null || coordinates.isEmpty()) {
                return result;
            }

            // 多边形的第一个环（外环）
            JSONArray outerRing = coordinates.getJSONArray(0);
            if (outerRing == null || outerRing.isEmpty()) {
                return result;
            }

            // 提取坐标点
            for (int i = 0; i < outerRing.size(); i++) {
                JSONArray point = outerRing.getJSONArray(i);
                if (point != null && point.size() >= 2) {
                    double[] coord = new double[2];
                    coord[0] = point.getDoubleValue(0);
                    coord[1] = point.getDoubleValue(1);
                    result.add(coord);
                }
            }

            return result;
        } catch (Exception e) {
            log.error("从GeoJSON中提取坐标数组时发生错误", e);
            return new ArrayList<>();
        }
    }

    /**
     * 使用射线法判断点是否在多边形内
     *
     * @param lon 经度
     * @param lat 纬度
     * @param coordinates 多边形坐标数组
     * @return 是否在多边形内
     */
    private static boolean isPointInPolygon(double lon, double lat, List<double[]> coordinates) {
        if (coordinates == null || coordinates.size() < 3) {
            return false;
        }

        boolean inside = false;
        int size = coordinates.size();
        // 定义浮点数比较的精度阈值
        final double EPSILON = 1e-9;

        // 射线法（Ray Casting Algorithm）
        // 从点向右发射一条射线，计算与多边形边的交点数
        // 如果交点数为奇数，则点在多边形内部；如果为偶数，则在外部
        for (int i = 0, j = size - 1; i < size; j = i++) {
            double[] vertI = coordinates.get(i);
            double[] vertJ = coordinates.get(j);

            // 判断点是否在边上
            if (isPointOnSegment(lon, lat, vertI[0], vertI[1], vertJ[0], vertJ[1])) {
                return true;
            }

            // 处理国际日期变更线（±180度）的情况
            boolean crossesInternationalDateLine = Math.abs(vertI[0] - vertJ[0]) > 180;
            if (crossesInternationalDateLine) {
                // 如果边跨越了国际日期变更线，需要调整经度值
                double adjustedLon = lon > 0 ? lon - 360 : lon + 360;
                double adjustedVertIX = vertI[0] > 0 ? vertI[0] - 360 : vertI[0] + 360;
                double adjustedVertJX = vertJ[0] > 0 ? vertJ[0] - 360 : vertJ[0] + 360;

                // 判断调整后的点是否在调整后的边上
                if ((adjustedVertIX > adjustedLon) != (adjustedVertJX > adjustedLon) &&
                    (lon < (adjustedVertJX - adjustedVertIX) * (lat - vertI[1]) / (vertJ[1] - vertI[1]) + adjustedVertIX)) {
                    inside = !inside;
                }
            } else {
                // 正常情况下的射线与边相交判断
                if ((vertI[1] > lat) != (vertJ[1] > lat) &&
                    (lon < (vertJ[0] - vertI[0]) * (lat - vertI[1]) / (vertJ[1] - vertI[1]) + vertI[0])) {
                    inside = !inside;
                }
            }
        }

        return inside;
    }

    /**
     * 判断点是否在线段上
     *
     * @param x 点的x坐标（经度）
     * @param y 点的y坐标（纬度）
     * @param x1 线段起点x坐标
     * @param y1 线段起点y坐标
     * @param x2 线段终点x坐标
     * @param y2 线段终点y坐标
     * @return 是否在线段上
     */
    private static boolean isPointOnSegment(double x, double y, double x1, double y1, double x2, double y2) {
        // 判断点是否在线段的矩形范围内
        if (x < Math.min(x1, x2) || x > Math.max(x1, x2) || y < Math.min(y1, y2) || y > Math.max(y1, y2)) {
            return false;
        }

        // 如果线段是垂直的
        if (Math.abs(x1 - x2) < 1e-9) {
            return Math.abs(x - x1) < 1e-9;
        }

        // 如果线段是水平的
        if (Math.abs(y1 - y2) < 1e-9) {
            return Math.abs(y - y1) < 1e-9;
        }

        // 计算点到线段的距离
        double distance = Math.abs((y2 - y1) * x - (x2 - x1) * y + x2 * y1 - y2 * x1) /
                          Math.sqrt(Math.pow(y2 - y1, 2) + Math.pow(x2 - x1, 2));

        // 如果距离足够小，认为点在线段上
        return distance < 1e-9;
    }
}
