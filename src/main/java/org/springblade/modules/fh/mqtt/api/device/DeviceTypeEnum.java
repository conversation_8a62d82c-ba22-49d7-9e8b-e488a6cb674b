package org.springblade.modules.fh.mqtt.api.device;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springblade.core.log.exception.ServiceException;

import java.util.Arrays;

@Schema(description = "device type", enumAsRef = true)
public enum DeviceTypeEnum {

    M350(89),

    M300(60),

    M30_OR_M3T_CAMERA(67),

    M3E(77),

    Z30(20),

    XT2(26),

    FPV(39),

    XTS(41),

    H20(42),

    H20T(43),

    P1(50),

    M30_CAMERA(52),

    M30T_CAMERA(53),

    H20<PERSON>(61),

    DOCK_CAMERA(165),

    L1(90742),

    M3E_CAMERA(66),

    M3M_CAMERA(68),

    <PERSON>(56),

    <PERSON>_PLUS(119),

    <PERSON>_<PERSON>O(144),

    <PERSON><PERSON><PERSON>(1),

    <PERSON>OC<PERSON>2(2),
    <PERSON><PERSON><PERSON>3(3),

    M3<PERSON>(91),

    M3D_CAMERA(80),

    M3TD_CAMERA(81),

    M4<PERSON>(100),

    M4D_CAME<PERSON>(98),

    M4TD_CAMERA(99),

    M4E_CAMERA(88),

    ZS_H30T(83),

    ZS_H30(82),

    M3D_ASSISTED_IMAGE(176),

    RC_PLUS2(174)
    ;

    private final int type;

    DeviceTypeEnum(int type) {
        this.type = type;
    }

    @JsonValue
    public int getType() {
        return type;
    }

    @JsonCreator
    public static DeviceTypeEnum find(int type) {
        return Arrays.stream(values()).filter(typeEnum -> typeEnum.type == type).findAny()
                .orElseThrow(() -> new ServiceException("未知设备type"));
    }
}
