package org.springblade.modules.fh.mqtt.handler.events;


import jakarta.annotation.Resource;
import org.springblade.modules.fh.mqtt.IMqttTopicService;
import org.springblade.modules.fh.mqtt.TopicConst;
import org.springframework.stereotype.Component;


@Component
public class EventsSubscribe {

    public static final String TOPIC = TopicConst.THING_MODEL_PRE + TopicConst.PRODUCT + "%s" + TopicConst.EVENTS_SUF;

    @Resource
    private IMqttTopicService topicService;

    public void subscribe(String sn) {
        topicService.subscribe(String.format(TOPIC,sn));
    }

    public void unsubscribe(String sn) {
        topicService.unsubscribe(String.format(TOPIC, sn));
    }
}
