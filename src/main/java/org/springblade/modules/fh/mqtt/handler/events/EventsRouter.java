package org.springblade.modules.fh.mqtt.handler.events;


import org.springblade.common.constant.ChannelName;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.modules.fh.mqtt.Common;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;

import java.io.IOException;
import java.util.Arrays;

import static org.springblade.modules.fh.mqtt.TopicConst.*;

@Configuration
public class EventsRouter {

//    @Resource
//    private MqttGatewayPublish gatewayPublish;

    @Bean
    public IntegrationFlow eventsMethodRouterFlow() {
		return IntegrationFlow
			.from(ChannelName.INBOUND_EVENTS)
			.transform(Message.class, source -> {
				try {
                        TopicEventsRequest data = Common.getObjectMapper().readValue((byte[]) source.getPayload(), TopicEventsRequest.class);
                        String topic = String.valueOf(source.getHeaders().get(MqttHeaders.RECEIVED_TOPIC));
                        return data.setFrom(topic.substring((THING_MODEL_PRE + PRODUCT).length(), topic.indexOf(EVENTS_SUF)))
                                .setData(Common.getObjectMapper().convertValue(data.getData(), EventsMethodEnum.find(data.getMethod()).getClassType()));
                    } catch (IOException e) {
                        throw new ServiceException(e.getMessage());
                    }
			})
			.<TopicEventsRequest, EventsMethodEnum>route(
				response -> EventsMethodEnum.find(response.getMethod()),
				mapping -> Arrays.stream(EventsMethodEnum.values()).forEach(
                                methodEnum -> mapping.channelMapping(methodEnum, methodEnum.getChannelName())))
			.get();
    }

    @Bean
    public IntegrationFlow replySuccessEvents() {
        return IntegrationFlow
                .from(ChannelName.OUTBOUND_EVENTS)
                .handle(this::publish)
                .nullChannel();

    }

    private TopicEventsResponse publish(TopicEventsResponse request, MessageHeaders headers) {

		return null;
//        if (Objects.isNull(request) || Objects.isNull(request.getData())) {
//            return null;
//        }
//        gatewayPublish.publishReply(request, headers);
//        return request;
    }

}
