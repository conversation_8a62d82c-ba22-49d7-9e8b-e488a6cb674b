package org.springblade.modules.fh.mqtt.handler.events;

import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.pojo.dto.DetectResultDTO;

import java.util.List;

/**
 * 回调检测结果处理服务接口
 */
public interface IDetectCallbackService {

    /**
     * 处理检测结果列表
     *
     * @param requestBody 请求体对象
     * @return 处理结果
     */
    R<String> handleDetectBatchCallback(Object requestBody);

    /**
     * 处理检测结果列表
     *
     * @param records 检测结果记录列表
     * @return 处理结果
     */
    R<String> processDetectResults(List<DetectResultDTO> records);

}
