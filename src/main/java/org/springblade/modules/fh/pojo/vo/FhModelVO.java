package org.springblade.modules.fh.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 模型信息视图对象
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "模型信息视图对象")
public class FhModelVO {

    /**
     * 模型ID
     */
    @Schema(description = "模型ID")
    private Integer id;

    /**
     * 模型名称
     */
    @Schema(description = "模型名称")
    private String name;

    /**
     * 模型类型
     */
    @Schema(description = "模型类型")
    private String fileType;

    /**
     * 是否在地图上展示
     */
    @Schema(description = "是否在地图上展示")
    private Boolean showOnMap;

    /**
     * 模型大小
     */
    @Schema(description = "模型大小")
    private Integer size;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Long createAt;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Long updateAt;

}
