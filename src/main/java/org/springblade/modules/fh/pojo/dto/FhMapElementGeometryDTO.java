package org.springblade.modules.fh.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 地图标注几何信息DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "地图标注几何信息")
public class FhMapElementGeometryDTO {

    /**
     * 几何类型
     * Point: 点
     * LineString: 线
     * Polygon: 多边形
     * Circle: 圆形
     */
    @NotBlank(message = "几何类型不能为空")
    @Schema(description = "几何类型", example = "Point", allowableValues = {"Point", "LineString", "Polygon", "Circle"})
    private String type;

	/**
	 * 坐标信息
	 * Point: [x, y]
	 * LineString: [[x1, y1], [x2, y2], ...]
	 * Polygon: [[[x1, y1], [x2, y2], ...]]
	 * Circle: [x, y]
	 */
	@NotNull(message = "坐标信息不能为空")
	@Schema(description = "坐标信息")
	private Object coordinates;

	/**
	 * 半径
	 */
	@Schema(description = "半径")
	private Number radius;

}
