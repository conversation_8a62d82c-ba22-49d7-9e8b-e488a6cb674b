package org.springblade.modules.fh.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 地图标注VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "地图标注信息")
public class FhMapElementVO {

    /**
     * 标注ID
     */
    @Schema(description = "标注ID")
    private String id;

    /**
     * 标注名称
     */
    @Schema(description = "标注名称")
    private String name;

    /**
     * 标注类型
     */
    @Schema(description = "标注类型")
    private String type;

    /**
     * 经度
     */
    @Schema(description = "经度")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @Schema(description = "纬度")
    private BigDecimal latitude;

    /**
     * 标注描述
     */
    @Schema(description = "标注描述")
    private String description;

    /**
     * 标注颜色
     */
    @Schema(description = "标注颜色")
    private String color;

    /**
     * 标注图标
     */
    @Schema(description = "标注图标")
    private String icon;

    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private String projectId;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer order;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 显示状态
     */
    @Schema(description = "显示状态")
    private Integer display;

    /**
     * 资源信息
     */
    @Schema(description = "资源信息")
    private Map<String, Object> resource;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 高程加载状态
     */
    @Schema(description = "高程加载状态")
    private Integer elevationLoadStatus;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private String properties;

}
