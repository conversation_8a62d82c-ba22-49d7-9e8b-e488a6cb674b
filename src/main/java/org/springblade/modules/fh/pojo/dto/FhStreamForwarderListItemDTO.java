package org.springblade.modules.fh.pojo.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 码流转发器列表项DTO
 *
 * <AUTHOR> AI
 * @since 2024-01-01
 */
@Data
@Schema(description = "码流转发器列表项")
public class FhStreamForwarderListItemDTO {

    @Schema(description = "转发器名称")
    @JsonProperty("converter_name")
    private String converterName;

    @Schema(description = "转发器ID")
    @JsonProperty("converter_id")
    private String converterId;

    @Schema(description = "设备序列号")
    @JsonProperty("sn")
    private String sn;

    @Schema(description = "摄像头索引")
    @JsonProperty("camera_index")
    private String cameraIndex;

    @Schema(description = "协议类型")
    @JsonProperty("schema")
    private String schema;

    @Schema(description = "协议选项")
    @JsonProperty("schema_option")
    private Object schemaOption;

    @Schema(description = "自动推流状态")
    @JsonProperty("auto_push_stream")
    private Boolean autoPushStream;

    @Schema(description = "设备在线状态")
    @JsonProperty("device_online_status")
    private Boolean deviceOnlineStatus;

    @Schema(description = "设备SN")
    @JsonProperty("device_sn")
    private String deviceSn;

    @Schema(description = "状态")
    @JsonProperty("state")
    private String state;

    @Schema(description = "状态码")
    @JsonProperty("state_code")
    private Integer stateCode;

    @Schema(description = "状态消息")
    @JsonProperty("state_message")
    private String stateMessage;

}