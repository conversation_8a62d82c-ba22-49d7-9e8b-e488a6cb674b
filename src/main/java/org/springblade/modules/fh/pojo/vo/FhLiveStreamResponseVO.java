package org.springblade.modules.fh.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 直播响应VO
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "直播响应数据")
public class FhLiveStreamResponseVO {

    /**
     * 直播推流Token有效期时间戳
     */
    @Schema(description = "直播推流Token有效期时间戳", example = "1764751629")
    private Long expireTs;

    /**
     * 直播拉流播放地址
     */
    @Schema(description = "直播拉流播放地址", 
            example = "http://*************:30806/rtc/v1/whep/?app=live&stream=7CTDM3D00BZNVZ_165-0-7&token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String url;

    /**
     * 直播推流类型
     */
    @Schema(description = "直播推流类型", example = "srs")
    private String urlType;
    
}