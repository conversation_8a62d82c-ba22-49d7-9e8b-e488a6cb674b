package org.springblade.modules.fh.pojo.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.Map;

/**
 * RTMP协议特定参数
 *
 * <AUTHOR> AI
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "RTMP协议参数配置")
public class RtmpSchemaOption extends SchemaOption {

    @Schema(description = "RTMP推流地址", required = true)
    @NotBlank(message = "RTMP推流地址不能为空")
    @JsonProperty("url")
    private String url;

    @Override
    public String getType() {
        return "rtmp";
    }

    @Override
    public Map<String, Object> toParamsMap() {
        Map<String, Object> params = new HashMap<>();

        if (url != null) {
            params.put("url", url);
        }

        return params;
    }

    @Override
    public void validate() {
        if (url == null || url.trim().isEmpty()) {
            throw new IllegalArgumentException("RTMP协议必须提供推流地址");
        }
    }

}
