package org.springblade.modules.fh.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 飞行任务轨迹点信息VO
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "轨迹点信息")
public class FhFlightTaskTrackPointVO {

    @Schema(description = "时间戳（毫秒）")
    private Long timestamp;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "高度（米）")
    private Double height;

    /**
     * 默认构造函数
     */
    public FhFlightTaskTrackPointVO() {
    }

    /**
     * 全参构造函数
     *
     * @param timestamp 时间戳（毫秒）
     * @param latitude  纬度
     * @param longitude 经度
     * @param height    高度（米）
     */
    public FhFlightTaskTrackPointVO(Long timestamp, Double latitude, Double longitude, Double height) {
        this.timestamp = timestamp;
        this.latitude = latitude;
        this.longitude = longitude;
        this.height = height;
    }

    @Override
    public String toString() {
        return "FhFlightTaskTrackPointVO{" +
                "timestamp=" + timestamp +
                ", latitude=" + latitude +
                ", longitude=" + longitude +
                ", height=" + height +
                '}';
    }
}