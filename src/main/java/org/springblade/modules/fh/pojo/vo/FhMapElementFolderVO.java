package org.springblade.modules.fh.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 地图标注文件夹VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "地图标注文件夹信息")
public class FhMapElementFolderVO {

    /**
     * 文件夹ID
     */
    @Schema(description = "文件夹ID")
    private String id;

    /**
     * 文件夹名称
     */
    @Schema(description = "文件夹名称")
    private String name;


    /**
     * 父文件夹ID
     */
    @Schema(description = "父文件夹ID")
    private String parentId;

    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private String projectId;

    /**
     * 文件夹类型
     */
    @Schema(description = "文件夹类型")
    private String type;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer order;

    /**
     * 是否分布式
     */
    @Schema(description = "是否分布式")
    private Boolean isDistributed;

    /**
     * 是否锁定
     */
    @Schema(description = "是否锁定")
    private Boolean isLock;

    /**
     * 标注数量
     */
    @Schema(description = "标注数量")
    private Integer elementCount;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;


    /**
     * 文件夹内的地图元素列表
     */
    @Schema(description = "文件夹内的地图元素列表")
    private List<FhMapElementVO> elements;
}
