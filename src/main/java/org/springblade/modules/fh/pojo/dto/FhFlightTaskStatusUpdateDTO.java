package org.springblade.modules.fh.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
@Schema(description = "更新飞行任务状态请求参数")
public class FhFlightTaskStatusUpdateDTO {

    @Schema(description = "任务状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "任务状态不能为空")
    private String status;

	/**
	 * String taskUuid
	 */
	@Schema(description = "任务UUID", requiredMode = Schema.RequiredMode.REQUIRED)
	@NotBlank(message = "任务UUID不能为空")
	private String taskUuid;
}
