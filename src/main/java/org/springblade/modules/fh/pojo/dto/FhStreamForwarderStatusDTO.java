package org.springblade.modules.fh.pojo.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 码流转发器状态控制请求DTO
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "码流转发器状态控制请求参数")
public class FhStreamForwarderStatusDTO {

    @Schema(description = "码流转发器ID", required = true)
    @NotBlank(message = "码流转发器ID不能为空")
	@JsonProperty("converter_id")
    private String converterId;

    @Schema(description = "操作类型：1-开启，0-关闭", required = true)
    @NotNull(message = "操作类型不能为空")
    private Boolean action;

}
