package org.springblade.modules.fh.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 地图标注属性DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "地图标注属性")
public class FhMapElementPropertiesDTO {

    /**
     * 标注颜色
     */
    @Schema(description = "标注颜色", example = "#FF0000")
    private String color;

	/**
	 * 是否贴地
	 */
	@Schema(description = "是否贴地", example = "true")
	private Boolean clampToGround;

}