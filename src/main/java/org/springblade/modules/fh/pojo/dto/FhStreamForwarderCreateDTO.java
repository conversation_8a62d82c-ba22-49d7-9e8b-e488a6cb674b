package org.springblade.modules.fh.pojo.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.Valid;
import lombok.Data;

import java.util.Map;

/**
 * 创建码流转发器请求DTO
 * 支持RTMP和GB28181两种协议的不同参数结构
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "创建码流转发器请求参数")
public class FhStreamForwarderCreateDTO {

    @Schema(description = "设备SN", required = true)
    @NotBlank(message = "设备SN不能为空")
    @JsonProperty("sn")
    private String sn;

    @Schema(description = "转发器名称", required = true)
    @NotBlank(message = "转发器名称不能为空")
    @JsonProperty("converter_name")
    private String converterName;

    /**
     * camera_index
     * 相机索引，此参数可通过获取设备列表中data.list.drone.camera_list.camera_index传入。
     */
    @Schema(description = "相机索引")
    @JsonProperty("camera_index")
    private String cameraIndex;

    @Schema(description = "协议类型：rtmp或gb28181", required = true)
    @NotBlank(message = "协议类型不能为空")
    @JsonProperty("schema")
    private String schema;

    @Schema(description = "协议特定参数配置", required = true)
    @NotNull(message = "协议参数配置不能为空")
    @Valid
    @JsonProperty("schema_option")
    private SchemaOption schemaOption;

    /**
     * 获取协议特定参数
     * 通过schema_option获取协议相关参数
     *
     * @return 协议特定参数Map
     */
    public Map<String, Object> getProtocolParams() {
        if (schemaOption == null) {
            return new java.util.HashMap<>();
        }
        return schemaOption.toParamsMap();
    }

    /**
     * 验证协议参数的一致性和有效性
     */
    public void validateProtocolConsistency() {
        if (schemaOption == null) {
            throw new IllegalArgumentException("协议参数配置不能为空");
        }

        // 验证协议类型与schema_option类型的一致性
        if (!schema.equalsIgnoreCase(schemaOption.getType())) {
            throw new IllegalArgumentException(
                String.format("协议类型不匹配：schema=%s, schema_option.type=%s",
                    schema, schemaOption.getType()));
        }

        // 验证协议特定参数
        schemaOption.validate();
    }

}
