package org.springblade.modules.fh.pojo.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RepeatOption {

    /**
     * 重复间隔时间: 最小1，每几天
     * 重复间隔时间: 最小1，每几周
     * 重复间隔时间: 最小1，每几月
     */
	@JsonProperty("interval")
    private long interval;

    /**
     * 每周执行日数组，每周第几天，0-6，0=周日，1=周一
     */
    @JsonProperty("days_of_week")
    private List<Long> daysOfWeek;

    /**
     * 每月执行日数组，每月第几天，1-31
     */
    @JsonProperty("days_of_month")
    private List<Long> daysOfMonth;

    /**
     * 每月第几周，1-4
     */
    @JsonProperty("week_of_month")
    private Long weekOfMonth;

}
