package org.springblade.modules.fh.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 地图标注创建DTO (v0.9版本)
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "地图标注创建参数(v0.9版本)")
public class FhMapElementCreateV09DTO {

    /**
     * 项目UUID
     */
    @NotBlank(message = "项目UUID不能为空")
    @Schema(description = "项目UUID", example = "project-uuid-123")
    private String projectUuid;

    /**
     * 标注文件夹ID
     */
    @NotBlank(message = "标注文件夹ID不能为空")
    @Schema(description = "标注文件夹ID", example = "group-id-123")
    private String groupId;

    /**
     * 标注名称
     */
    @Schema(description = "标注名称", example = "重要区域标注")
    private String name;

    /**
     * 标注描述
     */
    @Schema(description = "标注描述", example = "这是一个重要的监控区域")
    private String desc;

    /**
     * 标注来源
     */
    @Schema(description = "标注来源", example = "1")
    private Integer elementSource;

    /**
     * 是否添加索引
     */
    @Schema(description = "是否添加索引", example = "true")
    private Boolean addIndex;

    /**
     * C2C标记
     */
    @Schema(description = "C2C标记", example = "false")
    private Boolean c2C;

    /**
     * 来源
     */
    @Schema(description = "来源", example = "1")
    private Integer source;

    /**
     * 资源项
     */
    @NotNull(message = "资源项不能为空")
    @Schema(description = "资源项")
    private FhMapElementResourceDTO resource;

}