package org.springblade.modules.fh.pojo.vo;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "DjiFhOADeviceVO对象")
public class FhOADeviceVO implements Serializable {
	@Serial
    private static final long serialVersionUID = 1L;

    private String sn;
    private String callsign;
    private JSONObject device_model;
    private Boolean device_online_status;
    private Integer mode_code;
    private JSONArray camera_list;
}
