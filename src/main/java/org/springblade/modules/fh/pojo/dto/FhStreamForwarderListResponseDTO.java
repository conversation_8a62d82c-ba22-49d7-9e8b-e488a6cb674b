package org.springblade.modules.fh.pojo.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 码流转发器列表响应DTO
 * 用于完整映射API响应中包含list字段的数据结构
 *
 * <AUTHOR> AI
 * @since 2024-01-01
 */
@Data
@Schema(description = "码流转发器列表响应")
public class FhStreamForwarderListResponseDTO {

    @Schema(description = "转发器列表")
    @JsonProperty("list")
    private List<FhStreamForwarderListItemDTO> list;

    @Schema(description = "其他可能的响应字段")
    @JsonProperty("total")
    private Integer total;

    @Schema(description = "页码")
    @JsonProperty("page")
    private Integer page;

    @Schema(description = "每页大小")
    @JsonProperty("size")
    private Integer size;

    /**
     * 获取转发器列表，如果为空则返回空列表
     *
     * @return 转发器列表
     */
    public List<FhStreamForwarderListItemDTO> getList() {
        return list != null ? list : new java.util.ArrayList<>();
    }

}