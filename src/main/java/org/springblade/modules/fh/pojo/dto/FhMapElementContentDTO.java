package org.springblade.modules.fh.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 地图标注内容DTO (GeoJSON格式)
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "地图标注内容(GeoJSON格式)")
public class FhMapElementContentDTO {

	/**
	 * 类型
	 */
	@NotBlank
	@Schema(description = "类型,目前仅支持Feature", example = "Feature")
	private String type;

    /**
     * 标注属性
     */
    @Schema(description = "标注属性")
    private FhMapElementPropertiesDTO properties;

    /**
     * 几何信息
     */
    @NotNull(message = "几何信息不能为空")
    @Schema(description = "几何信息")
    private FhMapElementGeometryDTO geometry;

}
