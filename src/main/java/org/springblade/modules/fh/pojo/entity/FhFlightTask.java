package org.springblade.modules.fh.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.time.OffsetDateTime;

/**
 * 飞行任务信息表
 * <AUTHOR> AI
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("fh_flight_task")
public class FhFlightTask extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务UUID（来自大疆API）
     */
    private String uuid;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 任务状态
     */
    private String taskStatus;

    /**
     * 设备SN
     */
    private String sn;

    /**
     * 航线ID
     */
    private String waylineUuid;

    /**
     * 任务降落使用到设备sn，若为空则表示仅使用一个设备
     */
    private String landingDockSn;

    /**
     * 任务设定的开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("begin_at")
    private OffsetDateTime beginAt;

    /**
     * 任务设定的结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("end_at")
    private OffsetDateTime endAt;

    /**
     * 任务实际执行的时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("run_at")
    private OffsetDateTime runAt;

    /**
     * 任务实际完成的时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("completed_at")
    private OffsetDateTime completedAt;

    /**
     * 任务关联的媒体文件夹ID
     */
    private Integer folderId;

    /**
     * 任务已经飞完的航点数量
     */
    private Integer currentWaypointIndex;

    /**
     * 任务的总航点数量
     */
    private Integer totalWaypoints;

    /**
     * 媒体上传状态
     * to_upload, uploading, upload_finished
     */
    private String mediaUploadStatus;

    /**
     * 断点续传状态
     * 不可续传、auto-自动续传、manual-手动续传
     */
    private String resumableStatus;

    /**
     * 是否为断点续传任务
     */
    private Boolean isBreakPointResume;

    /**
     * 操作员账号
     */
    private String operatorAccount;

    /**
     * 异常代码
     */
    private Integer exceptionCode;

    /**
     * 异常信息
     */
    private String exceptionMessage;

    /**
     * 异常发生时间
     */
    private OffsetDateTime exceptionHappenAt;

    /**
     * 异常设备SN
     */
    private String exceptionSn;

    /**
     * 飞行距离（米）
     */
    private Long flightDistance;



}
