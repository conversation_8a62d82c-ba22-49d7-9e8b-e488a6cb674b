package org.springblade.modules.fh.pojo.dto;

import lombok.Getter;

/**
 * 飞行任务距离更新结果统计
 * 
 * <AUTHOR> AI
 */
@Getter
public class FlightTaskDistanceUpdateResult {
    
    /**
     * 成功更新的任务数量
     */
    private final int successCount;
    
    /**
     * 更新失败的任务数量
     */
    private final int failCount;

    /**
     * 构造函数
     * 
     * @param successCount 成功更新的任务数量
     * @param failCount 更新失败的任务数量
     */
    public FlightTaskDistanceUpdateResult(int successCount, int failCount) {
        this.successCount = successCount;
        this.failCount = failCount;
    }

    /**
     * 获取总处理任务数量
     * 
     * @return 总处理任务数量
     */
    public int getTotalCount() {
        return successCount + failCount;
    }

    /**
     * 获取成功率
     * 
     * @return 成功率（百分比，保留两位小数）
     */
    public double getSuccessRate() {
        if (getTotalCount() == 0) {
            return 0.0;
        }
        return (double) successCount / getTotalCount() * 100;
    }

    /**
     * 获取格式化的结果描述
     * 
     * @return 结果描述字符串
     */
    public String getResultDescription() {
        return String.format("飞行任务距离更新完成，成功更新%d个任务，失败%d个任务，成功率%.2f%%",
            successCount, failCount, getSuccessRate());
    }

    @Override
    public String toString() {
        return getResultDescription();
    }
}