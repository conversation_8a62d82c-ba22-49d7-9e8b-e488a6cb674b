package org.springblade.modules.fh.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 飞行任务轨迹信息VO
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "飞行任务轨迹信息VO")
public class FhFlightTaskTrackVO {

    @Schema(description = "任务UUID")
    private String taskUuid;
    
    @Schema(description = "任务名称")
    private String name;
    
    @Schema(description = "航线UUID")
    private String waylineUuid;

    @Schema(description = "轨迹信息")
    private FhFlightTaskTrackInfoVO track;

    /**
     * 默认构造函数
     */
    public FhFlightTaskTrackVO() {
    }

	/**
     * 全参构造函数
     *
     * @param taskUuid    任务UUID
     * @param name        任务名称
     * @param waylineUuid 航线UUID
     * @param track       轨迹信息
     */
    public FhFlightTaskTrackVO(String taskUuid, String name, String waylineUuid, FhFlightTaskTrackInfoVO track) {
        this.taskUuid = taskUuid;
        this.name = name;
        this.waylineUuid = waylineUuid;
        this.track = track;
    }

    @Override
    public String toString() {
        return "FhFlightTaskTrackVO{" +
                "taskUuid='" + taskUuid + '\'' +
                ", name='" + name + '\'' +
                ", waylineUuid='" + waylineUuid + '\'' +
                ", track=" + track +
                '}';
    }
}
