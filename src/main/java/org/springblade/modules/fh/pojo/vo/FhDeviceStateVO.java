package org.springblade.modules.fh.pojo.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设备物模型状态视图对象
 * 整合了设备基本信息、设备模型和物模型状态
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "设备物模型状态视图对象")
public class FhDeviceStateVO {

    /**
     * 设备SN
     */
    @Schema(description = "设备SN")
    @JsonProperty("device_sn")
    private String deviceSn;

    /**
     * 设备模型
     */
    @Schema(description = "设备模型")
    @JsonProperty("device_model")
    private DeviceModelVO deviceModel;

    /**
     * 物模型信息
     */
    @Schema(description = "物模型信息")
    @JsonProperty("device_state")
    private DeviceStateVO deviceState;
}
