package org.springblade.modules.fh.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 地图标注创建DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "地图标注创建参数")
public class FhMapElementCreateDTO {

    /**
     * 标注名称
     */
    @NotBlank(message = "标注名称不能为空")
    @Schema(description = "标注名称", example = "重要区域标注")
    private String name;

    /**
     * 标注类型
     */
    @NotBlank(message = "标注类型不能为空")
    @Schema(description = "标注类型", example = "point")
    private String type;

    /**
     * 经度
     */
    @NotNull(message = "经度不能为空")
    @Schema(description = "经度", example = "120.123456")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @NotNull(message = "纬度不能为空")
    @Schema(description = "纬度", example = "31.123456")
    private BigDecimal latitude;

    /**
     * 标注描述
     */
    @Schema(description = "标注描述", example = "这是一个重要的监控区域")
    private String description;

    /**
     * 标注颜色
     */
    @Schema(description = "标注颜色", example = "#FF0000")
    private String color;

    /**
     * 标注图标
     */
    @Schema(description = "标注图标", example = "marker")
    private String icon;

    /**
     * 项目ID
     */
    @Schema(description = "项目ID", example = "project123")
    private String projectId;

    /**
     * 扩展属性（JSON格式）
     */
    @Schema(description = "扩展属性", example = "{\"radius\": 100}")
    private String properties;
}