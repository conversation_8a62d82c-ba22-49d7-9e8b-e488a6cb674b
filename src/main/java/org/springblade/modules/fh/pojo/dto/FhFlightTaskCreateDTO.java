package org.springblade.modules.fh.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springblade.modules.fh.pojo.entity.RepeatOption;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import static com.fasterxml.jackson.annotation.JsonFormat.Shape.NUMBER;

/**
 * 创建飞行任务请求参数
 *
 * <AUTHOR> AI
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FhFlightTaskCreateDTO implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String name;

    /**
     * 航线ID
     */
    @NotBlank(message = "航线ID不能为空")
    @JsonProperty("wayline_uuid")
    private String waylineUuid;

    /**
     * 机场SN
     */
    @NotBlank(message = "机场SN不能为空")
    private String sn;

    /**
     * 降落机场SN
     */
    @JsonProperty("landing_dock_sn")
    private String landingDockSn;

    /**
     * 返航高度
     */
    @JsonProperty("rth_altitude")
    @NotNull(message = "返航高度不能为空")
    private Integer rthAltitude = 120;

    /**
     * 返航模式
     * optimal-智能高度 preset-设定高度
     */
    @JsonProperty("rth_mode")
    private String rthMode = "optimal";

    /**
     * 任务精度
     * gps- GNSS任务 rtk-高精度RTK任务
     */
    @JsonProperty("wayline_precision_type")
    @NotBlank(message = "任务精度不能为空")
    private String waylinePrecisionType = "rtk";

    /**
     * 丢失信号后无人机动作
     * return_home-返航 continue_task-继续执行
     */
    @JsonProperty("out_of_control_action_in_flight")
    @NotBlank(message = "丢失信号后无人机动作不能为空")
    private String outOfControlActionInFlight = "return_home";

    /**
     * 自动断点续飞状态
     * 不可续飞、auto-自动续飞、manual-手动续飞
     */
    @JsonProperty("resumable_status")
    @NotBlank(message = "断点续飞状态不能为空")
    private String resumableStatus = "auto";

    /**
     * 任务类型
	 * immediate 立即任务
	 * timed 单次定时任务
	 * recurring 重复任务
	 * continuous 连续任务
     */
    @JsonProperty("task_type")
    @NotBlank(message = "任务类型不能为空")
    private String taskType;

    /**
     * 时区
     */
    @JsonProperty("time_zone")
    @NotBlank(message = "时区不能为空")
    private String timeZone;

    /**
     * 任务重复模式
	 * nonrepeating 不重复 默认
	 * daily 每几天
	 * weekly 每几周
	 * absolute_monthly 每几月（按日期）
	 * relative_monthly 每几月(按星期)
     */
	@JsonProperty("repeat_type")
    private String repeatType;

    /**
     * 任务重复间隔
     */
    @JsonProperty("repeat_option")
    private RepeatOption repeatOption;

    /**
     * 任务设定的开始时间（秒级时间戳）
     */
    @JsonFormat(shape = NUMBER)
    @JsonProperty("begin_at")
    private Long beginAt;

    /**
     * 任务设定的结束时间（秒级时间戳）
     */
	@JsonFormat(shape = NUMBER)
	@JsonProperty("end_at")
    private Long endAt;

    /**
     * 重复任务的多个开始执行的时间，秒级时间戳，必须跟“begin_at”时间同一天。
     */
    @JsonProperty("recurring_task_start_time_list")
    private List<Long> recurringTaskStartTimeList;

    /**
     * 连续任务的多个执行时间段，秒级时间戳，必须跟“begin_at”时间同一天
     */
    @JsonProperty("continuous_task_periods")
    private List<List<Long>> continuousTaskPeriods;

    /**
     * 最低执行电量
     */
    @JsonProperty("min_battery_capacity")
    @Min(value = 50, message = "最低执行电量不能低于50%")
    @Max(value = 100, message = "最低执行电量不能超过100%")
    private Integer minBatteryCapacity = 90;

}
