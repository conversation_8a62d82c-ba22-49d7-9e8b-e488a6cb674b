package org.springblade.modules.fh.pojo.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 负载信息内部类
 */
@Data
@Schema(description = "负载信息")
public class PayloadInformation implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 域
	 */
	@Schema(description = "域")
	private String domain;

	/**
	 * 类型
	 */
	@Schema(description = "类型")
	private String type;

	/**
	 * 镜头类型
	 */
	@Schema(description = "镜头类型")
	private String lensType;
}
