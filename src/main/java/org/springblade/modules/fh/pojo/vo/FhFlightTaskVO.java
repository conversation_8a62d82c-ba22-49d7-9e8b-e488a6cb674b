package org.springblade.modules.fh.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springblade.modules.fh.pojo.entity.FTExceptions;
import org.springblade.modules.fh.pojo.entity.FTOperations;

import java.util.Date;

/**
 * 飞行任务信息VO
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "飞行任务信息VO")
public class FhFlightTaskVO {

    @Schema(description = "任务ID")
    private String uuid;

    @Schema(description = "任务名称")
    private String name;

    @Schema(description = "任务类型")
    private String taskType;

    @Schema(description = "任务状态")
    private String taskStatus;

    @Schema(description = "设备SN")
    private String sn;

    @Schema(description = "航线ID")
    private String waylineUuid;

	@Schema(description = "任务降落使用到设备sn，若为空则表示仅使用一个设备")
	private String landingDockSn;

    @Schema(description = "任务设定的开始时间")
    private Date beginAt;

    @Schema(description = "任务设定的结束时间")
    private Date endAt;

	@Schema(description = "任务实际执行的时间")
    private Date runAt;

	@Schema(description = "任务实际完成的时间")
    private Date completedAt;

    @Schema(description = "任务关联的媒体文件夹ID")
    private Integer folderId;

    @Schema(description = "任务已经飞完的航点数量")
    private Integer currentWaypointIndex;

    @Schema(description = "任务的总航点数量")
    private Integer totalWaypoints;

	/**
	 * to_upload, uploading, upload_finished
	 */
    @Schema(description = "媒体上传状态")
    private String mediaUploadStatus;

	/**
	 * 不可续传、auto-自动续传、manual-手动续传
	 */
    @Schema(description = "断点续传状态")
    private String resumableStatus;

    @Schema(description = "是否为断点续传任务")
    private Boolean isBreakPointResume;

    @Schema(description = "任务操作记录")
    private FTOperations operations;

    @Schema(description = "异常信息")
    private FTExceptions exceptions;

    /**
     * 获取任务状态
     * 兼容性方法，返回taskStatus字段
     *
     * @return 任务状态
     */
    public String getStatus() {
        return this.taskStatus;
    }

    /**
     * 获取飞行任务ID
     * 兼容性方法，返回uuid字段
     *
     * @return 飞行任务ID
     */
    public String getFlightId() {
        return this.uuid;
    }

}
