package org.springblade.modules.fh.pojo.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

import static com.fasterxml.jackson.annotation.JsonFormat.Shape.NUMBER;

/**
 * 飞行任务创建信息
 * <AUTHOR> AI
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FhFlightJob {

    /**
     * 任务名称
     */
    private String name;

	/**
	 * 航线ID
	 */
	@JsonProperty("wayline_uuid")
	private String waylineUuid;

	/**
	 * 设备SN
	 */
	@JsonProperty("sn")
	private String sn;

	/**
	 * 降落机场SN
	 */
	@JsonProperty("landing_dock_sn")
	private String landingDockSn;

	/**
	 * 返航高度
	 */
	@JsonProperty("rth_altitude")
	private Integer rthAltitude;

	/**
	 * 返航模式
	 * optimal-智能高度 preset-设定高度
	 */
	@JsonProperty("rth_mode")
	private String rthMode;

	/**
	 * 任务精度
	 * gps- GNSS任务 rtk-高精度RTK任务
	 */
	@JsonProperty("wayline_precision_type")
	private String waylinePrecisionType;

	/**
	 * 丢失信号后无人机动作
	 * return_home-返航 continue_task-继续执行
	 */
	@JsonProperty("out_of_control_action_in_flight")
	private String outOfControlActionInFlight;

	/**
	 * 断点续传状态
	 * 不可续传、auto-自动续传、manual-手动续传
	 */
	@JsonProperty("resumable_status")
	private String resumableStatus;

    /**
     * 任务类型
     */
    @JsonProperty("task_type")
    private String taskType;

	/**
	 * 时区
	 */
	@JsonProperty("time_zone")
	private String timeZone;

	/**
	 * 任务重复模式
	 */
	@JsonProperty("repeat_type")
	private String repeatType;

	/**
	 * 任务重复间隔
	 */
	@JsonProperty("repeat_option")
	private RepeatOption repeatOption;

    /**
     * 任务设定的开始时间（秒级时间戳）
     * 注意：该字段需要自定义Jackson序列化器处理OffsetDateTime<->时间戳转换
     */
    @JsonFormat(shape = NUMBER)
    @JsonProperty("begin_at")
    private Long beginAt;

    /**
     * 任务设定的结束时间（秒级时间戳）
     * 注意：该字段需要自定义Jackson序列化器处理OffsetDateTime<->时间戳转换
     */
    @JsonFormat(shape = NUMBER)
    @JsonProperty("end_at")
    private Long endAt;

	/**
	 * 重复任务的多个开始执行的时间，秒级时间戳，必须跟“begin_at”时间同一天。
	 */
	@JsonProperty("recurring_task_start_time_list")
	private List<Long> recurringTaskStartTimeList;

	/**
	 * 连续任务的多个执行时间段，秒级时间戳，必须跟“begin_at”时间同一天
	 */
	@JsonProperty("continuous_task_periods")
	private List<List<Long>> continuousTaskPeriods;

    /**
     * 最低执行电量
     */
    @JsonProperty("min_battery_capacity")
    private Integer minBatteryCapacity;

}
