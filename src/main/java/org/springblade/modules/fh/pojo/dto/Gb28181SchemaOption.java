package org.springblade.modules.fh.pojo.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.Map;

/**
 * GB28181协议特定参数
 *
 * <AUTHOR> AI
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "GB28181协议参数配置")
public class Gb28181SchemaOption extends SchemaOption {

    @Schema(description = "服务器IP", required = true)
    @NotBlank(message = "服务器IP不能为空")
    @JsonProperty("server_ip")
    private String serverIp;

    @Schema(description = "SIP服务器端口", required = true)
    @NotNull(message = "服务器端口不能为空")
    @JsonProperty("server_port")
    private Integer serverPort;

    @Schema(description = "注册密码", required = true)
    @NotBlank(message = "注册密码不能为空")
    @JsonProperty("device_password")
    private String devicePassword;

    @Schema(description = "本地端口", required = true)
    @NotBlank(message = "本地端口不能为空")
    @JsonProperty("local_port")
    private String localPort;

    @Schema(description = "设备编号", required = true)
    @NotBlank(message = "设备编号不能为空")
    @JsonProperty("device_id")
    private String deviceId;

	@Schema(description = "本地通道", required = true)
	@NotBlank(message = "本地通道不能为空")
	@JsonProperty("local_channel")
	private String localChannel;

    @Override
    public String getType() {
        return "gb28181";
    }
    @Override
    public Map<String, Object> toParamsMap() {
        Map<String, Object> params = new HashMap<>();
        params.put("server_ip", serverIp);
        params.put("server_port", serverPort);
        params.put("device_password", devicePassword);
        params.put("local_port", localPort);
        params.put("device_id", deviceId);
        params.put("local_channel", localChannel);
        return params;
    }

    @Override
    public void validate() {
        // 验证服务器IP不能为空
        if (serverIp == null || serverIp.trim().isEmpty()) {
            throw new IllegalArgumentException("服务器IP不能为空");
        }
        // 验证服务器端口不能为空且必须为有效端口号
        if (serverPort == null || serverPort < 1 || serverPort > 65535) {
            throw new IllegalArgumentException("无效的服务器端口号");
        }
        // 验证设备密码不能为空
        if (devicePassword == null || devicePassword.trim().isEmpty()) {
            throw new IllegalArgumentException("设备密码不能为空");
        }
        // 验证本地端口不能为空且必须为有效端口号
        if (localPort == null || localPort.trim().isEmpty()) {
            throw new IllegalArgumentException("本地端口不能为空");
        }
        try {
            int port = Integer.parseInt(localPort);
            if (port < 1 || port > 65535) {
                throw new IllegalArgumentException("无效的本地端口号");
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("本地端口必须是有效的数字");
        }
        // 验证设备编号不能为空
        if (deviceId == null || deviceId.trim().isEmpty()) {
            throw new IllegalArgumentException("设备编号不能为空");
        }
        // 验证本地通道不能为空
        if (localChannel == null || localChannel.trim().isEmpty()) {
            throw new IllegalArgumentException("本地通道不能为空");
        }
    }
}