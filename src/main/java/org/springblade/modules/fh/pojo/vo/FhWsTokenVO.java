package org.springblade.modules.fh.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FhWsTokenVO implements Serializable {
	@Serial
	private static final long serialVersionUID = 1L;

    private String url;
    private String tokenKey;
    private String tokenValue;

}
