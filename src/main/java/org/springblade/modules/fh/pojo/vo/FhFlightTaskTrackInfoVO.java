package org.springblade.modules.fh.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 飞行任务轨迹信息VO
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "轨迹信息")
public class FhFlightTaskTrackInfoVO {

    @Schema(description = "轨迹ID")
    private String trackId;

    @Schema(description = "无人机序列号")
    private String droneSn;

    @Schema(description = "飞行距离（米）")
    private Integer flightDistance;

    @Schema(description = "飞行时长（秒）")
    private Integer flightDuration;

    @Schema(description = "轨迹点列表")
    private List<FhFlightTaskTrackPointVO> points;

    /**
     * 默认构造函数
     */
    public FhFlightTaskTrackInfoVO() {
        this.points = new ArrayList<>();
    }

    /**
     * 全参构造函数
     *
     * @param trackId        轨迹ID
     * @param droneSn        无人机序列号
     * @param flightDistance 飞行距离（米）
     * @param flightDuration 飞行时长（秒）
     * @param points         轨迹点列表
     */
    public FhFlightTaskTrackInfoVO(String trackId, String droneSn, Integer flightDistance, Integer flightDuration,
                                   List<FhFlightTaskTrackPointVO> points) {
        this.trackId = trackId;
        this.droneSn = droneSn;
        this.flightDistance = flightDistance;
        this.flightDuration = flightDuration;
        this.points = points != null ? points : new ArrayList<>();
    }

    @Override
    public String toString() {
        return "FhFlightTaskTrackInfoVO{" +
                "trackId='" + trackId + '\'' +
                ", droneSn='" + droneSn + '\'' +
                ", flightDistance=" + flightDistance +
                ", flightDuration=" + flightDuration +
                ", points=" + points +
                '}';
    }
}