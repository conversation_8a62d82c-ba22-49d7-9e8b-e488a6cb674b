package org.springblade.modules.fh.pojo.dto;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;

/**
 * 协议特定参数抽象基类
 * 用于封装不同协议的特定参数
 *
 * <AUTHOR> AI
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.DEDUCTION)
@JsonSubTypes({
    @JsonSubTypes.Type(value = RtmpSchemaOption.class),
    @JsonSubTypes.Type(value = Gb28181SchemaOption.class)
})
@Schema(description = "协议特定参数配置")
public abstract class SchemaOption {

    /**
     * 获取协议类型
     *
     * @return 协议类型
     */
    public abstract String getType();

    /**
     * 将协议特定参数转换为Map
     *
     * @return 参数Map
     */
    public abstract Map<String, Object> toParamsMap();

    /**
     * 验证协议特定参数的有效性
     *
     * @throws IllegalArgumentException 当参数无效时抛出异常
     */
    public abstract void validate();
}