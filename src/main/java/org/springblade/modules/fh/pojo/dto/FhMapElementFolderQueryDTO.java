package org.springblade.modules.fh.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 地图标注文件夹查询DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "地图标注文件夹查询参数")
public class FhMapElementFolderQueryDTO {

    /**
     * 项目UUID
     */
    @NotBlank(message = "项目UUID不能为空")
    @Schema(description = "项目UUID", required = true, example = "a5bc6015-xxxx-a824-6695db0386a2")
    private String projectId;

    /**
     * 标注文件ID（可选）
     */
    @Schema(description = "标注文件ID", example = "group-123456")
    private String groupId;

    /**
     * 是否已分发（可选）
     */
    @Schema(description = "是否已分发")
    private Boolean isDistributed;

    /**
     * 类型（可选）
     */
    @Schema(description = "类型", example = "1")
    private Integer type;
}