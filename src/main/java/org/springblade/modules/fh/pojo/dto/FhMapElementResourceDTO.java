package org.springblade.modules.fh.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 地图标注资源DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "地图标注资源")
public class FhMapElementResourceDTO {

    /**
     * 标注类型
     * 0: 点状标注
     * 1: 线状标注
     * 2: 多边形标注
     * 7: 圆形标注
     */
    @NotNull(message = "标注类型不能为空")
    @Schema(description = "标注类型", example = "0", allowableValues = {"0", "1", "2", "7"})
    private Integer type;

    /**
     * 标注内容(GeoJSON格式)
     */
    @NotNull(message = "标注内容不能为空")
    @Schema(description = "标注内容(GeoJSON格式)")
    private FhMapElementContentDTO content;

}