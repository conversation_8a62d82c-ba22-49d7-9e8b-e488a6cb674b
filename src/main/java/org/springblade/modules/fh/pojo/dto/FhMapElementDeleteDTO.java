package org.springblade.modules.fh.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 地图标注删除DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "地图标注删除参数")
public class FhMapElementDeleteDTO {

    /**
     * 项目UUID
     */
    @NotBlank(message = "项目UUID不能为空")
    @Schema(description = "项目UUID", required = true, example = "project-uuid-123")
    private String projectUuid;

    /**
     * 标注ID
     */
    @NotBlank(message = "标注ID不能为空")
    @Schema(description = "标注ID", required = true, example = "element-id-123")
    private String elementId;

}