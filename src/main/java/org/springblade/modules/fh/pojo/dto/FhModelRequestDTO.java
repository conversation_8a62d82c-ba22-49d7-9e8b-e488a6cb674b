package org.springblade.modules.fh.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 模型列表请求参数
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "模型列表请求参数")
public class FhModelRequestDTO {

    /**
     * 项目UUID
     */
    @Schema(description = "项目UUID")
    private String projectUuid;

    /**
     * 模型名称，用于模糊搜索
     */
    @Schema(description = "模型名称，用于模糊搜索")
    private String q;

    /**
     * 排序字段
     * - created_at : 创建时间排序
     * - name : 模型名称排序
     */
    @Schema(description = "排序字段，支持created_at(创建时间)和name(模型名称)")
    private String sortColumn = "created_at";

    /**
     * 排序顺序
     * - ASC : 正序
     * - DESC : 倒序
     */
    @Schema(description = "排序顺序，支持ASC(正序)和DESC(倒序)")
    private String sortType = "DESC";
}