package org.springblade.modules.fh.pojo.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

import static org.springblade.modules.fh.pojo.vo.DeviceStateComponents.*;

/**
 * 设备物模型状态视图对象
 * 包含设备的物模型数据，根据接口文档定义的字段
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "设备物模型状态")
public class DeviceStateVO {

    /**
     * 累计运行时间（秒）
     */
    @Schema(description = "累计运行时间（秒）")
    @JsonProperty("acc_time")
    private Long accTime;

    /**
     * 激活时间（时间戳）
     */
    @Schema(description = "激活时间（时间戳）")
    @JsonProperty("activation_time")
    private Long activationTime;

    /**
     * 空调状态
     */
    @Schema(description = "空调状态")
    @JsonProperty("air_conditioner")
    private AirConditioner airConditioner;

    /**
     * 是否启用空中传输
     */
    @Schema(description = "是否启用空中传输")
    @JsonProperty("air_transfer_enable")
    private Boolean airTransferEnable;

    /**
     * 报警状态
     */
    @Schema(description = "报警状态")
    @JsonProperty("alarm_state")
    private String alarmState;

    /**
     * 备用降落点信息
     */
    @Schema(description = "备用降落点信息")
    @JsonProperty("alternate_land_point")
    private AlternateLandPoint alternateLandPoint;

    /**
     * 备用电池信息
     */
    @Schema(description = "备用电池信息")
    @JsonProperty("backup_battery")
    private BackupBattery backupBattery;

    /**
     * 电池存储模式
     */
    @Schema(description = "电池存储模式")
    @JsonProperty("battery_store_mode")
    private String batteryStoreMode;

    /**
     * 兼容状态
     */
    @Schema(description = "兼容状态")
    @JsonProperty("compatible_status")
    private String compatibleStatus;

    /**
     * 舱盖状态
     */
    @Schema(description = "舱盖状态")
    @JsonProperty("cover_state")
    private String coverState;

    /**
     * 加密狗信息
     */
    @Schema(description = "加密狗信息")
    @JsonProperty("dongle_infos")
    private List<Object> dongleInfos;

    /**
     * 无人机电池维护信息
     */
    @Schema(description = "无人机电池维护信息")
    @JsonProperty("drone_battery_maintenance_info")
    private DroneBatteryMaintenanceInfo droneBatteryMaintenanceInfo;

    /**
     * 无人机充电状态
     */
    @Schema(description = "无人机充电状态")
    @JsonProperty("drone_charge_state")
    private DroneChargeState droneChargeState;

    /**
     * 无人机是否在机场内
     */
    @Schema(description = "无人机是否在机场内")
    @JsonProperty("drone_in_dock")
    private String droneInDock;

    /**
     * 紧急停止状态
     */
    @Schema(description = "紧急停止状态")
    @JsonProperty("emergency_stop_state")
    private String emergencyStopState;

    /**
     * 环境温度
     */
    @Schema(description = "环境温度")
    @JsonProperty("environment_temperature")
    private Double environmentTemperature;

    /**
     * 固件升级状态
     */
    @Schema(description = "固件升级状态")
    @JsonProperty("firmware_upgrade_status")
    private String firmwareUpgradeStatus;

    /**
     * 固件版本
     */
    @Schema(description = "固件版本")
    @JsonProperty("firmware_version")
    private String firmwareVersion;

    /**
     * 首次开机时间（毫秒时间戳）
     */
    @Schema(description = "首次开机时间（毫秒时间戳）")
    @JsonProperty("first_power_on")
    private Long firstPowerOn;

    /**
     * 飞行任务步骤代码
     */
    @Schema(description = "飞行任务步骤代码")
    @JsonProperty("flighttask_step_code")
    private String flighttaskStepCode;

    /**
     * 航向角（度）
     */
    @Schema(description = "航向角（度）")
    private Double heading;

    /**
     * 高度（米）
     */
    @Schema(description = "高度（米）")
    private Double height;

    /**
     * Home点是否有效
     */
    @Schema(description = "Home点是否有效")
    @JsonProperty("home_position_is_valid")
    private String homePositionIsValid;

    /**
     * 湿度（%）
     */
    @Schema(description = "湿度（%）")
    private Integer humidity;

    /**
     * 任务编号
     */
    @Schema(description = "任务编号")
    @JsonProperty("job_number")
    private Integer jobNumber;

    /**
     * 纬度
     */
    @Schema(description = "纬度")
    private Double latitude;

    /**
     * 直播能力
     */
    @Schema(description = "直播能力")
    @JsonProperty("live_capacity")
    private LiveCapacity liveCapacity;

    /**
     * 直播状态
     */
    @Schema(description = "直播状态")
    @JsonProperty("live_status")
    private List<LiveStatus> liveStatus;

    /**
     * 经度
     */
    @Schema(description = "经度")
    private Double longitude;

    /**
     * 维护状态
     */
    @Schema(description = "维护状态")
    @JsonProperty("maintain_status")
    private MaintainStatus maintainStatus;

    /**
     * 媒体文件详情
     */
    @Schema(description = "媒体文件详情")
    @JsonProperty("media_file_detail")
    private MediaFileDetail mediaFileDetail;

    /**
     * 工作模式代码
     */
    @Schema(description = "工作模式代码")
    @JsonProperty("mode_code")
    private String modeCode;

    /**
     * 网络状态
     */
    @Schema(description = "网络状态")
    @JsonProperty("network_state")
    private NetworkState networkState;

    /**
     * 位置状态
     */
    @Schema(description = "位置状态")
    @JsonProperty("position_state")
    private PositionState positionState;

    /**
     * 降雨状态
     */
    @Schema(description = "降雨状态")
    private String rainfall;

    /**
     * RTCM信息
     */
    @Schema(description = "RTCM信息")
    @JsonProperty("rtcm_info")
    private RtcmInfo rtcmInfo;

    /**
     * 静音模式
     */
    @Schema(description = "静音模式")
    @JsonProperty("silent_mode")
    private String silentMode;

    /**
     * 存储信息
     */
    @Schema(description = "存储信息")
    private Storage storage;

    /**
     * 子设备信息
     */
    @Schema(description = "子设备信息")
    @JsonProperty("sub_device")
    private SubDevice subDevice;

    /**
     * 补光灯状态
     */
    @Schema(description = "补光灯状态")
    @JsonProperty("supplement_light_state")
    private String supplementLightState;

    /**
     * 温度（摄氏度）
     */
    @Schema(description = "温度（摄氏度）")
    private Double temperature;

    /**
     * 物模型版本
     */
    @Schema(description = "物模型版本")
    @JsonProperty("thing_version")
    private String thingVersion;

    /**
     * 用户体验改进
     */
    @Schema(description = "用户体验改进")
    @JsonProperty("user_experience_improvement")
    private String userExperienceImprovement;

    /**
     * 风速（m/s）
     */
    @Schema(description = "风速（m/s）")
    @JsonProperty("wind_speed")
    private Double windSpeed;

    /**
     * 无线链路
     */
    @Schema(description = "无线链路")
    @JsonProperty("wireless_link")
    private WirelessLink wirelessLink;

    /**
     * 无线链路拓扑
     */
    @Schema(description = "无线链路拓扑")
    @JsonProperty("wireless_link_topo")
    private WirelessLinkTopo wirelessLinkTopo;

    /**
     * 工作电流
     */
    @Schema(description = "工作电流")
    @JsonProperty("working_current")
    private Integer workingCurrent;

    /**
     * 工作电压
     */
    @Schema(description = "工作电压")
    @JsonProperty("working_voltage")
    private Integer workingVoltage;
}
