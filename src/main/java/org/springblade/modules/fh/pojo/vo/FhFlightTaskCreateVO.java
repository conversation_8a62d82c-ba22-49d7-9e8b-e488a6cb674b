package org.springblade.modules.fh.pojo.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 飞行任务创建结果
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "飞行任务创建结果")
public class FhFlightTaskCreateVO {

    /**
     * 任务ID
     */
    @Schema(description = "任务ID")
    @JsonProperty("job_id")
    private String jobId;

    /**
     * 创建状态
     */
    @Schema(description = "创建状态")
    private String status;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String message;
    
}