package org.springblade.modules.fh.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设备任务统计结果VO
 * 按设备组织的年度任务统计数据，包含设备名称和12个月的任务数量
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "设备任务统计结果")
public class FhDeviceTaskStatsVO {

    @Schema(description = "设备名称")
    private String name;

    @Schema(description = "12个月的任务数量数据，按月份顺序从1月到12月")
    private Integer[] data;
}
