package org.springblade.modules.fh.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springblade.modules.fh.pojo.entity.PayloadInformation;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 航线信息VO
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "航线信息")
public class FhWaylineVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 航线ID
     */
    @Schema(description = "航线ID")
    private String id;

    /**
     * 兼容旧版本的航线ID字段
     */
    @Schema(description = "兼容旧版本的航线ID字段")
    private String waylineId;

    /**
     * 航线名称
     */
    @Schema(description = "航线名称")
    private String name;

    /**
     * 航线文件大小（字节）
     */
    @Schema(description = "航线文件大小（字节）")
    private Long size;

    /**
     * 兼容旧版本的文件大小字段
     */
    @Schema(description = "兼容旧版本的文件大小字段")
    private Long fileSize;

    /**
     * 负载信息
     */
    @Schema(description = "负载信息")
    private List<PayloadInformation> payloadInformation;

    /**
     * 设备型号标识
     */
    @Schema(description = "设备型号标识")
    private String deviceModelKey;

    /**
     * 航线模板类型列表
     */
    @Schema(description = "航线模板类型列表")
    private List<String> templateTypes;

    /**
     * 更新时间戳
     */
    @Schema(description = "更新时间戳")
    private Long updateTimeStamp;

    /**
     * 设备SN号
     */
    @Schema(description = "设备SN号")
    private String deviceSn;

    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private String projectId;

    /**
     * 航线类型
     */
    @Schema(description = "航线类型")
    private String templateType;

    /**
     * 航线创建时间
     */
    @Schema(description = "航线创建时间")
    private LocalDateTime createTime;

    /**
     * 航线更新时间
     */
    @Schema(description = "航线更新时间")
    private LocalDateTime updateTime;

    /**
     * 航线状态
     */
    @Schema(description = "航线状态")
    private String status;

    /**
     * 航线描述
     */
    @Schema(description = "航线描述")
    private String description;

}
