package org.springblade.modules.fh.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 创建标注文件夹DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "创建标注文件夹参数")
public class FhMapElementFolderCreateDTO {

    /**
     * 项目UUID
     */
    @NotBlank(message = "项目UUID不能为空")
    @Schema(description = "项目UUID", example = "project-uuid-123")
    private String projectUuid;

    /**
     * 标注文件夹名称
     */
    @NotBlank(message = "标注文件夹名称不能为空")
    @Schema(description = "标注文件夹名称", example = "新建文件夹")
    private String name;

    /**
     * 父标注文件夹ID
     */
    @Schema(description = "父标注文件夹ID", example = "1670c4d9-xxxx-ce5fbe8eab96")
    private String pid;
}