package org.springblade.modules.fh.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 开启直播请求DTO
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "开启直播请求参数")
public class FhLiveStreamStartDTO {

    /**
     * 设备序列号
     */
    @NotBlank(message = "设备序列号不能为空")
    @Schema(description = "设备序列号", required = true, example = "7CTDM3D00BZNVZ")
    private String sn;

    /**
     * 摄像头索引
     */
    @NotBlank(message = "摄像头索引不能为空")
    @Schema(description = "摄像头索引", required = true, example = "165-0-7")
    private String cameraIndex;

    /**
     * 直播推流Token有效期（秒）
     */
    @NotNull(message = "直播推流Token有效期不能为空")
    @Schema(description = "直播推流Token有效期，超过这个有效期直播将中止（秒）", required = true, example = "7200")
    private Integer videoExpire;

    /**
     * 直播清晰度
     */
    @NotBlank(message = "直播清晰度不能为空")
    @Pattern(regexp = "^(adaptive|smooth|ultra_high_definition)$", message = "直播清晰度必须为：adaptive（自动）、smooth（流畅）、ultra_high_definition（超清）")
    @Schema(description = "直播清晰度", required = true, example = "adaptive", 
            allowableValues = {"adaptive", "smooth", "ultra_high_definition"})
    private String qualityType;
    
}