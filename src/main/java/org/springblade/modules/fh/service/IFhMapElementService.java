package org.springblade.modules.fh.service;

import org.springblade.core.tool.api.R;
import org.springblade.modules.fh.pojo.dto.FhMapElementCreateV09DTO;
import org.springblade.modules.fh.pojo.dto.FhMapElementDeleteDTO;
import org.springblade.modules.fh.pojo.dto.FhMapElementFolderCreateDTO;
import org.springblade.modules.fh.pojo.dto.FhMapElementFolderQueryDTO;
import org.springblade.modules.fh.pojo.vo.FhMapElementDeleteVO;
import org.springblade.modules.fh.pojo.vo.FhMapElementFolderCreateVO;
import org.springblade.modules.fh.pojo.vo.FhMapElementFolderVO;
import org.springblade.modules.fh.pojo.vo.FhMapElementV09VO;

import java.util.List;

/**
 * 地图标注服务接口
 *
 * <AUTHOR>
 */
public interface IFhMapElementService {

    /**
     * 创建地图标注(v0.9版本)
     *
     * @param createDTO 创建参数
     * @return 创建结果
     */
    R<FhMapElementV09VO> createMapElementV09(FhMapElementCreateV09DTO createDTO);

    /**
     * 删除地图标注
     *
     * @param deleteDTO 删除参数
     * @return 删除结果
     */
    R<FhMapElementDeleteVO> deleteMapElement(FhMapElementDeleteDTO deleteDTO);

    /**
     * 获取标注文件夹列表
     *
     * @param queryDTO 查询参数
     * @return 文件夹列表
     */
    List<FhMapElementFolderVO> getMapElementFolders(FhMapElementFolderQueryDTO queryDTO);

    /**
     * 创建标注文件夹
     *
     * @param createDTO 创建参数
     * @return 创建结果
     */
    R<FhMapElementFolderCreateVO> createMapElementFolder(FhMapElementFolderCreateDTO createDTO);

}
