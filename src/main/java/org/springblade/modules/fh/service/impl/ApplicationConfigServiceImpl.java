package org.springblade.modules.fh.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.minio.MinioClient;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springblade.modules.fh.config.ApplicationConfig;
import org.springblade.modules.fh.dto.*;
import org.springblade.modules.fh.service.IApplicationConfigService;
import org.springblade.modules.fh.service.IFhMapElementService;
import org.springblade.modules.system.pojo.entity.Param;
import org.springblade.modules.system.service.IParamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

/**
 * 应用配置服务实现类
 * 支持Skii MinIO、MQTT、FH-SDK、EJ-FH-SERVER等多种配置的管理
 *
 * <AUTHOR>
@Slf4j
@Service
public class ApplicationConfigServiceImpl implements IApplicationConfigService {

    @Autowired
    private ApplicationConfig applicationConfig;

    @Autowired
    private IParamService paramService;

    @Autowired
    private IFhMapElementService fhMapElementService;

    /**
     * 配置备份存储（用于回滚）
     */
    private final Map<String, String> configBackup = new ConcurrentHashMap<>();

    // ==================== Skii MinIO配置服务方法实现 ====================

    @Override
    public String getSkiiMinioEndpoint() {
        return applicationConfig.getSkiiMinioEndpoint();
    }

    @Override
    public String getSkiiMinioAccessKey() {
        return applicationConfig.getSkiiMinioAccessKey();
    }

    @Override
    public String getSkiiMinioSecretKey() {
        return applicationConfig.getSkiiMinioSecretKey();
    }

    @Override
    public String getSkiiMinioBucket() {
        return applicationConfig.getSkiiMinioBucket();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSkiiMinioEndpoint(String endpoint) {
        return updateConfigValue(ApplicationConfig.SKII_MINIO_ENDPOINT_KEY, endpoint, "Skii MinIO服务端点");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSkiiMinioAccessKey(String accessKey) {
        return updateConfigValue(ApplicationConfig.SKII_MINIO_ACCESS_KEY_KEY, accessKey, "Skii MinIO访问密钥");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSkiiMinioSecretKey(String secretKey) {
        return updateConfigValue(ApplicationConfig.SKII_MINIO_SECRET_KEY_KEY, secretKey, "Skii MinIO秘密密钥");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSkiiMinioBucket(String bucket) {
        return updateConfigValue(ApplicationConfig.SKII_MINIO_BUCKET_KEY, bucket, "Skii MinIO存储桶名称");
    }

    // ==================== MQTT BASIC配置服务方法实现 ====================

    @Override
    public String getMqttBasicProtocol() {
        return applicationConfig.getMqttBasicProtocol();
    }

    @Override
    public String getMqttBasicHost() {
        return applicationConfig.getMqttBasicHost();
    }

    @Override
    public String getMqttBasicPort() {
        return applicationConfig.getMqttBasicPort();
    }

    @Override
    public String getMqttBasicUsername() {
        return applicationConfig.getMqttBasicUsername();
    }

    @Override
    public String getMqttBasicPassword() {
        return applicationConfig.getMqttBasicPassword();
    }

    @Override
    public String getMqttBasicClientId() {
        return applicationConfig.getMqttBasicClientId();
    }

    @Override
    public String getMqttBasicPath() {
        return applicationConfig.getMqttBasicPath();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMqttBasicProtocol(String protocol) {
        return updateConfigValue(ApplicationConfig.MQTT_BASIC_PROTOCOL_KEY, protocol, "MQTT BASIC协议");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMqttBasicHost(String host) {
        return updateConfigValue(ApplicationConfig.MQTT_BASIC_HOST_KEY, host, "MQTT BASIC主机");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMqttBasicPort(String port) {
        return updateConfigValue(ApplicationConfig.MQTT_BASIC_PORT_KEY, port, "MQTT BASIC端口");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMqttBasicUsername(String username) {
        return updateConfigValue(ApplicationConfig.MQTT_BASIC_USERNAME_KEY, username, "MQTT BASIC用户名");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMqttBasicPassword(String password) {
        return updateConfigValue(ApplicationConfig.MQTT_BASIC_PASSWORD_KEY, password, "MQTT BASIC密码");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMqttBasicClientId(String clientId) {
        return updateConfigValue(ApplicationConfig.MQTT_BASIC_CLIENT_ID_KEY, clientId, "MQTT BASIC客户端ID");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMqttBasicPath(String path) {
        return updateConfigValue(ApplicationConfig.MQTT_BASIC_PATH_KEY, path, "MQTT BASIC路径");
    }

    // ==================== MQTT DRC配置服务方法实现 ====================

    @Override
    public String getMqttDrcProtocol() {
        return applicationConfig.getMqttDrcProtocol();
    }

    @Override
    public String getMqttDrcHost() {
        return applicationConfig.getMqttDrcHost();
    }

    @Override
    public String getMqttDrcPort() {
        return applicationConfig.getMqttDrcPort();
    }

    @Override
    public String getMqttDrcPath() {
        return applicationConfig.getMqttDrcPath();
    }

    @Override
    public String getMqttDrcUsername() {
        return applicationConfig.getMqttDrcUsername();
    }

    @Override
    public String getMqttDrcPassword() {
        return applicationConfig.getMqttDrcPassword();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMqttDrcProtocol(String protocol) {
        return updateConfigValue(ApplicationConfig.MQTT_DRC_PROTOCOL_KEY, protocol, "MQTT DRC协议");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMqttDrcHost(String host) {
        return updateConfigValue(ApplicationConfig.MQTT_DRC_HOST_KEY, host, "MQTT DRC主机");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMqttDrcPort(String port) {
        return updateConfigValue(ApplicationConfig.MQTT_DRC_PORT_KEY, port, "MQTT DRC端口");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMqttDrcPath(String path) {
        return updateConfigValue(ApplicationConfig.MQTT_DRC_PATH_KEY, path, "MQTT DRC路径");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMqttDrcUsername(String username) {
        return updateConfigValue(ApplicationConfig.MQTT_DRC_USERNAME_KEY, username, "MQTT DRC用户名");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMqttDrcPassword(String password) {
        return updateConfigValue(ApplicationConfig.MQTT_DRC_PASSWORD_KEY, password, "MQTT DRC密码");
    }

    // ==================== FH-SDK配置服务方法实现 ====================

    @Override
    public String getFhSdkMqttInboundTopic() {
        return applicationConfig.getFhSdkMqttInboundTopic();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFhSdkMqttInboundTopic(String inboundTopic) {
        return updateConfigValue(ApplicationConfig.FH_SDK_MQTT_INBOUND_TOPIC_KEY, inboundTopic, "FH-SDK MQTT入站主题");
    }

    // ==================== EJ-FH-SERVER配置服务方法实现 ====================

    @Override
    public String getEjFhServerUrl() {
        return applicationConfig.getEjFhServerUrl();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateEjFhServerUrl(String url) {
        return updateConfigValue(ApplicationConfig.EJ_FH_SERVER_URL_KEY, url, "EJ-FH-SERVER URL");
    }

    // ==================== 标注文件夹ID配置服务方法实现 ====================

    @Override
    public String getAnnotationFolderId() {
        return applicationConfig.getAnnotationFolderId();
    }

    @Override
    public boolean updateAnnotationFolderId(String folderId) {
        return updateConfigValue(ApplicationConfig.ANNOTATION_FOLDER_ID_KEY, folderId, "标注文件夹ID");
    }

    // ==================== 通用配置管理方法实现 ====================

    @Override
    public void refreshCache(String paramKey) {
        applicationConfig.refreshCache(paramKey);
    }

    @Override
    public void refreshAllCache() {
        applicationConfig.refreshAllCache();
    }

    @Override
    public void warmUpCache() {
        applicationConfig.warmUpCache();
    }



    // ==================== 私有辅助方法 ====================

    /**
     * 更新配置值的通用方法
     *
     * @param paramKey 参数键
     * @param paramValue 参数值
     * @param paramName 参数名称
     * @return 是否成功
     */
    private boolean updateConfigValue(String paramKey, String paramValue, String paramName) {
        try {
            // 查找现有配置
            Param existingParam = paramService.getOne(
                Wrappers.lambdaQuery(Param.class)
                    .eq(Param::getParamKey, paramKey)
                    .last("limit 1")
            );

            if (existingParam != null) {
                // 更新现有配置
                existingParam.setParamValue(paramValue);
                boolean updated = paramService.updateById(existingParam);
                if (updated) {
                    // 直接更新缓存（而不是删除）
                    applicationConfig.updateCacheDirectly(paramKey, paramValue);
                    log.info("[CONFIG] [INFO] 配置更新成功: key={}, value={}", paramKey, paramValue);
                    return true;
                } else {
                    log.error("[CONFIG] [ERROR] 配置更新失败: key={}", paramKey);
                    return false;
                }
            } else {
                // 创建新配置
                Param newParam = new Param();
                newParam.setParamKey(paramKey);
                newParam.setParamValue(paramValue);
                newParam.setParamName(paramName);
                newParam.setRemark(paramName + "配置");
                newParam.setStatus(1);

                boolean saved = paramService.save(newParam);
                if (saved) {
                    // 直接更新缓存（而不是删除）
                    applicationConfig.updateCacheDirectly(paramKey, paramValue);
                    log.info("[CONFIG] [INFO] 配置创建成功: key={}, value={}", paramKey, paramValue);
                    return true;
                } else {
                    log.error("[CONFIG] [ERROR] 配置创建失败: key={}", paramKey);
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("[CONFIG] [ERROR] 配置更新异常: key={}, error={}", paramKey, e.getMessage(), e);
            return false;
        }
    }

    // ==================== 配置验证和安全更新方法实现 ====================


    @Override
    public ConfigValidationResult validateMinioConfig(String endpoint, String accessKey, String secretKey, String bucket) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("[CONFIG] [VALIDATION] 开始MinIO配置验证: endpoint={}", endpoint);

            // 基本参数验证
            if (endpoint == null || endpoint.trim().isEmpty()) {
                return ConfigValidationResult.failure("MinIO服务端点不能为空",
                    System.currentTimeMillis() - startTime, "endpoint validation failed");
            }

            if (accessKey == null || accessKey.trim().isEmpty()) {
                return ConfigValidationResult.failure("MinIO访问密钥不能为空",
                    System.currentTimeMillis() - startTime, "accessKey validation failed");
            }

            if (secretKey == null || secretKey.trim().isEmpty()) {
                return ConfigValidationResult.failure("MinIO秘密密钥不能为空",
                    System.currentTimeMillis() - startTime, "secretKey validation failed");
            }

            if (bucket == null || bucket.trim().isEmpty()) {
                return ConfigValidationResult.failure("MinIO存储桶名称不能为空",
                    System.currentTimeMillis() - startTime, "bucket validation failed");
            }

            // 连接性验证
            try {
                MinioClient minioClient = MinioClient.builder()
                        .endpoint(endpoint)
                        .credentials(accessKey, secretKey)
                        .build();

                // 尝试列出存储桶
                minioClient.listBuckets();

                // 检查指定存储桶是否存在
                boolean bucketExists = minioClient.bucketExists(
                    io.minio.BucketExistsArgs.builder()
                        .bucket(bucket)
                        .build());

                long validationTime = System.currentTimeMillis() - startTime;

                if (bucketExists) {
                    log.info("[CONFIG] [VALIDATION] MinIO配置验证成功，耗时: {}ms", validationTime);
                    return ConfigValidationResult.success(validationTime,
                        "MinIO连接成功，存储桶存在");
                } else {
                    log.warn("[CONFIG] [VALIDATION] MinIO存储桶不存在: {}", bucket);
                    return ConfigValidationResult.failure("存储桶不存在: " + bucket,
                        validationTime, "bucket not found");
                }

            } catch (Exception e) {
                long validationTime = System.currentTimeMillis() - startTime;
                log.error("[CONFIG] [VALIDATION] MinIO连接验证失败: {}", e.getMessage());
                return ConfigValidationResult.failure("MinIO连接失败: " + e.getMessage(),
                    validationTime, "connection failed");
            }

        } catch (Exception e) {
            long validationTime = System.currentTimeMillis() - startTime;
            log.error("[CONFIG] [VALIDATION] MinIO配置验证异常: {}", e.getMessage(), e);
            return ConfigValidationResult.failure("验证异常: " + e.getMessage(),
                validationTime, "validation exception");
        }
    }

    @Override
    public ConfigValidationResult validateMqttConfig(String protocol, String host, String port, String username, String password) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("[CONFIG] [VALIDATION] 开始MQTT配置验证: host={}, port={}", host, port);

            // 基本参数验证
            if (host == null || host.trim().isEmpty()) {
                return ConfigValidationResult.failure("MQTT主机不能为空",
                    System.currentTimeMillis() - startTime, "host validation failed");
            }

            if (port == null || port.trim().isEmpty()) {
                return ConfigValidationResult.failure("MQTT端口不能为空",
                    System.currentTimeMillis() - startTime, "port validation failed");
            }

            int portNum;
            try {
                portNum = Integer.parseInt(port);
                if (portNum <= 0 || portNum > 65535) {
                    return ConfigValidationResult.failure("MQTT端口号无效: " + port,
                        System.currentTimeMillis() - startTime, "invalid port range");
                }
            } catch (NumberFormatException e) {
                return ConfigValidationResult.failure("MQTT端口号格式错误: " + port,
                    System.currentTimeMillis() - startTime, "port format error");
            }

            // 连接性验证
            String clientId = "config-validator-" + System.currentTimeMillis();
            String brokerUrl = protocol + "://" + host + ":" + port;

            try {
                MemoryPersistence persistence = new MemoryPersistence();
                MqttClient mqttClient = new MqttClient(brokerUrl, clientId, persistence);

                MqttConnectOptions connOpts = new MqttConnectOptions();
                connOpts.setCleanSession(true);
                connOpts.setConnectionTimeout(10); // 10秒超时

                if (username != null && !username.trim().isEmpty()) {
                    connOpts.setUserName(username);
                }
                if (password != null && !password.trim().isEmpty()) {
                    connOpts.setPassword(password.toCharArray());
                }

                // 尝试连接
                mqttClient.connect(connOpts);

                // 连接成功，立即断开
                mqttClient.disconnect();
                mqttClient.close();

                long validationTime = System.currentTimeMillis() - startTime;
                log.info("[CONFIG] [VALIDATION] MQTT配置验证成功，耗时: {}ms", validationTime);
                return ConfigValidationResult.success(validationTime,
                    "MQTT连接成功");

            } catch (Exception e) {
                long validationTime = System.currentTimeMillis() - startTime;
                log.error("[CONFIG] [VALIDATION] MQTT连接验证失败: {}", e.getMessage());
                return ConfigValidationResult.failure("MQTT连接失败: " + e.getMessage(),
                    validationTime, "connection failed");
            }

        } catch (Exception e) {
            long validationTime = System.currentTimeMillis() - startTime;
            log.error("[CONFIG] [VALIDATION] MQTT配置验证异常: {}", e.getMessage(), e);
            return ConfigValidationResult.failure("验证异常: " + e.getMessage(),
                validationTime, "validation exception");
        }
    }

    /**
     * 验证标注文件夹ID
     *
     * @param folderId 文件夹ID
     * @return 是否有效
     */
    private boolean validateAnnotationFolderId(String folderId) {
        try {
            // 基本格式验证
            if (folderId == null || folderId.trim().isEmpty()) {
                log.warn("[CONFIG] [VALIDATION] 标注文件夹ID为空");
                return false;
            }

            // 长度验证（假设合理的ID长度范围）
            String trimmedId = folderId.trim();
            if (trimmedId.length() < 1 || trimmedId.length() > 100) {
                log.warn("[CONFIG] [VALIDATION] 标注文件夹ID长度无效: {}", trimmedId.length());
                return false;
            }

            // 字符验证（允许字母、数字、下划线、连字符）
            if (!trimmedId.matches("^[a-zA-Z0-9_-]+$")) {
                log.warn("[CONFIG] [VALIDATION] 标注文件夹ID包含无效字符: {}", trimmedId);
                return false;
            }

            log.info("[CONFIG] [VALIDATION] 标注文件夹ID验证通过: {}", trimmedId);
            return true;

        } catch (Exception e) {
            log.error("[CONFIG] [VALIDATION] 标注文件夹ID验证异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rollbackConfigs(List<String> configKeys) {
        try {
            log.info("[CONFIG] [ROLLBACK] 开始回滚配置: keys={}", configKeys);

            boolean allSuccess = true;
            for (String configKey : configKeys) {
                String backupValue = configBackup.get(configKey);
                if (backupValue != null) {
                    boolean success = updateConfigValueSafe(configKey, backupValue, "回滚配置");
                    if (!success) {
                        allSuccess = false;
                        log.error("[CONFIG] [ROLLBACK] 配置回滚失败: key={}", configKey);
                    } else {
                        log.info("[CONFIG] [ROLLBACK] 配置回滚成功: key={}", configKey);
                    }
                } else {
                    log.warn("[CONFIG] [ROLLBACK] 未找到备份配置: key={}", configKey);
                    allSuccess = false;
                }
            }

            if (allSuccess) {
                log.info("[CONFIG] [ROLLBACK] 所有配置回滚成功");
            } else {
                log.error("[CONFIG] [ROLLBACK] 部分配置回滚失败");
            }

            return allSuccess;

        } catch (Exception e) {
            log.error("[CONFIG] [ROLLBACK] 配置回滚异常: {}", e.getMessage(), e);
            return false;
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 备份当前配置
     */
    private void backupCurrentConfigs(List<String> configKeys) {
        for (String configKey : configKeys) {
            try {
                String currentValue = paramService.getValue(configKey);
                if (currentValue != null) {
                    configBackup.put(configKey, currentValue);
                    log.debug("[CONFIG] [BACKUP] 配置备份成功: key={}", configKey);
                }
            } catch (Exception e) {
                log.warn("[CONFIG] [BACKUP] 配置备份失败: key={}, error={}", configKey, e.getMessage());
            }
        }
    }

    /**
     * 安全的配置更新方法（不抛出异常）
     */
    private boolean updateConfigValueSafe(String paramKey, String paramValue, String paramName) {
        try {
            return updateConfigValue(paramKey, paramValue, paramName);
        } catch (Exception e) {
            log.error("[CONFIG] [SAFE_UPDATE] 配置更新异常: key={}, error={}", paramKey, e.getMessage());
            return false;
        }
    }

    /**
     * 通用的配置验证方法
     */
    private ConfigValidationResult validateConfigWithConnection(String configType,
            Supplier<ConfigValidationResult> validator) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("[CONFIG] [VALIDATION] 开始{}配置验证", configType);
            return validator.get();
        } catch (Exception e) {
            long validationTime = System.currentTimeMillis() - startTime;
            log.error("[CONFIG] [VALIDATION] {}配置验证异常: {}", configType, e.getMessage(), e);
            return ConfigValidationResult.failure("验证异常: " + e.getMessage(),
                validationTime, "validation exception");
        }
    }

    /**
     * 通用的批量配置更新方法
     */
    private ConfigUpdateResult performConfigUpdate(String configType,
            Supplier<Map<String, String>> configUpdater) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("[CONFIG] [{}] 开始更新{}配置", configType.toUpperCase(), configType);
            Map<String, String> updateDetails = configUpdater.get();
            long timeTaken = System.currentTimeMillis() - startTime;
            log.info("[CONFIG] [{}] {}配置更新成功，耗时: {}ms", configType.toUpperCase(), configType, timeTaken);
            return ConfigUpdateResult.success(updateDetails, timeTaken);
        } catch (Exception e) {
            long timeTaken = System.currentTimeMillis() - startTime;
            log.error("[CONFIG] [{}] {}配置更新失败", configType.toUpperCase(), configType, e);
            return ConfigUpdateResult.failure(configType + "配置更新失败: " + e.getMessage());
        }
    }

    /**
     * 脱敏处理敏感信息
     */
    private String maskSensitive(String sensitiveInfo) {
        if (sensitiveInfo == null || sensitiveInfo.length() <= 4) {
            return "****";
        }
        return sensitiveInfo.substring(0, 2) + "****" + sensitiveInfo.substring(sensitiveInfo.length() - 2);
    }

    // ==================== 批量配置验证和更新方法实现 ====================

    @Override
    public BatchConfigValidationResult validateBatchConfigs(BatchConfigValidationRequest request) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("[CONFIG] [BATCH_VALIDATION] 开始批量配置验证，配置数量: {}", request.getConfigCount());

            // 检查是否有配置需要验证
            if (!request.hasAnyConfig()) {
                long totalTime = System.currentTimeMillis() - startTime;
                return BatchConfigValidationResult.failure("没有提供任何配置进行验证", totalTime);
            }

            BatchConfigValidationResult result = BatchConfigValidationResult.builder()
                    .totalTimeMs(0L)
                    .build();

            // 验证Skii MinIO配置
            if (request.getSkiiMinio() != null) {
                log.info("[CONFIG] [BATCH_VALIDATION] 验证Skii MinIO配置");
                ConfigValidationResult minioValidation = validateMinioConfig(
                        request.getSkiiMinio().getEndpoint(),
                        request.getSkiiMinio().getAccessKey(),
                        request.getSkiiMinio().getSecretKey(),
                        request.getSkiiMinio().getBucket());
                result.addValidationResult("skiiMinio", minioValidation);
            }

            // 验证MQTT Basic配置
            if (request.getMqttBasic() != null) {
                log.info("[CONFIG] [BATCH_VALIDATION] 验证MQTT Basic配置");
                ConfigValidationResult mqttBasicValidation = validateMqttConfig(
                        request.getMqttBasic().getProtocol(),
                        request.getMqttBasic().getHost(),
                        request.getMqttBasic().getPort(),
                        request.getMqttBasic().getUsername(),
                        request.getMqttBasic().getPassword());
                result.addValidationResult("mqttBasic", mqttBasicValidation);
            }

            // 验证MQTT DRC配置
            if (request.getMqttDrc() != null) {
                log.info("[CONFIG] [BATCH_VALIDATION] 验证MQTT DRC配置");
                ConfigValidationResult mqttDrcValidation = validateMqttConfig(
                        request.getMqttDrc().getProtocol(),
                        request.getMqttDrc().getHost(),
                        request.getMqttDrc().getPort(),
                        request.getMqttDrc().getUsername(),
                        request.getMqttDrc().getPassword());
                result.addValidationResult("mqttDrc", mqttDrcValidation);
            }

            // FH-SDK和EJ-FH-SERVER配置无需连接验证，直接标记为成功
            if (request.getFhSdk() != null) {
                result.addValidationResult("fhSdk", ConfigValidationResult.success(0L, "FH-SDK配置验证通过"));
            }
            if (request.getEjFhServer() != null) {
                result.addValidationResult("ejFhServer", ConfigValidationResult.success(0L, "EJ-FH-SERVER配置验证通过"));
            }

            // 验证标注文件夹ID配置
            if (request.getAnnotationFolder() != null) {
                log.info("[CONFIG] [BATCH_VALIDATION] 验证标注文件夹ID配置");
                boolean isValid = validateAnnotationFolderId(request.getAnnotationFolder().getFolderId());
                if (isValid) {
                    result.addValidationResult("annotationFolder", ConfigValidationResult.success(0L, "标注文件夹ID配置验证通过"));
                } else {
                    result.addValidationResult("annotationFolder", ConfigValidationResult.failure("标注文件夹ID无效或不存在", 0L, "annotation folder validation failed"));
                }
            }

            long totalTime = System.currentTimeMillis() - startTime;
            result.setTotalTimeMs(totalTime);
            result.calculateStats();

            log.info("[CONFIG] [BATCH_VALIDATION] 批量配置验证完成，成功: {}/{}, 耗时: {}ms",
                    result.getSuccessCount(), result.getTotalCount(), totalTime);

            return result;

        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("[CONFIG] [BATCH_VALIDATION] 批量配置验证异常: {}", e.getMessage(), e);
            return BatchConfigValidationResult.failure("批量配置验证异常: " + e.getMessage(), totalTime);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchConfigUpdateResult updateBatchConfigs(BatchConfigUpdateRequest request) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("[CONFIG] [BATCH_UPDATE_ONLY] 开始批量配置更新（跳过验证），配置数量: {}", request.getConfigCount());

            // 检查是否有配置需要更新
            if (!request.hasAnyConfig()) {
                long totalTime = System.currentTimeMillis() - startTime;
                return BatchConfigUpdateResult.failure("没有提供任何配置进行更新", totalTime);
            }

            // 直接执行更新操作，不进行验证
            BatchConfigUpdateResult result = BatchConfigUpdateResult.builder()
                    .totalTimeMs(0L)
                    .build();

            // 更新Skii MinIO配置
            if (request.getSkiiMinio() != null) {
                log.info("[CONFIG] [BATCH_UPDATE_ONLY] 更新Skii MinIO配置");
                ConfigUpdateResult minioResult = performSkiiMinioConfigUpdate(request.getSkiiMinio());
                result.addConfigResult("skiiMinio", minioResult);
            }

            // 更新MQTT Basic配置
            if (request.getMqttBasic() != null) {
                log.info("[CONFIG] [BATCH_UPDATE_ONLY] 更新MQTT Basic配置");
                ConfigUpdateResult mqttBasicResult = performMqttBasicConfigUpdate(request.getMqttBasic());
                result.addConfigResult("mqttBasic", mqttBasicResult);
            }

            // 更新MQTT DRC配置
            if (request.getMqttDrc() != null) {
                log.info("[CONFIG] [BATCH_UPDATE_ONLY] 更新MQTT DRC配置");
                ConfigUpdateResult mqttDrcResult = updateMqttDrcConfigBatch(request.getMqttDrc());
                result.addConfigResult("mqttDrc", mqttDrcResult);
            }

            // 更新FH-SDK配置
            if (request.getFhSdk() != null) {
                log.info("[CONFIG] [BATCH_UPDATE_ONLY] 更新FH-SDK配置");
                ConfigUpdateResult fhSdkResult = updateFhSdkConfigBatch(request.getFhSdk());
                result.addConfigResult("fhSdk", fhSdkResult);
            }

            // 更新EJ-FH-SERVER配置
            if (request.getEjFhServer() != null) {
                log.info("[CONFIG] [BATCH_UPDATE_ONLY] 更新EJ-FH-SERVER配置");
                ConfigUpdateResult ejFhServerResult = updateEjFhServerConfigBatch(request.getEjFhServer());
                result.addConfigResult("ejFhServer", ejFhServerResult);
            }

            // 更新标注文件夹ID配置
            if (request.getAnnotationFolder() != null) {
                log.info("[CONFIG] [BATCH_UPDATE_ONLY] 更新标注文件夹ID配置");
                ConfigUpdateResult annotationFolderResult = updateAnnotationFolderConfigBatch(request.getAnnotationFolder());
                result.addConfigResult("annotationFolder", annotationFolderResult);
            }

            long totalTime = System.currentTimeMillis() - startTime;
            result.setTotalTimeMs(totalTime);
            result.calculateStatistics();

            log.info("[CONFIG] [BATCH_UPDATE_ONLY] 批量配置更新完成，成功: {}/{}, 耗时: {}ms",
                    result.getSuccessCount(), result.getTotalCount(), totalTime);

            return result;

        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("[CONFIG] [BATCH_UPDATE_ONLY] 批量配置更新异常: {}", e.getMessage(), e);
            return BatchConfigUpdateResult.failure("批量配置更新异常: " + e.getMessage(), totalTime);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchConfigUpdateResult validateAndUpdateBatchConfigs(BatchConfigUpdateRequest request) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("[CONFIG] [BATCH_UPDATE] 开始批量配置验证和更新，配置数量: {}", request.getConfigCount());

            // 检查是否有配置需要更新
            if (!request.hasAnyConfig()) {
                long totalTime = System.currentTimeMillis() - startTime;
                return BatchConfigUpdateResult.failure("没有提供任何配置进行更新", totalTime);
            }

            // 第一阶段：验证所有配置
            log.info("[CONFIG] [BATCH_UPDATE] 第一阶段：验证所有配置");
            Map<String, ConfigValidationResult> validationResults = new HashMap<>();
            boolean allValidationsPassed = true;
            StringBuilder validationErrors = new StringBuilder();

            // 验证Skii MinIO配置
            if (request.getSkiiMinio() != null) {
                log.info("[CONFIG] [BATCH_UPDATE] 验证Skii MinIO配置");
                ConfigValidationResult minioValidation = validateMinioConfig(
                        request.getSkiiMinio().getEndpoint(),
                        request.getSkiiMinio().getAccessKey(),
                        request.getSkiiMinio().getSecretKey(),
                        request.getSkiiMinio().getBucket());
                validationResults.put("skiiMinio", minioValidation);
                if (!minioValidation.isValid()) {
                    allValidationsPassed = false;
                    validationErrors.append("Skii MinIO配置验证失败: ").append(minioValidation.getFailureReason()).append("; ");
                }
            }

            // 验证MQTT Basic配置
            if (request.getMqttBasic() != null) {
                log.info("[CONFIG] [BATCH_UPDATE] 验证MQTT Basic配置");
                ConfigValidationResult mqttValidation = validateMqttConfig(
                        request.getMqttBasic().getProtocol(),
                        request.getMqttBasic().getHost(),
                        request.getMqttBasic().getPort(),
                        request.getMqttBasic().getUsername(),
                        request.getMqttBasic().getPassword());
                validationResults.put("mqttBasic", mqttValidation);
                if (!mqttValidation.isValid()) {
                    allValidationsPassed = false;
                    validationErrors.append("MQTT Basic配置验证失败: ").append(mqttValidation.getFailureReason()).append("; ");
                }
            }

            // MQTT DRC、FH-SDK、EJ-FH-SERVER配置无需复杂验证，直接标记为通过
            if (request.getMqttDrc() != null) {
                validationResults.put("mqttDrc", ConfigValidationResult.success(0L, "MQTT DRC配置验证通过"));
            }
            if (request.getFhSdk() != null) {
                validationResults.put("fhSdk", ConfigValidationResult.success(0L, "FH-SDK配置验证通过"));
            }
            if (request.getEjFhServer() != null) {
                validationResults.put("ejFhServer", ConfigValidationResult.success(0L, "EJ-FH-SERVER配置验证通过"));
            }

            // 验证标注文件夹ID配置
            if (request.getAnnotationFolder() != null) {
                log.info("[CONFIG] [BATCH_UPDATE] 验证标注文件夹ID配置");
                boolean isValid = validateAnnotationFolderId(request.getAnnotationFolder().getFolderId());
                if (isValid) {
                    validationResults.put("annotationFolder", ConfigValidationResult.success(0L, "标注文件夹ID配置验证通过"));
                } else {
                    allValidationsPassed = false;
                    validationErrors.append("标注文件夹ID配置验证失败: 标注文件夹ID无效或不存在; ");
                    validationResults.put("annotationFolder", ConfigValidationResult.failure("标注文件夹ID无效或不存在", 0L, "annotation folder validation failed"));
                }
            }

            // 如果任何验证失败，直接返回失败结果
            if (!allValidationsPassed) {
                long totalTime = System.currentTimeMillis() - startTime;
                log.warn("[CONFIG] [BATCH_UPDATE] 配置验证失败，停止更新操作: {}", validationErrors.toString());

                BatchConfigUpdateResult result = BatchConfigUpdateResult.builder()
                        .totalTimeMs(totalTime)
                        .configResults(new HashMap<>())
                        .build();

                // 将验证结果转换为ConfigUpdateResult
                for (Map.Entry<String, ConfigValidationResult> entry : validationResults.entrySet()) {
                    ConfigValidationResult validation = entry.getValue();
                    ConfigUpdateResult updateResult = validation.isValid()
                            ? ConfigUpdateResult.success(new HashMap<>(), validation.getValidationTimeMs())
                            : ConfigUpdateResult.validationFailure(validation, validation.getValidationTimeMs());
                    result.addConfigResult(entry.getKey(), updateResult);
                }

                result.calculateStatistics();
                return result;
            }

            // 第二阶段：所有验证通过，开始更新配置
            log.info("[CONFIG] [BATCH_UPDATE] 第二阶段：所有验证通过，开始更新配置");
            BatchConfigUpdateResult result = BatchConfigUpdateResult.builder()
                    .totalTimeMs(0L)
                    .configResults(new HashMap<>())
                    .build();

            // 更新Skii MinIO配置
            if (request.getSkiiMinio() != null) {
                log.info("[CONFIG] [BATCH_UPDATE] 更新Skii MinIO配置");
                ConfigUpdateResult minioResult = performSkiiMinioConfigUpdate(request.getSkiiMinio());
                result.addConfigResult("skiiMinio", minioResult);
            }

            // 更新MQTT Basic配置
            if (request.getMqttBasic() != null) {
                log.info("[CONFIG] [BATCH_UPDATE] 更新MQTT Basic配置");
                ConfigUpdateResult mqttBasicResult = performMqttBasicConfigUpdate(request.getMqttBasic());
                result.addConfigResult("mqttBasic", mqttBasicResult);
            }

            // 更新其他配置
            if (request.getMqttDrc() != null) {
                log.info("[CONFIG] [BATCH_UPDATE] 更新MQTT DRC配置");
                ConfigUpdateResult mqttDrcResult = updateMqttDrcConfigBatch(request.getMqttDrc());
                result.addConfigResult("mqttDrc", mqttDrcResult);
            }

            if (request.getFhSdk() != null) {
                log.info("[CONFIG] [BATCH_UPDATE] 更新FH-SDK配置");
                ConfigUpdateResult fhSdkResult = updateFhSdkConfigBatch(request.getFhSdk());
                result.addConfigResult("fhSdk", fhSdkResult);
            }

            if (request.getEjFhServer() != null) {
                log.info("[CONFIG] [BATCH_UPDATE] 更新EJ-FH-SERVER配置");
                ConfigUpdateResult ejFhServerResult = updateEjFhServerConfigBatch(request.getEjFhServer());
                result.addConfigResult("ejFhServer", ejFhServerResult);
            }

            if (request.getAnnotationFolder() != null) {
                log.info("[CONFIG] [BATCH_UPDATE] 更新标注文件夹ID配置");
                ConfigUpdateResult annotationFolderResult = updateAnnotationFolderConfigBatch(request.getAnnotationFolder());
                result.addConfigResult("annotationFolder", annotationFolderResult);
            }
            result.calculateStatistics();

            long totalTime = System.currentTimeMillis() - startTime;
            result.setTotalTimeMs(totalTime);

            if (result.isSuccess()) {
                log.info("[CONFIG] [BATCH_UPDATE] 批量配置更新成功，成功: {}/{}, 耗时: {}ms",
                        result.getSuccessCount(), result.getTotalCount(), totalTime);
            } else {
                log.warn("[CONFIG] [BATCH_UPDATE] 批量配置更新部分失败，成功: {}/{}, 耗时: {}ms, 失败原因: {}",
                        result.getSuccessCount(), result.getTotalCount(), totalTime, result.getOverallFailureReason());
            }

            return result;

        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("[CONFIG] [BATCH_UPDATE] 批量配置更新异常: {}", e.getMessage(), e);
            return BatchConfigUpdateResult.failure("批量配置更新异常: " + e.getMessage(), totalTime);
        }
    }



    /**
     * 获取完整的Skii MinIO配置
     */
    private ApplicationConfigRequest.SkiiMinioConfig getSkiiMinioConfig() {
        ApplicationConfigRequest.SkiiMinioConfig config = new ApplicationConfigRequest.SkiiMinioConfig();
        config.setEndpoint(getSkiiMinioEndpoint());
        config.setAccessKey(getSkiiMinioAccessKey());
        config.setSecretKey(getSkiiMinioSecretKey());
        config.setBucket(getSkiiMinioBucket());
        return config;
    }

    /**
     * 获取完整的MQTT Basic配置
     */
    private ApplicationConfigRequest.MqttBasicConfig getMqttBasicConfig() {
        ApplicationConfigRequest.MqttBasicConfig config = new ApplicationConfigRequest.MqttBasicConfig();
        config.setProtocol(getMqttBasicProtocol());
        config.setHost(getMqttBasicHost());
        config.setPort(getMqttBasicPort());
        config.setUsername(getMqttBasicUsername());
        config.setPassword(getMqttBasicPassword());
        config.setClientId(getMqttBasicClientId());
        config.setPath(getMqttBasicPath());
        return config;
    }

	/**
     * 更新Skii MinIO配置（批量更新专用）
     */
    private void updateSkiiMinioConfig(ApplicationConfigRequest.SkiiMinioConfig config) {
        updateSkiiMinioEndpoint(config.getEndpoint());
        updateSkiiMinioAccessKey(config.getAccessKey());
        updateSkiiMinioSecretKey(config.getSecretKey());
        updateSkiiMinioBucket(config.getBucket());
    }

    /**
     * 更新MQTT Basic配置（批量更新专用）
     */
    private void updateMqttBasicConfig(ApplicationConfigRequest.MqttBasicConfig config) {
        updateMqttBasicProtocol(config.getProtocol());
        updateMqttBasicHost(config.getHost());
        updateMqttBasicPort(config.getPort());
        updateMqttBasicUsername(config.getUsername());
        updateMqttBasicPassword(config.getPassword());
        updateMqttBasicClientId(config.getClientId());
        updateMqttBasicPath(config.getPath());
    }

    /**
     * 执行Skii MinIO配置更新（不包含验证）
     */
    private ConfigUpdateResult performSkiiMinioConfigUpdate(ApplicationConfigRequest.SkiiMinioConfig skiiMinioConfig) {
        return performConfigUpdate("Skii MinIO", () -> {
            // 更新配置
            updateSkiiMinioConfig(skiiMinioConfig);

            // 构建更新详情
            Map<String, String> updateDetails = new HashMap<>();
            updateDetails.put("endpoint", skiiMinioConfig.getEndpoint());
            updateDetails.put("accessKey", skiiMinioConfig.getAccessKey());
            updateDetails.put("secretKey", maskSensitive(skiiMinioConfig.getSecretKey()));
            updateDetails.put("bucket", skiiMinioConfig.getBucket());
            return updateDetails;
        });
    }

    /**
     * 执行MQTT Basic配置更新（不包含验证）
     */
    private ConfigUpdateResult performMqttBasicConfigUpdate(ApplicationConfigRequest.MqttBasicConfig mqttBasicConfig) {
        return performConfigUpdate("MQTT Basic", () -> {
            // 更新配置
            updateMqttBasicConfig(mqttBasicConfig);

            // 构建更新详情
            Map<String, String> updateDetails = new HashMap<>();
            updateDetails.put("protocol", mqttBasicConfig.getProtocol());
            updateDetails.put("host", mqttBasicConfig.getHost());
            updateDetails.put("port", mqttBasicConfig.getPort());
            updateDetails.put("username", mqttBasicConfig.getUsername());
            updateDetails.put("password", maskSensitive(mqttBasicConfig.getPassword()));
            updateDetails.put("clientId", mqttBasicConfig.getClientId());
            updateDetails.put("path", mqttBasicConfig.getPath());
            return updateDetails;
        });
    }

    /**
     * 批量更新MQTT DRC配置
     */
    private ConfigUpdateResult updateMqttDrcConfigBatch(ApplicationConfigRequest.MqttDrcConfig config) {
        return performConfigUpdate("MQTT DRC", () -> {
            Map<String, String> updateDetails = new HashMap<>();

            boolean protocolSuccess = updateMqttDrcProtocol(config.getProtocol());
            boolean hostSuccess = updateMqttDrcHost(config.getHost());
            boolean portSuccess = updateMqttDrcPort(config.getPort());
            boolean pathSuccess = updateMqttDrcPath(config.getPath());
            boolean usernameSuccess = updateMqttDrcUsername(config.getUsername());
            boolean passwordSuccess = updateMqttDrcPassword(config.getPassword());

            if (!(protocolSuccess && hostSuccess && portSuccess && pathSuccess && usernameSuccess && passwordSuccess)) {
                throw new RuntimeException("MQTT DRC配置部分更新失败");
            }

            updateDetails.put("protocol", config.getProtocol());
            updateDetails.put("host", config.getHost());
            updateDetails.put("port", config.getPort());
            updateDetails.put("path", config.getPath());
            updateDetails.put("username", config.getUsername());
            updateDetails.put("password", maskSensitive(config.getPassword()));

            return updateDetails;
        });
    }

    /**
     * 批量更新FH-SDK配置
     */
    private ConfigUpdateResult updateFhSdkConfigBatch(ApplicationConfigRequest.FhSdkConfig config) {
        return performConfigUpdate("FH-SDK", () -> {
            boolean success = updateFhSdkMqttInboundTopic(config.getInboundTopic());
            if (!success) {
                throw new RuntimeException("FH-SDK配置更新失败");
            }

            Map<String, String> updateDetails = new HashMap<>();
            updateDetails.put("inboundTopic", config.getInboundTopic());
            return updateDetails;
        });
    }

    /**
     * 批量更新EJ-FH-SERVER配置
     */
    private ConfigUpdateResult updateEjFhServerConfigBatch(ApplicationConfigRequest.EjFhServerConfig config) {
        return performConfigUpdate("EJ-FH-SERVER", () -> {
            boolean success = updateEjFhServerUrl(config.getUrl());
            if (!success) {
                throw new RuntimeException("EJ-FH-SERVER配置更新失败");
            }

            Map<String, String> updateDetails = new HashMap<>();
            updateDetails.put("url", config.getUrl());
            return updateDetails;
        });
    }

    /**
     * 批量更新标注文件夹ID配置
     */
    private ConfigUpdateResult updateAnnotationFolderConfigBatch(ApplicationConfigRequest.AnnotationFolderConfig config) {
        return performConfigUpdate("标注文件夹ID", () -> {
            boolean success = updateAnnotationFolderId(config.getFolderId());
            if (!success) {
                throw new RuntimeException("标注文件夹ID配置更新失败");
            }

            Map<String, String> updateDetails = new HashMap<>();
            updateDetails.put("folderId", config.getFolderId());
            return updateDetails;
        });
    }
}
