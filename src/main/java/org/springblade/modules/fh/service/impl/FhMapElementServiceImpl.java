package org.springblade.modules.fh.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.FhOpenapiPathConstant;
import org.springblade.core.tool.api.R;
import org.springblade.modules.fh.pojo.dto.FhMapElementCreateV09DTO;
import org.springblade.modules.fh.pojo.dto.FhMapElementDeleteDTO;
import org.springblade.modules.fh.pojo.dto.FhMapElementFolderCreateDTO;
import org.springblade.modules.fh.pojo.dto.FhMapElementFolderQueryDTO;
import org.springblade.modules.fh.pojo.vo.FhMapElementDeleteVO;
import org.springblade.modules.fh.pojo.vo.FhMapElementFolderCreateVO;
import org.springblade.modules.fh.pojo.vo.FhMapElementFolderVO;
import org.springblade.modules.fh.pojo.vo.FhMapElementV09VO;
import org.springblade.modules.fh.pojo.vo.FhMapElementVO;
import org.springblade.modules.fh.service.IFhMapElementService;
import org.springblade.modules.fh.utils.FhOpenApiHttpUtil;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * 地图标注服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FhMapElementServiceImpl implements IFhMapElementService {

	@Override
	public R<FhMapElementV09VO> createMapElementV09(FhMapElementCreateV09DTO createDTO) {
		try {
			// 构建请求参数
			Map<String, Object> params = new HashMap<>();
			params.put("name", createDTO.getName());
			params.put("desc", createDTO.getDesc());
			params.put("element_source", createDTO.getElementSource());
			params.put("addIndex", createDTO.getAddIndex());
			params.put("c2C", createDTO.getC2C());
			params.put("source", createDTO.getSource());
			params.put("resource", createDTO.getResource());

			// 构建API路径，替换路径参数
			String apiPath = FhOpenapiPathConstant.MAP_ELEMENT_V09
				.replace("{proj_uuid}", createDTO.getProjectUuid())
				.replace("{group_id}", createDTO.getGroupId());

			// 调用大疆API  /openapi/v0.9/map/api/v1/workspaces/{proj_uuid}/element-groups/{group_id}/elements
			String response = FhOpenApiHttpUtil.post(apiPath, JSONUtil.toJsonStr(params));
			log.info("创建地图标注响应: {}", response);

			// 解析响应
			JSONObject responseJson = JSONUtil.parseObj(response);
			if (responseJson.getInt("code") == 0) {
				JSONObject data = responseJson.getJSONObject("data");
				FhMapElementV09VO mapElementVO = JSONUtil.toBean(data, FhMapElementV09VO.class);
				log.info("地图标注创建成功(v0.9)，ID: {}", mapElementVO.getId());
				return R.data(mapElementVO);
			} else {
				String errorMessage = responseJson.getStr("message", "创建地图标注失败");
				log.error("创建地图标注失败(v0.9)，API返回: {}", errorMessage);
				return R.fail(errorMessage);
			}
		} catch (Exception e) {
			log.error("创建地图标注失败(v0.9)，名称: {}, 项目UUID: {}, 异常信息: {}",
				createDTO.getName(), createDTO.getProjectUuid(), e.getMessage(), e);
			return R.fail("创建地图标注失败: " + e.getMessage());
		}
	}

    @Override
    public R<FhMapElementDeleteVO> deleteMapElement(FhMapElementDeleteDTO deleteDTO) {
        try {
            // 构建API路径，替换路径参数
            String apiPath = FhOpenapiPathConstant.MAP_ELEMENT_DELETE
                .replace("{proj_uuid}", deleteDTO.getProjectUuid())
                .replace("{id}", deleteDTO.getElementId());

            // 调用大疆API删除标注
            String response = FhOpenApiHttpUtil.delete(apiPath, "");

            // 解析响应
            JSONObject responseJson = JSONUtil.parseObj(response);
            if (responseJson.getInt("code") == 0) {
                JSONObject data = responseJson.getJSONObject("data");
                FhMapElementDeleteVO deleteVO = new FhMapElementDeleteVO();
                deleteVO.setId(data.getStr("id"));
                log.info("地图标注删除成功，ID: {}", deleteVO.getId());
                return R.data(deleteVO);
            } else {
                String errorMessage = responseJson.getStr("message", "删除地图标注失败");
                log.error("删除地图标注失败，API返回: {}", errorMessage);
                return R.fail(errorMessage);
            }
        } catch (Exception e) {
            log.error("删除地图标注失败，项目UUID: {}, 标注ID: {}, 异常信息: {}",
                deleteDTO.getProjectUuid(), deleteDTO.getElementId(), e.getMessage(), e);
            return R.fail("删除地图标注失败: " + e.getMessage());
        }
    }

    @Override
    public List<FhMapElementFolderVO> getMapElementFolders(FhMapElementFolderQueryDTO queryDTO) {
        try {
            // 构建查询参数
            Map<String, Object> queryParams = new HashMap<>(8);
            if (queryDTO.getGroupId() != null) {
                queryParams.put("group_id", queryDTO.getGroupId());
            }
            if (queryDTO.getIsDistributed() != null) {
                queryParams.put("is_distributed", queryDTO.getIsDistributed());
            }
            if (queryDTO.getType() != null) {
                queryParams.put("type", queryDTO.getType());
            }

            // 构建API路径，将项目UUID作为路径参数
            String apiPath = FhOpenapiPathConstant.MAP_ELEMENT_FOLDERS.replace("{proj_uuid}", queryDTO.getProjectId());

            // 调用大疆API获取标注文件夹列表
            String response = FhOpenApiHttpUtil.get(apiPath, queryParams);

            // 使用工具类方法解析API响应
            JSONObject responseObj = FhOpenApiHttpUtil.resStrToJson(response);

            // 从API响应中提取数据列表 - data直接是数组，不是包含list的对象
            List<Map<String, Object>> folderDataList = null;
            Object dataObj = responseObj.get("data");
            if (dataObj instanceof List) {
                folderDataList = (List<Map<String, Object>>) dataObj;
            }

            List<FhMapElementFolderVO> folderList = new ArrayList<>(folderDataList != null ? folderDataList.size() : 0);

            if (folderDataList != null) {
                for (Map<String, Object> folderData : folderDataList) {
                    FhMapElementFolderVO folderVO = new FhMapElementFolderVO();

                    // 基础字段映射 - 根据API文档的实际字段进行映射
                    folderVO.setId((String) folderData.get("id"));
                    folderVO.setName((String) folderData.get("name"));

                    // API中的pid对应VO中的parentId
                    folderVO.setParentId((String) folderData.get("pid"));

                    // 设置项目ID为查询参数中的项目ID，因为API响应中没有project_id字段
                    folderVO.setProjectId(queryDTO.getProjectId());

                    // 类型字段处理 - API返回Integer，VO期望String
                    Object typeObj = folderData.get("type");
                    if (typeObj != null) {
                        folderVO.setType(String.valueOf(typeObj));
                    }

                    // 处理数值和布尔字段
                    folderVO.setOrder(extractIntegerValue(folderData, "order"));
                    folderVO.setIsDistributed(extractBooleanValue(folderData, "is_distributed"));
                    folderVO.setIsLock(extractBooleanValue(folderData, "is_lock"));

                    // 解析时间字段 - 处理时间戳格式
                    Object createTimeObj = folderData.get("create_time");
                    if (createTimeObj != null) {
                        if (createTimeObj instanceof Number) {
                            // 时间戳格式（毫秒）
                            long timestamp = ((Number) createTimeObj).longValue();
                            folderVO.setCreateTime(LocalDateTime.ofInstant(
                                Instant.ofEpochMilli(timestamp),
                                ZoneId.systemDefault()));
                        } else if (createTimeObj instanceof String) {
                            // 字符串格式
                            try {
                                folderVO.setCreateTime(LocalDateTime.parse((String) createTimeObj));
                            } catch (Exception e) {
                                log.warn("解析创建时间失败: {}", createTimeObj);
                            }
                        }
                    }

                    // 解析更新时间
                    Object updateTimeObj = folderData.get("update_time");
                    if (updateTimeObj != null) {
                        if (updateTimeObj instanceof Number) {
                             long timestamp = ((Number) updateTimeObj).longValue();
                             folderVO.setUpdateTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault()));
                        } else if (updateTimeObj instanceof String) {
                            try {
                                folderVO.setUpdateTime(LocalDateTime.parse((String) updateTimeObj));
                            } catch (Exception e) {
                                log.warn("解析更新时间失败: {}", updateTimeObj);
                            }
                        }
                    }

                    // 处理文件夹内的标注元素
                    List<Map<String, Object>> elementsList = (List<Map<String, Object>>) folderData.get("elements");
                    List<FhMapElementVO> elements = new ArrayList<>();
                    int elementCount = 0;

                    if (elementsList != null) {
                        elementCount = elementsList.size();
                        for (Map<String, Object> elementData : elementsList) {
                            FhMapElementVO elementVO = new FhMapElementVO();

                            // 基础字段映射
                            elementVO.setId((String) elementData.get("id"));
                            elementVO.setName((String) elementData.get("name"));
                            elementVO.setOrder(extractIntegerValue(elementData, "order"));
                            elementVO.setStatus(extractIntegerValue(elementData, "status"));
                            elementVO.setDisplay(extractIntegerValue(elementData, "display"));
                            elementVO.setElevationLoadStatus(extractIntegerValue(elementData, "elevation_load_status"));

                            // 处理resource字段
                            Object resourceObj = elementData.get("resource");
                            if (resourceObj instanceof Map) {
                                elementVO.setResource((Map<String, Object>) resourceObj);
                            }

                            elements.add(elementVO);
                        }
                    }

                    folderVO.setElementCount(elementCount);
                    folderVO.setElements(elements);

                    // 将文件夹信息保存到列表中
                    folderList.add(folderVO);
                }
            }

            log.info("获取标注文件夹列表成功，项目UUID: {}, 标注文件ID: {}, 返回数量: {}",
                queryDTO.getProjectId(),
                queryDTO.getGroupId() != null ? queryDTO.getGroupId() : "全部",
                folderList.size());
            return folderList;
        } catch (Exception e) {
            log.error("获取标注文件夹列表失败，项目UUID: {}, 异常信息: {}", queryDTO.getProjectId(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public R<FhMapElementFolderCreateVO> createMapElementFolder(FhMapElementFolderCreateDTO createDTO) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("name", createDTO.getName());
            if (createDTO.getPid() != null) {
                params.put("pid", createDTO.getPid());
            }

            // 构建API路径，替换路径参数
            String apiPath = FhOpenapiPathConstant.MAP_ELEMENT_FOLDER_CREATE
                .replace("{proj_uuid}", createDTO.getProjectUuid());

            // 调用大疆API创建标注文件夹
            String response = FhOpenApiHttpUtil.post(apiPath, JSONUtil.toJsonStr(params));
            log.info("创建标注文件夹响应: {}", response);

            // 使用工具类方法解析响应
            JSONObject responseObj = FhOpenApiHttpUtil.resStrToJson(response);
            JSONObject data = responseObj.getJSONObject("data");
            
            FhMapElementFolderCreateVO folderCreateVO = new FhMapElementFolderCreateVO();
            folderCreateVO.setId(data.getStr("id"));
            
            log.info("标注文件夹创建成功，ID: {}, 名称: {}", folderCreateVO.getId(), createDTO.getName());
            return R.data(folderCreateVO);
            
        } catch (Exception e) {
            log.error("创建标注文件夹失败，项目UUID: {}, 文件夹名称: {}, 异常信息: {}", 
                createDTO.getProjectUuid(), createDTO.getName(), e.getMessage(), e);
            return R.fail("创建标注文件夹失败: " + e.getMessage());
        }
    }

    /**
     * 从Map中安全提取Integer值
     *
     * @param dataMap 数据Map
     * @param key     键名
     * @return Integer值，如果不存在或类型不匹配则返回null
     */
    private Integer extractIntegerValue(Map<String, Object> dataMap, String key) {
        Object value = dataMap.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return null;
    }

    /**
     * 从Map中安全提取Boolean值
     *
     * @param dataMap 数据Map
     * @param key     键名
     * @return Boolean值，如果不存在或类型不匹配则返回null
     */
    private Boolean extractBooleanValue(Map<String, Object> dataMap, String key) {
        Object value = dataMap.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return null;
    }
}
