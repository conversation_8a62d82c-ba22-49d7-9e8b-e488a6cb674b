package org.springblade.modules.fh.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springblade.common.constant.FhOpenapiPathConstant;
import org.springblade.common.util.JsonUtils;
import org.springblade.core.tool.api.R;
import org.springblade.modules.fh.enums.FlightTaskStatus;
import org.springblade.modules.fh.mapper.FhFlightTaskMapper;
import org.springblade.modules.fh.pojo.dto.*;
import org.springblade.modules.fh.pojo.entity.FhFlightTask;
import org.springblade.modules.fh.pojo.vo.*;
import org.springblade.modules.fh.service.IFhFlightTaskService;
import org.springblade.modules.fh.service.IFhOrgService;
import org.springblade.modules.fh.utils.FhOpenApiHttpUtil;
import org.springblade.modules.fh.utils.FlightTaskNameUtil;
import org.springblade.modules.fh.utils.FlightTaskOpenApiUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static java.time.Instant.ofEpochSecond;
import static java.time.ZoneId.systemDefault;

/**
 * 飞行任务管理服务实现类
 *
 * <AUTHOR> AI
 */
@Slf4j
@Service
public class FhFlightTaskServiceImpl implements IFhFlightTaskService {

    @Resource
    private IFhOrgService fhOrgService;

    @Resource
    private FhFlightTaskMapper fhFlightTaskMapper;

    @Resource
    private FlightTaskOpenApiUtil flightTaskOpenApiUtil;

    // 移除FhOpenApiHttpUtil的实例注入，因为它是工具类，应该使用静态方法

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<FhFlightTaskVO> getFlightTaskList(FhFlightTaskRequestByTimeBaseDTO requestDTO) {
        // 1. 获取所有drone设备的SN号
        List<String> droneSNList = getDroneSNList();
        if (CollUtil.isEmpty(droneSNList)) {
            log.warn("未获取到任何drone设备SN号");
            return Collections.emptyList();
        }

        // 2. 循环调用大疆API获取每个设备的飞行任务列表
        List<FhFlightTaskVO> resultList = new ArrayList<>();
        for (String sn : droneSNList) {
                try {
                    // 设置当前设备SN
                    requestDTO.setDeviceSn(sn);
                    // 调用优化后的API获取飞行任务列表（支持时间分段查询）
                    OffsetDateTime minBeginAt = OffsetDateTime.ofInstant(
                        ofEpochSecond(requestDTO.getBeginAt()), systemDefault());
                    OffsetDateTime maxBeginAt = OffsetDateTime.ofInstant(
                        ofEpochSecond(requestDTO.getEndAt()), systemDefault());
                    List<FhFlightTaskVO> deviceTaskList = flightTaskOpenApiUtil.getFlightTaskListWithTimeRangeOptimization(
                        sn, minBeginAt, maxBeginAt);
                    if (CollUtil.isNotEmpty(deviceTaskList)) {
                        resultList.addAll(deviceTaskList);
                    }
                } catch (Exception e) {
                    log.error("获取设备[{}]的飞行任务列表失败", sn, e);
                }
            }

        // 3. 将结果保存到数据库
        if (CollUtil.isNotEmpty(resultList)) {
            saveFlightTaskList(resultList);
        }

        return resultList;
    }

    /**
     * 保存飞行任务列表到数据库
     *
     * @param taskList 飞行任务列表
     */
    private void saveFlightTaskList(List<FhFlightTaskVO> taskList) {
        if (CollUtil.isEmpty(taskList)) {
            return;
        }

        log.info("开始保存{}条飞行任务数据到数据库", taskList.size());
        DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;

        // 使用Stream API处理任务列表
        List<FhFlightTask> entityList = taskList.stream()
            .map(vo -> {
                try {
                    // 创建实体对象
                    FhFlightTask entity = new FhFlightTask();

                    // 使用反射或BeanUtils复制基本属性
                    BeanUtils.copyProperties(vo, entity, "beginAt", "endAt", "runAt", "completedAt");
					entity.setTaskStatus(vo.getTaskStatus());
					entity.setLandingDockSn(vo.getSn());
					// 直接使用原始任务名称，不再添加时间后缀
					entity.setName(vo.getName());

                    // 使用新的方法处理Date到OffsetDateTime的转换
                    convertAndSetDateTime(vo.getBeginAt(), entity::setBeginAt);
                    convertAndSetDateTime(vo.getEndAt(), entity::setEndAt);
                    convertAndSetDateTime(vo.getRunAt(), entity::setRunAt);
                    convertAndSetDateTime(vo.getCompletedAt(), entity::setCompletedAt);

                    // 设置操作记录
            if (vo.getOperations() != null) {
                entity.setOperatorAccount(vo.getOperations().getOperatorAccount());
            }

            // 设置异常信息
            if (vo.getExceptions() != null) {
                try {
                    // 安全地将Long转换为Integer，避免数值溢出异常
                    entity.setExceptionCode(vo.getExceptions().getCode() <= Integer.MAX_VALUE ?
                        (int)vo.getExceptions().getCode() : 0);
                    entity.setExceptionMessage(vo.getExceptions().getMessage());
                    parseAndSetDateTime(vo.getExceptions().getHappenAt(), formatter, entity::setExceptionHappenAt);
                    entity.setExceptionSn(vo.getExceptions().getSn());
                } catch (Exception ex) {
                    log.warn("设置异常信息失败: {}", ex.getMessage());
                    // 设置默认值，避免整个转换失败
                    entity.setExceptionCode(0);
                }
            }

                    return entity;
                } catch (Exception e) {
                    log.error("转换飞行任务数据失败: {}", vo.getUuid(), e);
                    return null;
                }
            })
            .filter(entity -> entity != null)
            .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(entityList)) {
            try {
                // 1. 提取所有任务的UUID
                List<String> uuidList = entityList.stream()
                    .map(FhFlightTask::getUuid)
                    .collect(Collectors.toList());

                // 2. 一次性查询所有已存在的任务
                List<FhFlightTask> existingTasks = fhFlightTaskMapper.selectList(
                    new LambdaQueryWrapper<FhFlightTask>()
                        .in(FhFlightTask::getUuid, uuidList)
                );

                // 3. 构建UUID到ID的映射关系
                Map<String, Long> uuidToIdMap = existingTasks.stream()
                    .collect(Collectors.toMap(FhFlightTask::getUuid, FhFlightTask::getId));

                // 4. 分离需要更新和需要插入的记录
                List<FhFlightTask> toUpdateList = new ArrayList<>();
                List<FhFlightTask> toInsertList = new ArrayList<>();

                for (FhFlightTask entity : entityList) {
                    if (uuidToIdMap.containsKey(entity.getUuid())) {
                        // 设置ID并加入更新列表
                        entity.setId(uuidToIdMap.get(entity.getUuid()));
                        toUpdateList.add(entity);
                    } else {
                        // 加入插入列表
                        toInsertList.add(entity);
                    }
                }

                // 5. 批量更新已存在的记录
                if (CollUtil.isNotEmpty(toUpdateList)) {
                    for (FhFlightTask entity : toUpdateList) {
                        fhFlightTaskMapper.updateById(entity);
                    }
                    log.info("成功更新{}条飞行任务数据", toUpdateList.size());
                }

                // 6. 批量插入新记录
                if (CollUtil.isNotEmpty(toInsertList)) {
                    for (FhFlightTask entity : toInsertList) {
                        fhFlightTaskMapper.insert(entity);
                    }
                    log.info("成功插入{}条飞行任务数据", toInsertList.size());
                }

                log.info("成功保存{}条飞行任务数据到数据库", entityList.size());
            } catch (Exception e) {
                log.error("批量保存飞行任务数据失败", e);
            }
        }
    }

    /**
     * 解析日期时间字符串并设置到实体对象中
     *
     * @param dateTimeStr 日期时间字符串
     * @param formatter 日期格式化器
     * @param setter 设置方法引用
     */
    private void parseAndSetDateTime(String dateTimeStr, DateTimeFormatter formatter, java.util.function.Consumer<OffsetDateTime> setter) {
        if (dateTimeStr != null && !dateTimeStr.isEmpty()) {
            setter.accept(OffsetDateTime.parse(dateTimeStr, formatter));
        }
    }

    /**
     * 将Date对象转换为OffsetDateTime并设置到实体对象中
     *
     * @param date Date对象
     * @param setter 设置方法引用
     */
    private void convertAndSetDateTime(Date date, java.util.function.Consumer<OffsetDateTime> setter) {
        if (date != null) {
            OffsetDateTime offsetDateTime = date.toInstant().atZone(systemDefault()).toOffsetDateTime();
            setter.accept(offsetDateTime);
        }
    }

    /**
     * 获取所有drone设备的SN号
     *
     * @return drone设备SN号列表
     */
    private List<String> getDroneSNList() {
        try {
            // 调用/prj/devices接口获取项目下的所有设备
            List<FhOAPrjDeviceVO> deviceList = fhOrgService.prjDevices();
            if (CollUtil.isEmpty(deviceList)) {
                return Collections.emptyList();
            }

            // 提取所有drone设备的SN号
            List<String> droneSNList = new ArrayList<>();
            for (FhOAPrjDeviceVO device : deviceList) {
				// 20250521变更：获取机场的sn进行查询，不再根据飞机的sn查询
				droneSNList.add(device.getGateway().getSn());
            }
            return droneSNList;
        } catch (Exception e) {
            log.error("获取机场设备SN号列表失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<Integer> getTaskYears() {
        try {

            List<Integer> resultList = fhFlightTaskMapper.selectTaskYears();

            if (CollUtil.isEmpty(resultList)) {
                log.info("未查询到任何飞行任务年份数据");
                return Collections.emptyList();
            }
            return resultList;
        } catch (Exception e) {
            log.error("获取飞行任务年份列表失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<FhDeviceTaskStatsVO> getDeviceTaskStats(FhDeviceTaskStatsDTO dto) {
        try {
            // 1. 获取所有drone设备的SN号和名称
            List<FhOAPrjDeviceVO> deviceList = fhOrgService.prjDevices();
            if (CollUtil.isEmpty(deviceList)) {
                log.warn("未获取到任何drone设备");
                return Collections.emptyList();
            }

            // 2. 获取所有设备在指定年份的任务数量
            Map<String, Map<Integer, Long>> deviceMonthlyTasksMap = getAllDevicesMonthlyTasks(deviceList, dto.getYear());

            // 3. 使用Stream API按设备组织数据
			List<FhDeviceTaskStatsVO> collect = deviceList.stream()
				.filter(device -> device.getGateway() != null && device.getGateway().getSn() != null)
				.map(device -> {
					String deviceSn = device.getGateway().getSn();
					String deviceName = device.getDrone().getCallsign();

					// 创建设备统计对象
					FhDeviceTaskStatsVO deviceStats = new FhDeviceTaskStatsVO();
					deviceStats.setName(deviceName);

					// 创建12个月的任务数量数组，按月份顺序从1月到12月
					Integer[] monthlyData = new Integer[12];
					Map<Integer, Long> deviceMonthlyMap = deviceMonthlyTasksMap.getOrDefault(deviceSn, Collections.emptyMap());

					// 填充每个月的任务数量，如果没有数据则为0
					for (int month = 1; month <= 12; month++) {
						Long count = deviceMonthlyMap.getOrDefault(month, 0L);
						monthlyData[month - 1] = count.intValue();
					}

					deviceStats.setData(monthlyData);
					return deviceStats;
				}).collect(Collectors.toList());
	        return collect;
        } catch (Exception e) {
            log.error("获取设备任务统计失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<FhMediaResourceVO> getFlightTaskMediaList(String taskUuid) {
        try {
            // 1. 构建请求URL
            String url = String.format(FhOpenapiPathConstant.FLIGHT_TASK_MEDIA, taskUuid);

            // 2. 调用大疆API获取媒体资源列表
            String response = FhOpenApiHttpUtil.get(url);
            if (response == null || response.isEmpty()) {
                log.error("获取飞行任务媒体资源列表失败，API返回空响应");
                return Collections.emptyList();
            }

            // 3. 解析响应数据
            JSONObject jsonResponse = JSONUtil.parseObj(response);
            if (jsonResponse == null) {
                log.error("解析API响应失败，响应数据：{}", response);
                return Collections.emptyList();
            }

            // 检查响应码
            Integer code = jsonResponse.getInt("code", -1);
            if (code != 0) {
                return Collections.emptyList();
            }

            // 4. 提取分页信息和媒体资源列表
            JSONObject data = jsonResponse.getJSONObject("data");
            if (data == null) {
                log.error("API返回数据格式不正确，缺少data字段");
                return Collections.emptyList();
            }

            JSONArray mediaArray = data.getJSONArray("list");
            if (CollUtil.isEmpty(mediaArray)) {
                log.info("未找到媒体资源数据");
                return Collections.emptyList();
            }

            // 5. 转换媒体资源数据
            List<FhMediaResourceVO> mediaList = new ArrayList<>(mediaArray.size());
            for (int i = 0; i < mediaArray.size(); i++) {
                try {
                    JSONObject mediaJson = mediaArray.getJSONObject(i);
                    FhMediaResourceVO media = JSONUtil.toBean(mediaJson, FhMediaResourceVO.class);

                    mediaList.add(media);
                } catch (Exception e) {
                    log.error("解析媒体资源数据失败，索引：{}", i, e);
                }
            }
            return mediaList;

        } catch (Exception e) {
            log.error("获取飞行任务媒体资源列表异常", e);
            return Collections.emptyList();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> createFlightTask(FhFlightTaskCreateDTO createDTO) {
        log.info("创建飞行任务，参数：{}", createDTO);
        try {
            // 1. 检查时间冲突
            R<String> timeConflictCheck = checkTimeConflict(createDTO);
            if (!timeConflictCheck.isSuccess()) {
                return timeConflictCheck;
            }

            // 2. 使用JsonUtils工具类将DTO转换为蛇形命名（下划线分隔）的JSON字符串
            String requestJson = JsonUtils.toSnakeCaseJson(createDTO);
			long beginAt = System.currentTimeMillis() / 1000;

            // 设置时区为Asia/Shanghai，确保使用中国时区
            if (createDTO.getTimeZone() == null || createDTO.getTimeZone().isEmpty()) {
                createDTO.setTimeZone("Asia/Shanghai");
                // 重新生成JSON，确保时区信息被包含
                requestJson = JsonUtils.toSnakeCaseJson(createDTO);
            }

            log.info("创建飞行任务，请求JSON：{}", requestJson);

            // 3. 调用大疆API创建飞行任务
            String responseStr = FhOpenApiHttpUtil.post(FhOpenapiPathConstant.FLIGHT_TASK, requestJson);

            // 4. 解析响应数据
            JSONObject response = FhOpenApiHttpUtil.resData(responseStr);
            if (response == null) {
                return R.fail("创建飞行任务失败：API返回数据为空");
            }

            // 5. 处理响应结果 首先尝试获取单个任务ID
            String taskUUId = response.getStr("task_uuid");
            if (taskUUId != null && !taskUUId.isEmpty()) {
				FhFlightTaskRequestByTimeBaseDTO request = getFhFlightTaskRequestByTimeBaseDTO(createDTO, beginAt);
			OffsetDateTime minBeginAt = OffsetDateTime.ofInstant(
				ofEpochSecond(request.getBeginAt()),
				systemDefault());
			OffsetDateTime maxBeginAt = OffsetDateTime.ofInstant(
				ofEpochSecond(request.getEndAt()),
				systemDefault());
			List<FhFlightTaskVO> deviceFlightTaskList = flightTaskOpenApiUtil.getFlightTaskListWithTimeRangeOptimization(
				request.getDeviceSn(), minBeginAt, maxBeginAt);
				saveFlightTaskList(deviceFlightTaskList);
				return R.data(taskUUId, "创建飞行任务成功");
            }

            log.error("创建飞行任务失败，API返回异常：{}", response);
            return R.fail("创建飞行任务失败：API返回异常");
        } catch (Exception e) {
            log.error("创建飞行任务失败", e);
            return R.fail("创建飞行任务失败：" + e.getMessage());
        }
    }

	@NotNull
	private static FhFlightTaskRequestByTimeBaseDTO getFhFlightTaskRequestByTimeBaseDTO(FhFlightTaskCreateDTO createDTO, long beginAt) {
		FhFlightTaskRequestByTimeBaseDTO request = new FhFlightTaskRequestByTimeBaseDTO();
		request.setDeviceSn(createDTO.getSn());
		// 如果是立即起飞的任务则把开始结束时间控制一下
		if (createDTO.getTaskType().equals("immediate")) {
			request.setBeginAt(beginAt - 60);
			// 结束时间为开始时间的后3分钟
			request.setEndAt(beginAt + 3 * 60);
		} else if (createDTO.getTaskType().equals("timed")) {
			// 单次定时任务的结束时间和开始事件一致
			request.setBeginAt(createDTO.getBeginAt());
			request.setEndAt(createDTO.getBeginAt());
		} else {
			request.setBeginAt(createDTO.getBeginAt());
			request.setEndAt(createDTO.getEndAt());
		}

		request.setName(createDTO.getName());
		return request;
	}

	@Override
    public List<FhWaylineVO> getWaylineList() {
        try {
            // 调用大疆API获取航线列表
            String response = FhOpenApiHttpUtil.get(FhOpenapiPathConstant.WAYLINES);
            log.debug("获取航线列表原始响应：{}", response);

            // 解析响应数据
            if (response == null || response.isEmpty()) {
                log.warn("获取航线列表返回数据为空");
                return Collections.emptyList();
            }

            // 解析响应JSON
            JSONObject jsonResponse = JSONUtil.parseObj(response);

            // 检查响应码
            Integer code = jsonResponse.getInt("code", -1);
            if (code != 0) {
                log.warn("获取航线列表API返回错误码：{}, 错误信息：{}", code, jsonResponse.getStr("message"));
                return Collections.emptyList();
            }

            // 获取data.list数组
            if (!jsonResponse.containsKey("data") || !jsonResponse.getJSONObject("data").containsKey("list")) {
                log.warn("获取航线列表返回数据格式不正确，缺少data.list字段");
                return Collections.emptyList();
            }

            JSONArray waylineArray = jsonResponse.getJSONObject("data").getJSONArray("list");
            if (CollUtil.isEmpty(waylineArray)) {
                log.info("航线列表为空");
                return Collections.emptyList();
            }

            // 使用JSONUtil直接将JSONArray转换为对象列表
            List<FhWaylineVO> waylineList = JSONUtil.toList(waylineArray, FhWaylineVO.class);

            // 处理特殊字段和嵌套对象
            waylineList.forEach(wayline -> {
                // 设置ID字段（确保waylineId与id一致）
                wayline.setWaylineId(wayline.getId());
                wayline.setFileSize(wayline.getSize());

                // 处理更新时间
                if (wayline.getUpdateTimeStamp() != null) {
                    wayline.setUpdateTime(LocalDateTime.ofInstant(
                        java.time.Instant.ofEpochMilli(wayline.getUpdateTimeStamp()),
                        systemDefault()));
                }

                // 兼容旧版本，设置第一个模板类型
                if (CollUtil.isNotEmpty(wayline.getTemplateTypes())) {
                    wayline.setTemplateType(wayline.getTemplateTypes().get(0));
                }
            });

            log.info("获取航线列表成功，共{}条记录", waylineList.size());
            return waylineList;
        } catch (Exception e) {
            log.error("获取航线列表失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public R<String> updateFlightTaskStatus(FhFlightTaskStatusUpdateDTO updateDTO) {
		log.info("更新飞行任务状态，任务UUID：{}，状态：{}", updateDTO.getTaskUuid(), updateDTO.getStatus());
		try {
			// 1. 参数校验
			if (StrUtil.isBlank(updateDTO.getTaskUuid())) {
				return R.fail("任务UUID 不能为空");
			}
			if (StrUtil.isBlank(updateDTO.getStatus())) {
				return R.fail("任务状态 不能为空");
			}

			// 2. 构建请求 URL
			String url = String.format(FhOpenapiPathConstant.FLIGHT_TASK_STATUS, updateDTO.getTaskUuid());

			// 3. 构建请求参数为蛇形命名 JSON，JsonUtils 内部已实现驼峰转蛇形
			String requestJson = JsonUtils.toSnakeCaseJson(Collections.singletonMap("status", updateDTO.getStatus()));

			// 4. 调用大疆 API 更新飞行任务状态
			String responseStr = FhOpenApiHttpUtil.put(url, requestJson);

			// 5. 处理响应结果
			if (responseStr != null && responseStr.contains("Error request, response status: 500")) {
				log.error("更新飞行任务状态失败，服务器返回500错误，任务UUID：{}", updateDTO.getTaskUuid());
				return R.fail("更新飞行任务状态失败：服务器内部错误");
			}

			// 6. 解析响应数据（工具方法内部已做空值/格式校验）
			com.alibaba.fastjson.JSONObject response = JSON.parseObject(responseStr);
			int code = response.getIntValue("code");
			String message = response.getString("message");

			if (code != 0) {
				return R.fail(code, "更新飞行任务状态失败：" + message);
			}

			// 7. 更新数据库中的飞行任务状态
			try {
				// 根据UUID查询飞行任务
				FhFlightTask flightTask = fhFlightTaskMapper.selectByUuid(updateDTO.getTaskUuid());
				if (flightTask != null) {
					// 更新任务状态
					flightTask.setTaskStatus(updateDTO.getStatus());
					fhFlightTaskMapper.updateById(flightTask);
					log.info("成功更新数据库中飞行任务状态，任务UUID：{}，状态：{}", updateDTO.getTaskUuid(), updateDTO.getStatus());
				} else {
					log.warn("未找到对应的飞行任务记录，无法更新状态，任务UUID：{}", updateDTO.getTaskUuid());
				}
			} catch (Exception e) {
				// 数据库更新失败不影响API调用结果
				log.error("更新数据库中飞行任务状态失败，任务UUID：{}", updateDTO.getTaskUuid(), e);
			}

			return R.success("OK");

		} catch (Exception e) {
			log.error("更新飞行任务状态出现异常，任务UUID：{}", updateDTO.getTaskUuid(), e);
			return R.fail("更新飞行任务状态异常：" + e.getMessage());
		}
    }

    /**
     * 检查任务时间冲突
     * 根据阿里开发规范，优化异常处理和日志记录
     *
     * @param createDTO 创建任务的DTO
     * @return 检查结果
     */
    private R<String> checkTimeConflict(FhFlightTaskCreateDTO createDTO) {
        String taskType = createDTO.getTaskType();
        String deviceSn = createDTO.getSn();

        log.info("开始检查任务时间冲突，设备[{}]，任务类型[{}]，任务名称[{}]",
            deviceSn, taskType, createDTO.getName());

        try {
            // 参数校验
            if (StrUtil.isBlank(taskType)) {
                return R.fail("任务类型不能为空");
            }
            if (StrUtil.isBlank(deviceSn)) {
                return R.fail("设备SN不能为空");
            }

            // 根据任务类型执行相应的冲突检查
            R<String> result;
            switch (taskType) {
                case "immediate":
                    result = checkImmediateTaskConflict(createDTO);
                    break;
                case "timed":
                    result = checkTimedTaskConflict(createDTO);
                    break;
                case "recurring":
                    result = checkRecurringTaskConflict(createDTO);
                    break;
                case "continuous":
                    result = checkContinuousTaskConflict(createDTO);
                    break;
                default:
                    log.warn("未知任务类型[{}]，使用通用检查逻辑", taskType);
                    result = checkGeneralTaskConflict(createDTO);
                    break;
            }

            if (result.isSuccess()) {
                log.info("任务时间冲突检查通过，设备[{}]，任务类型[{}]", deviceSn, taskType);
            } else {
                log.warn("任务时间冲突检查失败，设备[{}]，任务类型[{}]，原因：{}",
                    deviceSn, taskType, result.getMsg());
            }

            return result;

        } catch (Exception e) {
            log.error("检查时间冲突时发生异常，设备[{}]，任务类型[{}]", deviceSn, taskType, e);
            return R.fail("检查时间冲突失败：" + e.getMessage());
        }
    }

    /**
     * 检查立即执行任务的时间冲突
     * 立即任务从当前时间开始执行，需要检查当前时间段是否有冲突
     */
    private R<String> checkImmediateTaskConflict(FhFlightTaskCreateDTO createDTO) {
        long currentTime = System.currentTimeMillis() / 1000;
        long beginAt = currentTime;

        // 计算任务结束时间
        Long endAt = createDTO.getEndAt();
        if (endAt == null) {
            // 默认30分钟任务时长
            endAt = currentTime + 30 * 60;
            log.info("立即任务未指定结束时间，使用默认30分钟时长，结束时间：{}", formatTimestamp(endAt));
        }

        // 验证时间范围的合理性
        if (endAt <= beginAt) {
            return R.fail("立即任务的结束时间不能早于或等于开始时间");
        }

        log.info("检查立即任务时间冲突，设备[{}]，时间范围：{} - {}",
            createDTO.getSn(), formatTimestamp(beginAt), formatTimestamp(endAt));

        return checkTimeRangeConflictWithBuffer(createDTO.getSn(), beginAt, endAt, "立即执行任务");
    }

    /**
     * 检查单次定时任务的时间冲突
     * 单次定时任务在指定时间执行一次
     */
    private R<String> checkTimedTaskConflict(FhFlightTaskCreateDTO createDTO) {
        Long beginAt = createDTO.getBeginAt();
        if (beginAt == null) {
            return R.fail("单次定时任务的开始时间不能为空");
        }

        // 验证开始时间不能是过去时间
        long currentTime = System.currentTimeMillis() / 1000;
        if (beginAt <= currentTime) {
            return R.fail("单次定时任务的开始时间不能早于当前时间");
        }

        // 计算任务结束时间
        Long endAt = createDTO.getEndAt();
        if (endAt == null) {
            // 默认30分钟任务时长
            endAt = beginAt + 30 * 60;
            log.info("单次定时任务未指定结束时间，使用默认30分钟时长，结束时间：{}", formatTimestamp(endAt));
        }

        // 验证时间范围的合理性
        if (endAt <= beginAt) {
            return R.fail("单次定时任务的结束时间不能早于或等于开始时间");
        }

        log.info("检查单次定时任务时间冲突，设备[{}]，时间范围：{} - {}",
            createDTO.getSn(), formatTimestamp(beginAt), formatTimestamp(endAt));

        return checkTimeRangeConflictWithBuffer(createDTO.getSn(), beginAt, endAt, "单次定时任务");
    }

    /**
     * 检查重复任务的时间冲突
     */
    private R<String> checkRecurringTaskConflict(FhFlightTaskCreateDTO createDTO) {
        List<Long> recurringTaskStartTimeList = createDTO.getRecurringTaskStartTimeList();

        if (CollUtil.isEmpty(recurringTaskStartTimeList)) {
            log.warn("重复任务的执行时间列表为空，使用通用检查逻辑");
            return checkGeneralTaskConflict(createDTO);
        }

        // 获取单次任务的持续时间，如果没有指定，使用默认值
        Long taskDuration = null;
        if (createDTO.getBeginAt() != null && createDTO.getEndAt() != null) {
            taskDuration = createDTO.getEndAt() - createDTO.getBeginAt();
        }
        if (taskDuration == null || taskDuration <= 0) {
			// 默认30分钟任务时长
            taskDuration = 30 * 60L;
        }

        // 验证重复任务的时间点不能是过去时间
        long currentTime = System.currentTimeMillis() / 1000;
        for (int i = 0; i < recurringTaskStartTimeList.size(); i++) {
            Long startTime = recurringTaskStartTimeList.get(i);
            if (startTime == null) {
                log.warn("重复任务第{}个执行时间为空，跳过检查", i + 1);
                continue;
            }

            if (startTime <= currentTime) {
                return R.fail(String.format("重复任务第%d个执行时间不能早于当前时间", i + 1));
            }
        }

        // 创建时间段列表用于内部重叠检查
        List<TimeRange> allTimeRangesToCheck = new ArrayList<>();

        // 检查每个重复执行时间点的冲突
        for (int i = 0; i < recurringTaskStartTimeList.size(); i++) {
            Long startTime = recurringTaskStartTimeList.get(i);
            if (startTime == null) {
                continue;
            }

            // 计算每个重复任务的结束时间
            long endTime = startTime + taskDuration;

            // 添加到时间段列表中，用于后续内部重叠检查
            allTimeRangesToCheck.add(new TimeRange(startTime, endTime, i + 1));

            log.info("检查重复任务第{}个执行时间点，设备[{}]，时间范围：{} - {}",
                i + 1, createDTO.getSn(), formatTimestamp(startTime), formatTimestamp(endTime));

            R<String> conflictResult = checkTimeRangeConflictWithBuffer(
                createDTO.getSn(), startTime, endTime,
                String.format("重复任务第%d个执行时间点", i + 1));

            if (!conflictResult.isSuccess()) {
                return conflictResult;
            }
        }

        // 检查重复任务内部时间段之间是否存在重叠
        for (int i = 0; i < allTimeRangesToCheck.size(); i++) {
            for (int j = i + 1; j < allTimeRangesToCheck.size(); j++) {
                TimeRange range1 = allTimeRangesToCheck.get(i);
                TimeRange range2 = allTimeRangesToCheck.get(j);
                if (range1.overlapsWith(range2)) {
                    return R.fail(String.format(
                        "重复任务第%d个执行时间点与第%d个执行时间点存在时间重叠，请调整时间安排。" +
                        "第%d个时间段：%s - %s，第%d个时间段：%s - %s",
                        range1.index, range2.index,
                        range1.index, formatTimestamp(range1.start), formatTimestamp(range1.end),
                        range2.index, formatTimestamp(range2.start), formatTimestamp(range2.end)));
                }
            }
        }

        log.info("重复任务时间冲突检查通过，设备[{}]共检查{}个执行时间点，无内部重叠",
            createDTO.getSn(), recurringTaskStartTimeList.size());
        return R.success("无时间冲突");
    }

    /**
     * 检查连续任务的时间冲突
     */
    private R<String> checkContinuousTaskConflict(FhFlightTaskCreateDTO createDTO) {
        List<List<Long>> continuousTaskPeriods = createDTO.getContinuousTaskPeriods();
        Long beginAt = createDTO.getBeginAt();
        Long endAt = createDTO.getEndAt();

        if (CollUtil.isEmpty(continuousTaskPeriods)) {
            log.warn("连续任务的执行时间段列表为空，使用通用检查逻辑");
            return checkGeneralTaskConflict(createDTO);
        }

        if (beginAt == null) {
            return R.fail("连续任务的开始时间(begin_at)不能为空");
        }

        if (endAt == null) {
            return R.fail("连续任务的结束时间(end_at)不能为空");
        }

        // 1. 生成所有需要检查的时间段
        List<TimeRange> allTimeRangesToCheck = new ArrayList<>();

        // 获取时区
        ZoneId zoneId = ZoneId.of("Asia/Shanghai");
        OffsetDateTime beginAtDateTime = OffsetDateTime.ofInstant(ofEpochSecond(beginAt), zoneId);
        LocalDate beginAtDate = beginAtDateTime.toLocalDate();

        // 首先验证原始时间段的基本有效性
        for (int i = 0; i < continuousTaskPeriods.size(); i++) {
            List<Long> period = continuousTaskPeriods.get(i);
            if (CollUtil.isEmpty(period) || period.size() < 2) {
                return R.fail(String.format("连续任务第%d个时间段格式不正确，必须包含开始和结束时间", i + 1));
            }

            Long periodStart = period.get(0);
            Long periodEnd = period.get(1);

            if (periodStart == null || periodEnd == null) {
                return R.fail(String.format("连续任务第%d个时间段的开始或结束时间不能为空", i + 1));
            }

            if (periodStart >= periodEnd) {
                return R.fail(String.format("连续任务第%d个时间段的开始时间不能大于等于结束时间", i + 1));
            }

            // 检查时间段的开始时间是否与beginAt在同一天（根据OpenAPI文档要求）
            OffsetDateTime periodStartDateTime = OffsetDateTime.ofInstant(ofEpochSecond(periodStart), zoneId);
            LocalDate periodStartDate = periodStartDateTime.toLocalDate();

            if (!periodStartDate.equals(beginAtDate)) {
                return R.fail(String.format("连续任务第%d个时间段的开始时间必须与begin_at(%s)在同一天，当前为%s",
                    i + 1, beginAtDate, periodStartDate));
            }
        }

        // 2. 根据OpenAPI规范，连续任务的时间段必须与begin_at在同一天
        // 因此无论是否为重复任务，都只检查原始时间段
        for (int i = 0; i < continuousTaskPeriods.size(); i++) {
            List<Long> period = continuousTaskPeriods.get(i);
            allTimeRangesToCheck.add(new TimeRange(period.get(0), period.get(1), i + 1));
        }

        log.info("连续任务生成了{}个时间段进行冲突检查，设备[{}]",
            allTimeRangesToCheck.size(), createDTO.getSn());

        // 3. 检查所有生成的时间段是否为未来时间
        long currentTime = System.currentTimeMillis() / 1000;
        for (TimeRange timeRange : allTimeRangesToCheck) {
            if (timeRange.start < currentTime) {
                return R.fail(String.format("连续任务第%d个时间段的开始时间不能早于当前时间", timeRange.index));
            }
        }

        // 4. 通过OpenAPI查询现有任务并检查冲突
        try {
            // 计算所有时间段的最小和最大时间，用于查询范围
            Long minStartTime = allTimeRangesToCheck.stream()
                .mapToLong(range -> range.start)
                .min().orElse(beginAt);
            Long maxEndTime = allTimeRangesToCheck.stream()
                .mapToLong(range -> range.end)
                .max().orElse(endAt);

            // 扩展查询范围，前后各扩展一天
            long expandedBeginAt = minStartTime - 24 * 60 * 60;
            long expandedEndAt = maxEndTime + 24 * 60 * 60;

            log.info("查询连续任务冲突，设备[{}]，查询时间范围：{} 到 {}，共检查{}个时间段",
                createDTO.getSn(), formatTimestamp(expandedBeginAt), formatTimestamp(expandedEndAt),
                allTimeRangesToCheck.size());

            // 调用OpenAPI获取指定时间范围内的飞行任务
            List<FhFlightTaskVO> existingTasks = FlightTaskOpenApiUtil.getFlightTaskListFromOpenApiSegmented(
                    createDTO.getSn(), expandedBeginAt, expandedEndAt);

            if (CollUtil.isNotEmpty(existingTasks)) {
                log.info("设备[{}]在时间范围内找到{}个现有任务", createDTO.getSn(), existingTasks.size());

                // 过滤掉已终止或启动失败的任务，只检查活跃任务
                List<FhFlightTaskVO> activeTasks = existingTasks.stream()
                        .filter(task -> {
                            String status = task.getStatus();
                            // 只检查可能冲突的任务状态：待执行、执行中等
                            return !"terminated".equals(status) && !"failed".equals(status) &&
                                   !"cancelled".equals(status) && !"completed".equals(status);
                        })
                        .collect(Collectors.toList());

                if (CollUtil.isEmpty(activeTasks)) {
                    log.info("设备[{}]在时间范围内无活跃任务，无冲突", createDTO.getSn());
                } else {
                    log.info("设备[{}]在时间范围内找到{}个活跃任务，开始检查时间重叠", createDTO.getSn(), activeTasks.size());

                    // 检查每个新任务时间段与现有任务的冲突
                    for (TimeRange newTimeRange : allTimeRangesToCheck) {
                        log.info("检查新任务时间段[{}]: {} - {}", newTimeRange.index,
                            formatTimestamp(newTimeRange.start), formatTimestamp(newTimeRange.end));

                        for (FhFlightTaskVO existingTask : activeTasks) {
                            String timeRangeDesc = String.format("连续任务第%d个时间段", newTimeRange.index);

                            R<String> overlapResult = isTimeRangeOverlapWithBuffer(
                                newTimeRange.start, newTimeRange.end, existingTask, timeRangeDesc);

                            if (!overlapResult.isSuccess()) {
                                return overlapResult;
                            }
                        }
                    }
                }
            } else {
                log.info("设备[{}]在时间范围内无现有任务，无冲突", createDTO.getSn());
            }

            // 5. 检查新任务内部时间段之间是否存在重叠
            for (int i = 0; i < allTimeRangesToCheck.size(); i++) {
                for (int j = i + 1; j < allTimeRangesToCheck.size(); j++) {
                    TimeRange range1 = allTimeRangesToCheck.get(i);
                    TimeRange range2 = allTimeRangesToCheck.get(j);
                    if (range1.overlapsWith(range2)) {
                        return R.fail(String.format(
                            "连续任务第%d个时间段与第%d个时间段存在时间重叠，请调整时间安排",
                            range1.index, range2.index));
                    }
                }
            }

        } catch (Exception e) {
            log.error("通过OpenAPI检查连续任务时间冲突时发生异常，设备[{}]", createDTO.getSn(), e);
            return R.fail(String.format("检查连续任务时间冲突失败：%s", e.getMessage()));
        }

        log.info("连续任务时间冲突检查通过，设备[{}]共检查{}个时间段",
            createDTO.getSn(), allTimeRangesToCheck.size());
        return R.success("无时间冲突");
    }

    /**
     * 时间范围内部类，用于连续任务时间段重叠检查
     */
    private static class TimeRange {
        final Long start;
        final Long end;
        final int index;

        TimeRange(Long start, Long end, int index) {
            this.start = start;
            this.end = end;
            this.index = index;
        }

        /**
         * 检查两个时间段是否重叠
         */
        boolean overlapsWith(TimeRange other) {
            // 两个时间段重叠当且仅当 start1 < end2 && start2 < end1
            return this.start < other.end && other.start < this.end;
        }
    }

    /**
     * 检查通用任务的时间冲突
     */
    private R<String> checkGeneralTaskConflict(FhFlightTaskCreateDTO createDTO) {
        Long beginAt = createDTO.getBeginAt();
        Long endAt = createDTO.getEndAt();

        if (beginAt == null) {
            return R.fail("任务开始时间不能为空");
        }

        if (endAt == null) {
            // 如果没有结束时间，假设任务持续时间为默认值（如30分钟）
            endAt = beginAt + 30 * 60;
        }

        // 确保结束时间不早于开始时间
        if (endAt < beginAt) {
            return R.fail("任务的结束时间不能早于开始时间");
        }

        // 验证开始时间不能是过去时间
        long currentTime = System.currentTimeMillis() / 1000;
        if (beginAt <= currentTime) {
            return R.fail("通用任务的开始时间不能早于当前时间");
        }

        log.info("检查通用任务时间冲突，设备[{}]，时间范围：{} - {}",
            createDTO.getSn(), formatTimestamp(beginAt), formatTimestamp(endAt));

        return checkTimeRangeConflictWithBuffer(createDTO.getSn(), beginAt, endAt, "通用任务");
    }

    /**
     * 带缓冲时间的时间冲突检查方法
     * 实现1分钟间隔容错机制，确保任务间至少有1分钟间隔
     *
     * @param deviceSn 设备SN
     * @param beginAt 开始时间（秒级时间戳）
     * @param endAt 结束时间（秒级时间戳）
     * @param taskTypeDesc 任务类型描述，用于日志
     * @return 检查结果
     */
    private R<String> checkTimeRangeConflictWithBuffer(String deviceSn, Long beginAt, Long endAt, String taskTypeDesc) {
        // 添加1分钟缓冲时间
        final long BUFFER_SECONDS = 60;
        long bufferedBeginAt = beginAt - BUFFER_SECONDS;
        long bufferedEndAt = endAt + BUFFER_SECONDS;

        log.info("{}时间冲突检查，设备[{}]，原始时间范围：{} - {}，缓冲后范围：{} - {}",
            taskTypeDesc, deviceSn,
            formatTimestamp(beginAt), formatTimestamp(endAt),
            formatTimestamp(bufferedBeginAt), formatTimestamp(bufferedEndAt));

        return checkTimeRangeConflictEnhanced(deviceSn, bufferedBeginAt, bufferedEndAt, beginAt, endAt, taskTypeDesc);
    }

    /**
     * 增强版时间冲突检查方法，使用更精确的时间重叠判断
     *
     * @param deviceSn 设备SN
     * @param queryBeginAt 查询开始时间（包含缓冲）
     * @param queryEndAt 查询结束时间（包含缓冲）
     * @param actualBeginAt 实际任务开始时间
     * @param actualEndAt 实际任务结束时间
     * @param taskTypeDesc 任务类型描述，用于日志
     * @return 检查结果
     */
    private R<String> checkTimeRangeConflictEnhanced(String deviceSn, Long queryBeginAt, Long queryEndAt,
                                                     Long actualBeginAt, Long actualEndAt, String taskTypeDesc) {
        try {
            // 扩大查询范围，确保能查询到可能重叠的任务
            // 前后各扩展一天，以防止边界情况遗漏
            long expandedBeginAt = queryBeginAt - 24 * 60 * 60;
            long expandedEndAt = queryEndAt + 24 * 60 * 60;

            log.info("{}时间冲突检查，设备[{}]，查询时间范围：{} - {}，实际任务时间：{} - {}",
                    taskTypeDesc, deviceSn,
                    formatTimestamp(expandedBeginAt), formatTimestamp(expandedEndAt),
                    formatTimestamp(actualBeginAt), formatTimestamp(actualEndAt));

            // 调用OpenAPI获取指定时间范围内的飞行任务
            List<FhFlightTaskVO> existingTasks = FlightTaskOpenApiUtil.getFlightTaskListFromOpenApiSegmented(
                    deviceSn, expandedBeginAt, expandedEndAt);

            if (CollUtil.isEmpty(existingTasks)) {
                log.info("设备[{}]在时间范围内无现有任务，无冲突", deviceSn);
                return R.success("无时间冲突");
            }

            log.info("设备[{}]在时间范围内找到{}个现有任务", deviceSn, existingTasks.size());

            // 过滤掉已终止或启动失败的任务，只检查活跃任务
            List<FhFlightTaskVO> activeTasks = existingTasks.stream()
                    .filter(task -> {
                        String status = task.getStatus();
                        // 只检查可能冲突的任务状态：待执行、执行中等
                        return !"terminated".equals(status) && !"failed".equals(status) &&
                               !"cancelled".equals(status) && !"completed".equals(status);
                    })
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(activeTasks)) {
                log.info("设备[{}]在时间范围内无活跃任务，无冲突", deviceSn);
                return R.success("无时间冲突");
            }

            log.info("设备[{}]在时间范围内找到{}个活跃任务，开始检查时间重叠", deviceSn, activeTasks.size());

            // 检查新任务与现有活跃任务是否有时间重叠（使用实际任务时间进行冲突检测）
            for (FhFlightTaskVO existingTask : activeTasks) {
                R<String> overlapResult = isTimeRangeOverlapWithBuffer(actualBeginAt, actualEndAt, existingTask, taskTypeDesc);
                if (!overlapResult.isSuccess()) {
                    // 发现冲突，返回详细信息
                    return overlapResult;
                }
            }

            log.info("设备[{}]{}时间冲突检查通过，无冲突任务", deviceSn, taskTypeDesc);
            return R.success("无时间冲突");

        } catch (Exception e) {
            log.error("检查时间范围冲突时发生异常，设备[{}]，任务类型[{}]", deviceSn, taskTypeDesc, e);
            return R.fail("检查时间冲突失败：" + e.getMessage());
        }
    }

    /**
     * 带缓冲机制的时间重叠判断
     * 实现1分钟间隔容错，确保任务间至少有1分钟间隔
     *
     * @param newTaskBeginAt 新任务开始时间
     * @param newTaskEndAt   新任务结束时间
     * @param existingTask   现有任务
     * @param taskTypeDesc   任务类型描述
     * @return 检查结果，如果重叠返回失败结果，否则返回成功
     */
    private R<String> isTimeRangeOverlapWithBuffer(Long newTaskBeginAt, Long newTaskEndAt, FhFlightTaskVO existingTask, String taskTypeDesc) {
        try {
            // 获取现有任务的时间信息
            Long existingBeginAt = existingTask.getBeginAt() != null ? existingTask.getBeginAt().getTime() / 1000 : null;
            Long existingEndAt = existingTask.getEndAt() != null ? existingTask.getEndAt().getTime() / 1000 : null;

            // 如果现有任务没有结束时间，假设默认时长（30分钟）
            if (existingEndAt == null && existingBeginAt != null) {
                existingEndAt = existingBeginAt + 30 * 60; // 默认30分钟
                log.info("现有任务[{}]没有结束时间，使用默认30分钟时长，结束时间：{}",
                        existingTask.getFlightId(), formatTimestamp(existingEndAt));
            }

            // 如果现有任务没有开始时间，跳过检查
            if (existingBeginAt == null) {
                log.warn("现有任务[{}]没有开始时间，跳过冲突检查", existingTask.getFlightId());
                return R.success("跳过检查");
            }

            // 添加1分钟缓冲时间到现有任务
            final long BUFFER_SECONDS = 60;
            long bufferedExistingBeginAt = existingBeginAt - BUFFER_SECONDS;
            long bufferedExistingEndAt = existingEndAt + BUFFER_SECONDS;

            log.info("检查时间重叠（含1分钟缓冲）：新任务[{} - {}] vs 现有任务[{}][原始:{} - {}，缓冲后:{} - {}]",
                    formatTimestamp(newTaskBeginAt), formatTimestamp(newTaskEndAt),
                    existingTask.getFlightId(),
                    formatTimestamp(existingBeginAt), formatTimestamp(existingEndAt),
                    formatTimestamp(bufferedExistingBeginAt), formatTimestamp(bufferedExistingEndAt));

            // 判断时间重叠：使用缓冲后的现有任务时间进行检查
            boolean isOverlap = newTaskBeginAt < bufferedExistingEndAt && bufferedExistingBeginAt < newTaskEndAt;

            if (isOverlap) {
                // 计算实际间隔时间
                long actualGap = Math.min(
                    Math.abs(newTaskBeginAt - existingEndAt),
                    Math.abs(existingBeginAt - newTaskEndAt)
                );

                String conflictMsg = String.format(
                        "时间冲突：%s时间段[%s - %s]与现有任务[%s][%s - %s]间隔不足1分钟（实际间隔：%d秒），请调整任务时间",
                        taskTypeDesc,
                        formatTimestamp(newTaskBeginAt), formatTimestamp(newTaskEndAt),
                        existingTask.getFlightId(),
                        formatTimestamp(existingBeginAt), formatTimestamp(existingEndAt),
                        actualGap
                );
                log.warn(conflictMsg);
                return R.fail(conflictMsg);
            } else {
                log.info("无时间重叠：新任务[{} - {}] vs 现有任务[{}][{} - {}]，间隔充足",
                        formatTimestamp(newTaskBeginAt), formatTimestamp(newTaskEndAt),
                        existingTask.getFlightId(),
                        formatTimestamp(existingBeginAt), formatTimestamp(existingEndAt));
                return R.success("无重叠");
            }

        } catch (Exception e) {
            log.error("判断时间重叠时发生异常，现有任务[{}]", existingTask.getFlightId(), e);
            return R.fail("判断时间重叠失败：" + e.getMessage());
        }
    }

    /**
     * 检查现有任务与新任务时间段是否重叠（原始方法，保留兼容性）
     *
     * @param existingTask 现有任务
     * @param newTaskBeginAt 新任务开始时间（秒级时间戳）
     * @param newTaskEndAt 新任务结束时间（秒级时间戳）
     * @return 是否重叠
     */
    private boolean isTimeRangeOverlap(FhFlightTaskVO existingTask, Long newTaskBeginAt, Long newTaskEndAt) {
        if (existingTask == null || newTaskBeginAt == null || newTaskEndAt == null) {
            return false;
        }

        // 获取现有任务的时间戳（FhFlightTaskVO中的时间字段为Date类型）
        Long existingBeginAt = null;
        Long existingEndAt = null;

        if (existingTask.getBeginAt() != null) {
            existingBeginAt = existingTask.getBeginAt().getTime() / 1000;
            log.info("现有任务[{}] beginAt转换: Date={} -> Long={}",
                existingTask.getUuid(), existingTask.getBeginAt(), existingBeginAt);
        }

        if (existingTask.getEndAt() != null) {
            existingEndAt = existingTask.getEndAt().getTime() / 1000;
            log.info("现有任务[{}] endAt转换: Date={} -> Long={}",
                existingTask.getUuid(), existingTask.getEndAt(), existingEndAt);
        }

        // 如果现有任务没有结束时间，假设任务持续30分钟
        if (existingEndAt == null) {
            if (existingBeginAt != null) {
                // 默认30分钟任务时长
                existingEndAt = existingBeginAt + 30 * 60;
            } else {
                // 既没有开始时间也没有结束时间，无法判断
                return false;
            }
        }

        // 如果现有任务没有开始时间，无法判断重叠
        if (existingBeginAt == null) {
            return false;
        }

        // 确保新任务的时间范围有效
        if (newTaskBeginAt >= newTaskEndAt) {
            log.warn("新任务时间范围无效：开始时间[{}] >= 结束时间[{}]", newTaskBeginAt, newTaskEndAt);
            return false;
        }

        // 确保现有任务的时间范围有效
        if (existingBeginAt >= existingEndAt) {
            log.warn("现有任务[{}]时间范围无效：开始时间[{}] >= 结束时间[{}]",
                existingTask.getUuid(), existingBeginAt, existingEndAt);
            return false;
        }

        /*
         * 检查时间段重叠：两个时间段重叠当且仅当 start1 < end2 && start2 < end1
         * 这个公式能检测所有类型的重叠：
         * 1. 新任务完全包含现有任务：newTaskBeginAt <= existingBeginAt && existingEndAt <= newTaskEndAt
         * 2. 现有任务完全包含新任务：existingBeginAt <= newTaskBeginAt && newTaskEndAt <= existingEndAt
         * 3. 新任务开始时间在现有任务时间段内：existingBeginAt <= newTaskBeginAt < existingEndAt
         * 4. 新任务结束时间在现有任务时间段内：existingBeginAt < newTaskEndAt <= existingEndAt
         * 5. 部分重叠的其他情况
         */
        boolean isOverlap = newTaskBeginAt < existingEndAt && existingBeginAt < newTaskEndAt;

        if (isOverlap) {
            log.info("检测到时间重叠 - 现有任务[{}]: [{}({})-{}({})], 新任务: [{}({})-{}({})]",
                existingTask.getUuid(),
                formatTimestamp(existingBeginAt), existingBeginAt,
                formatTimestamp(existingEndAt), existingEndAt,
                formatTimestamp(newTaskBeginAt), newTaskBeginAt,
                formatTimestamp(newTaskEndAt), newTaskEndAt);
        } else {
            log.info("无时间重叠 - 现有任务[{}]: [{}({})-{}({})], 新任务: [{}({})-{}({})]",
                existingTask.getUuid(),
                formatTimestamp(existingBeginAt), existingBeginAt,
                formatTimestamp(existingEndAt), existingEndAt,
                formatTimestamp(newTaskBeginAt), newTaskBeginAt,
                formatTimestamp(newTaskEndAt), newTaskEndAt);
        }

        return isOverlap;
    }

    /**
     * 格式化时间戳为可读的时间字符串
     *
     * @param timestamp 时间戳（秒）
     * @return 格式化的时间字符串
     */
    private String formatTimestamp(Long timestamp) {
        if (timestamp == null) {
            return "未知";
        }
        try {
            return OffsetDateTime.ofInstant(Instant.ofEpochSecond(timestamp), systemDefault())
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            return String.valueOf(timestamp);
        }
    }

    /**
     * 获取所有设备在指定年份的月度任务统计
     *
     * @param deviceList 设备列表
     * @param year 年份
     * @return 设备SN -> (月份 -> 任务数量) 的映射
     */
    private Map<String, Map<Integer, Long>> getAllDevicesMonthlyTasks(List<FhOAPrjDeviceVO> deviceList, Integer year) {
        try {
            // 提取所有的SN号
            List<String> droneSNList = deviceList.stream()
                .filter(device -> device.getDrone() != null && device.getDrone().getSn() != null)
                .map(device -> device.getGateway().getSn())
                .collect(Collectors.toList());

            if (CollUtil.isEmpty(droneSNList)) {
                log.info("未找到有效的无人机设备");
                return Collections.emptyMap();
            }

            // 计算年份的开始和结束时间
            Year targetYear = Year.of(year);
            OffsetDateTime startDate = targetYear.atDay(1)
                .atStartOfDay()
                .atZone(systemDefault())
                .toOffsetDateTime();

            OffsetDateTime endDate = targetYear.atMonth(12)
                .atEndOfMonth()
                .atTime(23, 59, 59)
                .atZone(systemDefault())
                .toOffsetDateTime();

            // 构建查询条件并执行查询
            List<FhFlightTask> allTasksList = fhFlightTaskMapper.selectList(
                new LambdaQueryWrapper<FhFlightTask>()
                    .in(FhFlightTask::getSn, droneSNList)
					.eq(FhFlightTask::getTaskStatus, FlightTaskStatus.SUCCESS.getValue())
                    .ge(FhFlightTask::getBeginAt, startDate)
                    .le(FhFlightTask::getBeginAt, endDate)
                    .eq(FhFlightTask::getIsDeleted, 0)
            );

            // 使用Stream API按设备SN和月份分组统计任务数量
            return allTasksList.stream()
                .collect(Collectors.groupingBy(FhFlightTask::getSn, Collectors.groupingBy(
                        task -> task.getBeginAt().getMonthValue(),
                        Collectors.counting())));
        } catch (Exception e) {
            log.error("获取所有设备在[{}]年的月度任务统计失败", year, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public List<FhFlightTaskVO> getFlightTaskListByGrid(FhFlightTaskGridQueryDTO requestDTO) {
        LambdaQueryWrapper<FhFlightTask> queryWrapper = new LambdaQueryWrapper<>();

        // 根据时间范围过滤
        if (requestDTO.getBeginAt() != null && requestDTO.getEndAt() != null) {
            // 使用ZoneId.systemDefault()获取系统默认时区
            ZoneId zoneId = ZoneId.of("Asia/Shanghai");
            // 将秒级时间戳转换为OffsetDateTime，确保使用正确的时区
            OffsetDateTime beginTime = OffsetDateTime.ofInstant(Instant.ofEpochSecond(requestDTO.getBeginAt()), zoneId);
            OffsetDateTime endTime = OffsetDateTime.ofInstant(Instant.ofEpochSecond(requestDTO.getEndAt()), zoneId);
            // 记录转换后的时间，便于调试
            log.debug("时间范围过滤：beginTime={}, endTime={}", beginTime, endTime);
            queryWrapper.between(FhFlightTask::getBeginAt, beginTime, endTime);
        }

        // 根据任务类型过滤
        if (requestDTO.getTaskType() != null) {
            queryWrapper.eq(FhFlightTask::getTaskType, requestDTO.getTaskType().name());
        }

        // 根据任务状态过滤
        if (requestDTO.getStatus() != null && requestDTO.getStatus().length > 0) {
            // 构建一个条件:status为null或者在指定的status列表中
            queryWrapper.and(wrapper -> wrapper
                .isNull(FhFlightTask::getTaskStatus)
                .or()
                .in(FhFlightTask::getTaskStatus, Arrays.asList(requestDTO.getStatus()))
            );
        }

        // 根据时间倒序
        queryWrapper.orderByDesc(FhFlightTask::getBeginAt);

        // 执行查询
        List<FhFlightTask> taskList = fhFlightTaskMapper.selectList(queryWrapper);

        // 将实体列表转换为VO列表
        List<FhFlightTaskVO> collect = taskList.stream().map(entity -> {
            FhFlightTaskVO vo = new FhFlightTaskVO();
            BeanUtils.copyProperties(entity, vo);
            // 将OffsetDateTime转换为Date对象
            if (entity.getBeginAt() != null) {
                vo.setBeginAt(Date.from(entity.getBeginAt().toInstant()));
            }
            if (entity.getEndAt() != null) {
                vo.setEndAt(Date.from(entity.getEndAt().toInstant()));
            }
            // 使用工具类移除任务名称中的时间后缀
            vo.setName(FlightTaskNameUtil.removeTimeSuffix(vo.getName()));
            return vo;
        }).collect(Collectors.toList());

        return collect;
    }

    @Override
    public FhFlightTaskVO getFlightTaskById(String id) {
        // 构建API请求URL - 使用飞行任务详情接口
        String url = String.format(FhOpenapiPathConstant.FLIGHT_TASK_DETAIL, id);
        // 调用大疆API获取媒体资源列表
        String response = FhOpenApiHttpUtil.get(url);
        if (StrUtil.isNotBlank(response)) {
            JSONObject data = FhOpenApiHttpUtil.resData(response);
            FhFlightTaskVO bean = data.toBean(FhFlightTaskVO.class);
            String status = data.getStr("status");
            bean.setTaskStatus(status == null || status.trim().isEmpty() ? FlightTaskStatus.TERMINATED.getValue() : status);
            // 使用工具类移除任务名称中的时间后缀
            bean.setName(FlightTaskNameUtil.removeTimeSuffix(bean.getName()));
            return bean;
        }
        return null;
    }

    @Override
    public FhFlightTaskTrackVO getFlightTaskTrack(String taskUuid) {
        log.info("获取飞行任务轨迹信息，任务UUID：{}", taskUuid);
        try {
            // 参数校验
            if (StrUtil.isBlank(taskUuid)) {
                log.error("获取飞行任务轨迹信息失败，任务UUID为空");
                return null;
            }

            // 构建请求URL
            String url = String.format(FhOpenapiPathConstant.FLIGHT_TASK_TRACK, taskUuid);

            // 调用SK2 API获取轨迹信息
            String response = FhOpenApiHttpUtil.get(url);
            if (StrUtil.isBlank(response)) {
                log.error("获取飞行任务轨迹信息失败，API返回空响应");
                return null;
            }

            // 解析响应数据
            JSONObject responseJson = JSONUtil.parseObj(response);
            if (responseJson.getInt("code") != 0) {
                log.error("获取飞行任务轨迹信息失败，API响应错误：{}", responseJson.getStr("message"));
                return null;
            }

            JSONObject data = responseJson.getJSONObject("data");
            if (data == null) {
                log.error("获取飞行任务轨迹信息失败，API响应data为空");
                return null;
            }

            // 从data中获取name和wayline_uuid
            String name = data.getStr("name", "");
            String waylineUuid = data.getStr("wayline_uuid", "");

            // 从data中获取track对象
            JSONObject trackJson = data.getJSONObject("track");
            if (trackJson == null) {
                log.error("获取飞行任务轨迹信息失败，API响应track为空");
                return null;
            }

            // 解析轨迹点数据
            List<FhFlightTaskTrackPointVO> pointList = new ArrayList<>();
            JSONArray points = trackJson.getJSONArray("points");
            if (points != null && !points.isEmpty()) {
                for (int i = 0; i < points.size(); i++) {
                    JSONObject point = points.getJSONObject(i);
                    FhFlightTaskTrackPointVO trackPoint = new FhFlightTaskTrackPointVO(
                        point.getLong("timestamp"),
                        point.getDouble("latitude"),
                        point.getDouble("longitude"),
                        point.getDouble("height")
                    );
                    pointList.add(trackPoint);
                }
            }

            // 构建轨迹信息对象
            FhFlightTaskTrackInfoVO trackInfo = new FhFlightTaskTrackInfoVO(
                trackJson.getStr("track_id", ""),
                trackJson.getStr("drone_sn", ""),
                trackJson.getInt("flight_distance", 0),
                trackJson.getInt("flight_duration", 0),
                pointList
            );

            // 使用工具类移除任务名称中的时间后缀
            String cleanedName = FlightTaskNameUtil.removeTimeSuffix(name);

            // 构建返回对象
            FhFlightTaskTrackVO trackVO = new FhFlightTaskTrackVO(taskUuid, cleanedName, waylineUuid, trackInfo);

            return trackVO;
        } catch (Exception e) {
            log.error("获取飞行任务轨迹信息异常", e);
            return null;
        }
    }

    @Override
    public R<String> updateFlightTaskDistance() {
        log.info("开始手动更新飞行任务距离");

        try {
            FlightTaskDistanceUpdateResult result = executeFlightTaskDistanceUpdate();
            String resultMessage = String.format("飞行任务距离更新完成，成功更新%d个任务，失败%d个任务",
                result.getSuccessCount(), result.getFailCount());
            log.info(resultMessage);

            return R.success(resultMessage);
        } catch (Exception e) {
            log.error("手动更新飞行任务距离失败", e);
            return R.fail("更新飞行任务距离失败: " + e.getMessage());
        }
    }

    /**
     * 执行飞行任务距离更新的核心逻辑
     * 该方法被定时任务和手动触发接口共同使用
     *
     * @return 更新结果统计
     */
    public FlightTaskDistanceUpdateResult executeFlightTaskDistanceUpdate() {
        // 查询任务状态为成功且飞行距离为空的飞行任务
        List<FhFlightTask> tasksToUpdate = findTasksWithoutDistance();

        if (tasksToUpdate.isEmpty()) {
            log.info("没有需要更新飞行距离的飞行任务");
            return new FlightTaskDistanceUpdateResult(0, 0);
        }

        log.info("找到{}个需要更新飞行距离的飞行任务", tasksToUpdate.size());

        int successCount = 0;
        int failCount = 0;

        for (FhFlightTask task : tasksToUpdate) {
            try {
                // 调用飞行任务轨迹查询方法获取飞行距离
                FhFlightTaskTrackVO trackVO = getFlightTaskTrack(task.getUuid());

                if (trackVO != null && trackVO.getTrack() != null) {
                    // 获取飞行距离
                    Integer flightDistance = trackVO.getTrack().getFlightDistance();

                    if (flightDistance != null && flightDistance > 0) {
                        // 更新飞行任务的飞行距离
                        task.setFlightDistance(flightDistance.longValue());

                        // 更新数据库
                        int result = fhFlightTaskMapper.updateById(task);
                        if (result > 0) {
                            log.info("成功更新飞行任务[{}]的飞行距离为{}米", task.getUuid(), flightDistance);
                            successCount++;
                        } else {
                            log.warn("更新飞行任务[{}]的飞行距离失败", task.getUuid());
                            failCount++;
                        }
                    } else {
                        log.warn("飞行任务[{}]的飞行距离为空或无效", task.getUuid());
                        failCount++;
                    }
                } else {
                    log.warn("获取飞行任务[{}]的轨迹信息失败", task.getUuid());
                    failCount++;
                }
            } catch (Exception e) {
                log.error("更新飞行任务[{}]的飞行距离时发生异常", task.getUuid(), e);
                failCount++;
            }
        }

        return new FlightTaskDistanceUpdateResult(successCount, failCount);
    }


    /**
     * 查询任务状态为成功且飞行距离为空的飞行任务 terminated
     */
    private List<FhFlightTask> findTasksWithoutDistance() {
        return fhFlightTaskMapper.selectList(
            new LambdaQueryWrapper<FhFlightTask>()
                .and(wrapper -> wrapper
                    .isNotNull(FhFlightTask::getCompletedAt)
                    .or()
                    .eq(FhFlightTask::getTaskStatus, FlightTaskStatus.TERMINATED.getValue())
                )
                .isNull(FhFlightTask::getFlightDistance)
        );
    }

}
