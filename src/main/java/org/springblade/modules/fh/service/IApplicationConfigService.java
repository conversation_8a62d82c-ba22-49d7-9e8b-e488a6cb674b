package org.springblade.modules.fh.service;

import org.springblade.modules.fh.dto.*;

/**
 * 应用配置服务接口
 * 支持Skii MinIO、MQTT、FH-SDK、EJ-FH-SERVER等多种配置的管理
 *
 * <AUTHOR>
 */
public interface IApplicationConfigService {

    // ==================== Skii MinIO配置服务方法 ====================

    /**
     * 获取MinIO服务端点
     * @return endpoint
     */
    String getSkiiMinioEndpoint();

    /**
     * 获取MinIO访问密钥
     * @return accessKey
     */
    String getSkiiMinioAccessKey();

    /**
     * 获取MinIO秘密密钥
     * @return secretKey
     */
    String getSkiiMinioSecretKey();

    /**
     * 获取MinIO存储桶名称
     * @return bucket
     */
    String getSkiiMinioBucket();

    /**
     * 更新MinIO服务端点
     * @param endpoint 服务端点
     * @return 是否成功
     */
    boolean updateSkiiMinioEndpoint(String endpoint);

    /**
     * 更新MinIO访问密钥
     * @param accessKey 访问密钥
     * @return 是否成功
     */
    boolean updateSkiiMinioAccessKey(String accessKey);

    /**
     * 更新MinIO秘密密钥
     * @param secretKey 秘密密钥
     * @return 是否成功
     */
    boolean updateSkiiMinioSecretKey(String secretKey);

    /**
     * 更新MinIO存储桶名称
     * @param bucket 存储桶名称
     * @return 是否成功
     */
    boolean updateSkiiMinioBucket(String bucket);

    // ==================== MQTT BASIC配置服务方法 ====================

    /**
     * 获取MQTT BASIC协议
     * @return protocol
     */
    String getMqttBasicProtocol();

    /**
     * 获取MQTT BASIC主机
     * @return host
     */
    String getMqttBasicHost();

    /**
     * 获取MQTT BASIC端口
     * @return port
     */
    String getMqttBasicPort();

    /**
     * 获取MQTT BASIC用户名
     * @return username
     */
    String getMqttBasicUsername();

    /**
     * 获取MQTT BASIC密码
     * @return password
     */
    String getMqttBasicPassword();

    /**
     * 获取MQTT BASIC客户端ID
     * @return clientId
     */
    String getMqttBasicClientId();

    /**
     * 获取MQTT BASIC路径
     * @return path
     */
    String getMqttBasicPath();

    /**
     * 更新MQTT BASIC协议
     * @param protocol 协议
     * @return 是否成功
     */
    boolean updateMqttBasicProtocol(String protocol);

    /**
     * 更新MQTT BASIC主机
     * @param host 主机
     * @return 是否成功
     */
    boolean updateMqttBasicHost(String host);

    /**
     * 更新MQTT BASIC端口
     * @param port 端口
     * @return 是否成功
     */
    boolean updateMqttBasicPort(String port);

    /**
     * 更新MQTT BASIC用户名
     * @param username 用户名
     * @return 是否成功
     */
    boolean updateMqttBasicUsername(String username);

    /**
     * 更新MQTT BASIC密码
     * @param password 密码
     * @return 是否成功
     */
    boolean updateMqttBasicPassword(String password);

    /**
     * 更新MQTT BASIC客户端ID
     * @param clientId 客户端ID
     * @return 是否成功
     */
    boolean updateMqttBasicClientId(String clientId);

    /**
     * 更新MQTT BASIC路径
     * @param path 路径
     * @return 是否成功
     */
    boolean updateMqttBasicPath(String path);

    // ==================== MQTT DRC配置服务方法 ====================

    /**
     * 获取MQTT DRC协议
     * @return protocol
     */
    String getMqttDrcProtocol();

    /**
     * 获取MQTT DRC主机
     * @return host
     */
    String getMqttDrcHost();

    /**
     * 获取MQTT DRC端口
     * @return port
     */
    String getMqttDrcPort();

    /**
     * 获取MQTT DRC路径
     * @return path
     */
    String getMqttDrcPath();

    /**
     * 获取MQTT DRC用户名
     * @return username
     */
    String getMqttDrcUsername();

    /**
     * 获取MQTT DRC密码
     * @return password
     */
    String getMqttDrcPassword();

    /**
     * 更新MQTT DRC协议
     * @param protocol 协议
     * @return 是否成功
     */
    boolean updateMqttDrcProtocol(String protocol);

    /**
     * 更新MQTT DRC主机
     * @param host 主机
     * @return 是否成功
     */
    boolean updateMqttDrcHost(String host);

    /**
     * 更新MQTT DRC端口
     * @param port 端口
     * @return 是否成功
     */
    boolean updateMqttDrcPort(String port);

    /**
     * 更新MQTT DRC路径
     * @param path 路径
     * @return 是否成功
     */
    boolean updateMqttDrcPath(String path);

    /**
     * 更新MQTT DRC用户名
     * @param username 用户名
     * @return 是否成功
     */
    boolean updateMqttDrcUsername(String username);

    /**
     * 更新MQTT DRC密码
     * @param password 密码
     * @return 是否成功
     */
    boolean updateMqttDrcPassword(String password);

    // ==================== FH-SDK配置服务方法 ====================

    /**
     * 获取FH-SDK MQTT入站主题
     * @return inboundTopic
     */
    String getFhSdkMqttInboundTopic();

    /**
     * 更新FH-SDK MQTT入站主题
     * @param inboundTopic 入站主题
     * @return 是否成功
     */
    boolean updateFhSdkMqttInboundTopic(String inboundTopic);

    // ==================== EJ-FH-SERVER配置服务方法 ====================

    /**
     * 获取EJ-FH-SERVER服务URL
     * @return url
     */
    String getEjFhServerUrl();

    /**
     * 更新EJ-FH-SERVER服务URL
     * @param url 服务URL
     * @return 是否成功
     */
    boolean updateEjFhServerUrl(String url);

    // ==================== 标注文件夹ID配置服务方法 ====================

    /**
     * 获取标注文件夹ID
     * @return 标注文件夹ID
     */
    String getAnnotationFolderId();

    /**
     * 更新标注文件夹ID
     * @param folderId 标注文件夹ID
     * @return 是否成功
     */
    boolean updateAnnotationFolderId(String folderId);



    // ==================== 通用配置管理方法 ====================

    /**
     * 刷新指定配置的缓存
     * @param paramKey 参数键
     */
    void refreshCache(String paramKey);

    /**
     * 刷新所有配置缓存
     */
    void refreshAllCache();

    /**
     * 预热配置缓存
     */
    void warmUpCache();



    // ==================== 配置验证和安全更新方法 ====================

    /**
     * 验证MinIO配置有效性
     * @param endpoint 服务端点
     * @param accessKey 访问密钥
     * @param secretKey 秘密密钥
     * @param bucket 存储桶名称
     * @return 验证结果
     */
    ConfigValidationResult validateMinioConfig(String endpoint, String accessKey, String secretKey, String bucket);

    /**
     * 验证MQTT配置有效性
     * @param protocol 协议
     * @param host 主机
     * @param port 端口
     * @param username 用户名
     * @param password 密码
     * @return 验证结果
     */
    ConfigValidationResult validateMqttConfig(String protocol, String host, String port, String username, String password);

    /**
     * 回滚配置到上一个版本
     * @param configKeys 要回滚的配置键列表
     * @return 回滚结果
     */
    boolean rollbackConfigs(java.util.List<String> configKeys);

    // ==================== 批量配置验证和更新方法 ====================

    /**
     * 批量验证多种配置
     * 支持同时验证Skii MinIO、MQTT Basic、MQTT DRC、FH-SDK、EJ-FH-SERVER等配置
     * @param request 批量配置验证请求
     * @return 批量验证结果
     */
    BatchConfigValidationResult validateBatchConfigs(BatchConfigValidationRequest request);

    /**
     * 批量更新多种配置（不进行验证）
     * 支持同时更新Skii MinIO、MQTT Basic、MQTT DRC、FH-SDK、EJ-FH-SERVER等配置
     * @param request 批量配置更新请求
     * @return 批量更新结果
     */
    BatchConfigUpdateResult updateBatchConfigs(BatchConfigUpdateRequest request);

    /**
     * 批量验证和更新多种配置
     * 支持同时更新Skii MinIO、MQTT Basic、MQTT DRC、FH-SDK、EJ-FH-SERVER等配置
     * @param request 批量配置更新请求
     * @return 批量更新结果
     */
    BatchConfigUpdateResult validateAndUpdateBatchConfigs(BatchConfigUpdateRequest request);

}
