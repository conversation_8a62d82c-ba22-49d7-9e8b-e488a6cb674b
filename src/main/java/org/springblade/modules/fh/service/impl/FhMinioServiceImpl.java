package org.springblade.modules.fh.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import io.minio.BucketExistsArgs;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.errors.ErrorResponseException;
import io.minio.http.Method;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.modules.fh.service.IFhMinioService;
import org.springblade.modules.fh.utils.minio.FhMinioClientUtil;
import org.springblade.modules.fh.utils.minio.FhMinioConfiguration;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class FhMinioServiceImpl implements IFhMinioService {
	@Override
	public String getObjectUrl(String objectKey) {
		if (StrUtil.isBlank(objectKey)){
			return null;
		}
		try {
			MinioClient client = FhMinioClientUtil.getMinioClient();

            return client.getPresignedObjectUrl(
				GetPresignedObjectUrlArgs.builder()
					.method(Method.GET)
					.bucket(FhMinioConfiguration.bucket)
					.object(objectKey)
					.expiry(FhMinioClientUtil.EXPIRY, TimeUnit.HOURS)
					.build());
		}catch (Exception e){
			log.error(e.getMessage());
		}
		return null;
	}

	@Override
	public R<Map<String, Object>> uploadFile(MultipartFile file, String bucket) {
		if (file == null || file.isEmpty()) {
			log.warn("上传文件为空或null");
			return null;
		}

		if (StrUtil.isBlank(bucket)) {
			log.error("存储桶名称不能为空");
			return null;
		}

		try {
			MinioClient client = FhMinioClientUtil.getMinioClient();

			// 检查存储桶是否存在，如果不存在则创建
			if (!client.bucketExists(BucketExistsArgs.builder().bucket(bucket).build())) {
				log.info("存储桶 [{}] 不存在，正在创建...", bucket);
				client.makeBucket(MakeBucketArgs.builder().bucket(bucket).build());
				log.info("存储桶 [{}] 创建成功", bucket);
			}

			// 生成唯一的文件ID
			String originalFilename = file.getOriginalFilename();
			String fileExtension = "";
			if (originalFilename != null && originalFilename.contains(".")) {
				fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
			}
			String objectKey = IdUtil.simpleUUID() + fileExtension;

			log.debug("开始上传文件到存储桶 [{}]，对象键: [{}]，文件大小: [{}] bytes",
					bucket, objectKey, file.getSize());

			// 上传文件
			InputStream inputStream = file.getInputStream();
			try {
				client.putObject(
					PutObjectArgs.builder()
						.bucket(bucket)
						.object(objectKey)
						.stream(inputStream, file.getSize(), -1)
						.contentType(file.getContentType())
						.build()
				);
				log.info("文件上传成功，存储桶: [{}]，对象键: [{}]", bucket, objectKey);
				Map<String, Object> result = Map.of("objectKey", objectKey);
				return R.data(result);
			} finally {
				inputStream.close();
			}

		} catch (ErrorResponseException e) {
			log.error("MinIO服务器响应错误 - 错误代码: [{}], 错误消息: [{}], 存储桶: [{}]",
					e.errorResponse().code(), e.errorResponse().message(), bucket, e);
			return null;
		} catch (Exception e) {
			log.error("文件上传失败，存储桶: [{}], 错误信息: [{}]", bucket, e.getMessage(), e);
			return null;
		}
	}
}
