package org.springblade.modules.fh.service;

import org.springblade.core.tool.api.R;
import org.springblade.modules.fh.pojo.dto.FhStreamForwarderCreateDTO;
import org.springblade.modules.fh.pojo.dto.FhStreamForwarderListItemDTO;
import org.springblade.modules.fh.pojo.dto.FhStreamForwarderStatusDTO;

import java.util.List;

/**
 * 码流转发器服务接口
 *
 * <AUTHOR> AI
 */
public interface IFhStreamForwarderService {

    /**
     * 创建码流转发器
     * 调用大疆API创建新的码流转发器
     *
     * @param createDTO 创建参数
     * @return 创建结果
     */
    R<String> createStreamForwarder(FhStreamForwarderCreateDTO createDTO);

    /**
     * 开启/关闭码流转发器
     * 调用大疆API控制码流转发器的启停状态
     *
     * @param statusDTO 状态控制参数
     * @return 操作结果
     */
    R<String> updateStreamForwarderStatus(FhStreamForwarderStatusDTO statusDTO);

    /**
     * 删除码流转发器
     * 调用大疆API删除指定的码流转发器
     *
     * @param converterId 码流转发器ID
     * @return 删除结果
     */
    R<String> deleteStreamForwarder(String converterId);

    /**
     * 获取码流转发器列表
     * 调用大疆API获取所有码流转发器的列表
     *
     * @return 码流转发器列表
     */
    R<List<FhStreamForwarderListItemDTO>> getStreamForwarderList();

}