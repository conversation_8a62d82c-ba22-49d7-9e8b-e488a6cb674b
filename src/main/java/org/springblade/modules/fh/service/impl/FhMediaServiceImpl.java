package org.springblade.modules.fh.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springblade.modules.beachwaste.mapper.BeachLitterMediaMapper;
import org.springblade.modules.beachwaste.pojo.entity.BeachLitterMedia;
import org.springblade.modules.fh.mapper.FhFlightTaskMapper;
import org.springblade.modules.fh.mqtt.MqttReply;
import org.springblade.modules.fh.mqtt.api.events.FileUploadCallback;
import org.springblade.modules.fh.mqtt.api.events.FileUploadCallbackFile;
import org.springblade.modules.fh.mqtt.api.events.Position;
import org.springblade.modules.fh.mqtt.api.events.abs.AbstractMediaService;
import org.springblade.modules.fh.mqtt.handler.events.TopicEventsRequest;
import org.springblade.modules.fh.mqtt.handler.events.TopicEventsResponse;
import org.springblade.modules.fh.service.IFhConfigService;
import org.springblade.modules.fh.service.IFhMediaService;
import org.springblade.modules.fh.service.IFhMinioService;
import org.springblade.modules.fh.utils.FhOpenApiHttpUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class FhMediaServiceImpl extends AbstractMediaService implements IFhMediaService {

    private static final String IMAGE_TYPE = "jpeg";

    @Value("${media.pixel-size:2}")
    private Long mediaPixelSize;

    @Value("${ai-detect.url}")
    private String aiDetectUrl;

    @Value("${ai-detect.callback-url}")
    private String aiCallbackUrl;

    @Resource
    private IFhMinioService fhMinioService;
    @Resource
    private IFhConfigService fhConfigService;
    @Resource
    private BeachLitterMediaMapper beachLitterMediaMapper;
	@Resource
	private FhFlightTaskMapper flightTaskMapper;

    @Override
    public TopicEventsResponse<MqttReply> fileUploadCallback(TopicEventsRequest<FileUploadCallback> request, MessageHeaders headers) {
        log.info("文件上传完成 ---> " + request.getData());

        FileUploadCallbackFile file = request.getData().getFile();

        // 非图片类型不做ai垃圾识别流程
        String fileName = file.getName();
        int lastDotIndex = fileName.lastIndexOf(".");
		String fileType = fileName.substring(lastDotIndex + 1);
		if (lastDotIndex == -1 || !IMAGE_TYPE.equalsIgnoreCase(fileType)) {
            return null;
        }

        //获取文件地址
        String fileUrl = fhMinioService.getObjectUrl(file.getObjectKey());

        //持久化（数据入库）
		String req = DigestUtils.md5Hex(file.getObjectKey());
		if (fhConfigService.checkMediaSave()) {
            try {
                if (StrUtil.isBlank(file.getObjectKey()) || StrUtil.isBlank(fileUrl) || StrUtil.isBlank(fileName)) {
                    log.warn("媒体数据校验失败，objectKey: {}, fileUrl: {}, fileName: {}", file.getObjectKey(), fileUrl, fileName);
                    return null;
                }

                BeachLitterMedia mediaData = new BeachLitterMedia();

				// 安全获取扩展信息
				if (file.getExt() != null) {
					String flightId = file.getExt().getFlightId();
					if (StrUtil.isNotBlank(flightId)) {
						try {
							var flightTask = flightTaskMapper.selectByUuid(flightId);
							if (flightTask != null) {
								String taskName = flightTask.getName();
								// 处理任务名称：移除倒数第三个"-"后的数据
								if (StrUtil.isNotBlank(taskName)) {
									String processedName = removeAfterThirdLastDash(taskName);
									mediaData.setTaskName(processedName);
								} else {
									mediaData.setTaskName(taskName);
								}
							}
							mediaData.setTaskUuid(flightId);
						} catch (Exception e) {
							log.warn("获取飞行任务信息失败，flightId: {}", flightId, e);
						}
					}

					Object payloadModelKey = file.getExt().getPayloadModelKey();
					if (payloadModelKey != null) {
						mediaData.setDeviceName(String.valueOf(payloadModelKey));
					}
				}

				// 安全获取元数据信息
				if (file.getMetadata() != null) {
					mediaData.setFhCreateTime(file.getMetadata().getCreatedTime());

					Position shootPosition = file.getMetadata().getShootPosition();
					if (shootPosition != null && shootPosition.getLat() != null && shootPosition.getLng() != null) {
						mediaData.setLocation(shootPosition.getLat() + "," + shootPosition.getLng());
					}
				}

                mediaData.setObjectKey(file.getObjectKey());
                mediaData.setFileUrl(fileUrl);
                mediaData.setFileName(fileName);
                mediaData.setFileMd5(req);
                mediaData.setPixelSize(mediaPixelSize);
				mediaData.setFileType(fileType);
				// 调用Mapper将数据插入到beach_litter_media表
                beachLitterMediaMapper.insert(mediaData);
                log.info("媒体数据持久化成功，objectKey: {}", file.getObjectKey());
            } catch (Exception e) {
                log.error("媒体数据持久化异常，objectKey: {}, fileUrl: {}, fileName: {}", file.getObjectKey(), fileUrl, fileName, e);
            }
        }

        //ai 图片类型
        if (fhConfigService.checkMediaAi()) {
            // 构建AI识别接口请求参数，符合OpenAPI文档定义
            List<Map<String, Object>> detectRequestList = new ArrayList<>(1);
            Map<String, Object> detectRequest = new HashMap<>(4);
            detectRequest.put("filePath", fileUrl);
            detectRequest.put("fileMd5", req);
            // 使用配置的回调地址
            detectRequest.put("callbackUrl", aiCallbackUrl);
            detectRequest.put("pixelSize", mediaPixelSize);
            detectRequestList.add(detectRequest);

            try {
                // 直接使用完整URL地址发送请求
                String response = HttpRequest.post(aiDetectUrl)
                    .header(FhOpenApiHttpUtil.X_REQUEST_ID, UUID.randomUUID().toString())
                    .body(JSONObject.toJSONString(detectRequestList))
                    .timeout(60000)
                    .execute().body();
                log.info("AI检测请求已发送，fileMd5: {}, 响应: {}", req, response);
            } catch (Exception e) {
                log.error("AI检测接口请求异常", e);
                return new TopicEventsResponse<MqttReply>().setData(MqttReply.error("AI检测接口请求异常"));
            }
        }

        return new TopicEventsResponse<MqttReply>().setData(MqttReply.success());
    }

    /**
     * 移除倒数第三个"-"后的数据，只保留之前的数据
     * @param taskName 原始任务名称
     * @return 处理后的任务名称
     */
    private String removeAfterThirdLastDash(String taskName) {
        if (StrUtil.isBlank(taskName)) {
            return taskName;
        }

        // 找到所有"-"的位置
        List<Integer> dashPositions = new ArrayList<>();
        for (int i = 0; i < taskName.length(); i++) {
            if (taskName.charAt(i) == '-') {
                dashPositions.add(i);
            }
        }

        // 如果"-"的数量少于3个，返回原字符串
        if (dashPositions.size() < 3) {
            return taskName;
        }

        // 获取倒数第三个"-"的位置
        int thirdLastDashIndex = dashPositions.get(dashPositions.size() - 3);

        // 返回倒数第三个"-"之前的部分
        return taskName.substring(0, thirdLastDashIndex);
    }

}
