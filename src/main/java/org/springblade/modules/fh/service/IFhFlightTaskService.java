package org.springblade.modules.fh.service;

import org.springblade.core.tool.api.R;
import org.springblade.modules.fh.pojo.dto.*;
import org.springblade.modules.fh.pojo.vo.FhDeviceTaskStatsVO;
import org.springblade.modules.fh.pojo.vo.FhFlightTaskTrackVO;
import org.springblade.modules.fh.pojo.vo.FhFlightTaskVO;
import org.springblade.modules.fh.pojo.vo.FhMediaResourceVO;
import org.springblade.modules.fh.pojo.vo.FhWaylineVO;

import java.util.List;

/**
 * 飞行任务管理服务接口
 *
 * <AUTHOR> AI
 */
public interface IFhFlightTaskService {

    /**
     * 获取飞行任务列表
     * 该方法会先获取所有drone设备的SN号，然后循环调用大疆API获取每个设备的飞行任务列表
     *
     * @param requestDTO 请求参数
     * @return 飞行任务列表
     */
    List<FhFlightTaskVO> getFlightTaskList(FhFlightTaskRequestByTimeBaseDTO requestDTO);

    /**
     * 获取所有任务的年份列表
     * 根据任务的设定开始时间，统计哪些年份有任务
     *
     * @return 年份列表，按降序排序
     */
    List<Integer> getTaskYears();

    /**
     * 获取设备执行任务统计
     * 根据年份统计每月各设备的任务数量，返回按设备组织的统计结果
     *
     * @param dto 包含年份的请求参数
     * @return 按设备组织的任务统计结果列表，每个设备包含12个月的任务数量数据
     */
    List<FhDeviceTaskStatsVO> getDeviceTaskStats(FhDeviceTaskStatsDTO dto);

    /**
     * 创建飞行任务
     * 调用大疆API创建新的飞行任务
     *
     * @param createDTO 创建飞行任务请求参数
     * @return 创建结果
     */
    R<String> createFlightTask(FhFlightTaskCreateDTO createDTO);

    /**
     * 获取项目下的航线列表
     * 调用大疆API获取项目下的航线列表
     *
     * @return 航线列表
     */
    List<FhWaylineVO> getWaylineList();

    /**
     * 更新飞行任务状态
     * 调用大疆API更新飞行任务的状态
	 *
     * @param updateDTO 更新状态请求参数
     * @return 更新结果
     */
    R<String> updateFlightTaskStatus(FhFlightTaskStatusUpdateDTO updateDTO);

    /**
     * 获取飞行任务媒体资源列表
     * 调用大疆API获取指定飞行任务的媒体资源列表
     *
     * @param taskUuid 请求参数
     * @return 媒体资源分页列表
     */
    List<FhMediaResourceVO> getFlightTaskMediaList(String taskUuid);

    /**
     * 根据网格ID和筛选条件获取飞行任务列表
     *
     * @param requestDTO 请求参数，包含网格ID、时间范围、状态和任务类型
     * @return 飞行任务列表
     */
    List<FhFlightTaskVO> getFlightTaskListByGrid(FhFlightTaskGridQueryDTO requestDTO);

	/**
	 * 根据ID获取飞行任务详情
	 * @param id 任务id
	 * @return
	 */
	FhFlightTaskVO getFlightTaskById(String id);

	/**
     * 获取飞行任务轨迹信息
     * 调用SK2 API获取指定飞行任务的轨迹信息
     *
     * @param taskUuid 任务UUID
     * @return 飞行任务轨迹信息
     */
    FhFlightTaskTrackVO getFlightTaskTrack(String taskUuid);

    /**
     * 手动更新飞行任务距离
     * 查找所有状态为成功且飞行距离为空的任务，通过调用轨迹API获取飞行距离并更新到数据库
     *
     * @return 更新结果信息
     */
    R<String> updateFlightTaskDistance();

}
