package org.springblade.modules.fh.service;

import org.springblade.modules.fh.pojo.dto.FhLiveStreamStartDTO;
import org.springblade.modules.fh.pojo.vo.FhLiveStreamResponseVO;

/**
 * 直播服务接口
 *
 * <AUTHOR> AI
 */
public interface IFhLiveStreamService {

    /**
     * 开启直播
     * 该方法调用大疆API开启设备直播功能
     *
     * @param startDTO 开启直播请求参数
     * @return 直播响应数据
     */
    FhLiveStreamResponseVO startLiveStream(FhLiveStreamStartDTO startDTO);

}
