package org.springblade.modules.fh.service;

import org.springblade.core.tool.api.R;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

public interface IFhMinioService {
	/**
	 * 获取对象URL
	 * @param objectKey 对象键
	 * @return 预签名URL
	 */
	String getObjectUrl(String objectKey);

	/**
	 * 上传文件到MinIO
	 *
	 * @param file   文件
	 * @param bucket 存储桶名称
	 * @return 文件对象键（文件ID）
	 */
	R<Map<String, Object>> uploadFile(MultipartFile file, String bucket);
}
