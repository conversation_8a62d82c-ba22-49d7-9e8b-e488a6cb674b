package org.springblade.modules.fh.service;

import org.springblade.modules.fh.dto.MinioConfigUpdateRequest;

/**
 * Skii MinIO配置服务接口
 *
 * <AUTHOR>
public interface ISkiiMinioConfigService {

    /**
     * 获取MinIO服务端点
     * @return endpoint
     */
    String getEndpoint();

    /**
     * 获取MinIO访问密钥
     * @return accessKey
     */
    String getAccessKey();

    /**
     * 获取MinIO秘密密钥
     * @return secretKey
     */
    String getSecretKey();

    /**
     * 获取MinIO存储桶名称
     * @return bucket
     */
    String getBucket();

    /**
     * 更新MinIO服务端点
     * @param endpoint 服务端点
     * @return 是否成功
     */
    boolean updateEndpoint(String endpoint);

    /**
     * 更新MinIO访问密钥
     * @param accessKey 访问密钥
     * @return 是否成功
     */
    boolean updateAccessKey(String accessKey);

    /**
     * 更新MinIO秘密密钥
     * @param secretKey 秘密密钥
     * @return 是否成功
     */
    boolean updateSecretKey(String secretKey);

    /**
     * 更新MinIO存储桶名称
     * @param bucket 存储桶名称
     * @return 是否成功
     */
    boolean updateBucket(String bucket);

    /**
     * 刷新指定配置的缓存
     * @param paramKey 参数键
     */
    void refreshCache(String paramKey);

    /**
     * 刷新所有MinIO配置缓存
     */
    void refreshAllCache();

    /**
     * 预热配置缓存
     */
    void warmUpCache();

    /**
     * 验证配置连通性
     * @return 验证结果
     */
    boolean validateConnection();

    /**
     * 批量更新所有MinIO配置参数
     * @param request 包含四个配置参数的请求对象
     * @return 是否成功
     */
    boolean updateAllConfigs(MinioConfigUpdateRequest request);
}
