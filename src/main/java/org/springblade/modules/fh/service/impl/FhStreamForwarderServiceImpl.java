package org.springblade.modules.fh.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springblade.core.tool.api.R;
import org.springblade.modules.fh.pojo.dto.FhStreamForwarderCreateDTO;
import org.springblade.modules.fh.pojo.dto.FhStreamForwarderListItemDTO;
import org.springblade.modules.fh.pojo.dto.FhStreamForwarderListResponseDTO;
import org.springblade.modules.fh.pojo.dto.FhStreamForwarderStatusDTO;
import org.springblade.modules.fh.service.IFhStreamForwarderService;
import org.springblade.modules.fh.utils.FhOpenApiHttpUtil;
import org.springblade.common.constant.FhOpenapiPathConstant;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 码流转发器服务实现类
 *
 * <AUTHOR> AI
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FhStreamForwarderServiceImpl implements IFhStreamForwarderService {

    @Override
    public R<String> createStreamForwarder(FhStreamForwarderCreateDTO createDTO) {
        try {
            log.info("创建码流转发器，设备SN: {}, 转发器名称: {}, 协议: {}",
                    createDTO.getSn(), createDTO.getConverterName(), createDTO.getSchema());

            // 验证协议参数的一致性和有效性
            createDTO.validateProtocolConsistency();

            // 构建基础请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("sn", createDTO.getSn());
            params.put("camera_index", createDTO.getCameraIndex());
            params.put("converter_name", createDTO.getConverterName());
            params.put("schema", createDTO.getSchema());

            // 添加协议特定参数
            Map<String, Object> protocolParams = createDTO.getProtocolParams();
            if (protocolParams != null && !protocolParams.isEmpty()) {
                // 将schema_option参数添加到请求中
                params.put("schema_option", protocolParams);
            }

            // 调用大疆API
            String url = "/openapi/v0.1/live-stream/converter";
            String response = FhOpenApiHttpUtil.post(url, JSONUtil.toJsonStr(params));

            log.info("创建码流转发器响应: {}", response);
            return R.success(response);

        } catch (Exception e) {
            log.error("创建码流转发器失败", e);
            return R.fail("创建码流转发器失败: " + e.getMessage());
        }
    }



    @Override
    public R<String> updateStreamForwarderStatus(FhStreamForwarderStatusDTO statusDTO) {
        try {
            String actionText = statusDTO.getAction() == true ? "开启" : "关闭";
            log.info("更新码流转发器状态，码流转发器ID: {}, 操作: {}", statusDTO.getConverterId(), actionText);

            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("auto_push_stream", statusDTO.getAction());

            // 构建API路径，使用converterId作为路径参数
            String apiPath = String.format(FhOpenapiPathConstant.LIVE_STREAM_CONVERTER_STATUS, statusDTO.getConverterId());

            // 调用大疆API控制码流转发器状态
            String requestBody = JSONUtil.toJsonStr(params);
            String result = FhOpenApiHttpUtil.put(apiPath, requestBody);

            log.info("{}码流转发器成功，响应: {}", actionText, result);
            return R.success(result);

        } catch (Exception e) {
            String actionText = statusDTO.getAction() == true ? "开启" : "关闭";
            log.error("{}码流转发器失败, 码流转发器ID: {}", actionText, statusDTO.getConverterId(), e);
            return R.fail(actionText + "码流转发器失败: " + e.getMessage());
        }
    }

    @Override
    public R<String> deleteStreamForwarder(String converterId) {
        try {
            log.info("删除码流转发器，码流转发器ID: {}", converterId);

            // 构建API路径，使用converterId作为路径参数
            String apiPath = String.format(FhOpenapiPathConstant.LIVE_STREAM_CONVERTER_STATUS, converterId);
            
            // 调用大疆API删除码流转发器
            String result = FhOpenApiHttpUtil.delete(apiPath, "");

            log.info("删除码流转发器成功，响应: {}", result);
            return R.success(result);

        } catch (Exception e) {
            log.error("删除码流转发器失败，码流转发器ID: {}", converterId, e);
            return R.fail("删除码流转发器失败: " + e.getMessage());
        }
    }

    @Override
    public R<List<FhStreamForwarderListItemDTO>> getStreamForwarderList() {
        try {
            log.info("获取码流转发器列表");

            // 调用大疆API获取码流转发器列表
            String response = FhOpenApiHttpUtil.get(FhOpenapiPathConstant.LIVE_STREAM_CONVERTER_LIST);
            log.info("获取码流转发器列表成功，响应: {}", response);

            // 解析响应JSON
            JSONObject responseJson = JSONUtil.parseObj(response);
            Integer code = responseJson.getInt("code");
            String message = responseJson.getStr("message");
            
            // 检查API调用是否成功
            if (code == null || code != 0) {
                log.error("大疆API返回错误，code: {}, message: {}", code, message);
                return R.fail("获取码流转发器列表失败: " + message);
            }

            // 提取data字段并转换为DTO列表
            Object dataObject = responseJson.get("data");
            List<FhStreamForwarderListItemDTO> resultList = new ArrayList<>();
            
            if (dataObject != null) {
                if (dataObject instanceof JSONArray) {
                    // data字段是数组类型
                    JSONArray dataArray = (JSONArray) dataObject;
                    for (Object item : dataArray) {
                        FhStreamForwarderListItemDTO dto = JSONUtil.toBean(JSONUtil.toJsonStr(item), FhStreamForwarderListItemDTO.class);
                        resultList.add(dto);
                    }
                } else if (dataObject instanceof JSONObject) {
                    // data字段是对象类型，检查是否包含list字段
                    JSONObject dataJsonObject = (JSONObject) dataObject;
                    if (dataJsonObject.containsKey("list")) {
                        // 包含list字段，使用包装DTO完整存储所有数据
                        FhStreamForwarderListResponseDTO responseDTO = JSONUtil.toBean(JSONUtil.toJsonStr(dataJsonObject), FhStreamForwarderListResponseDTO.class);
                        if (responseDTO != null && responseDTO.getList() != null) {
                            resultList.addAll(responseDTO.getList());
                            log.info("成功解析包装响应，包含{}个转发器项目，总数: {}, 页码: {}, 页大小: {}", 
                                    responseDTO.getList().size(), responseDTO.getTotal(), responseDTO.getPage(), responseDTO.getSize());
                        }
                    } else {
                        // 不包含list字段，作为单个项目处理
                        FhStreamForwarderListItemDTO dto = JSONUtil.toBean(JSONUtil.toJsonStr(dataJsonObject), FhStreamForwarderListItemDTO.class);
                        resultList.add(dto);
                    }
                } else {
                    log.warn("data字段类型不支持: {}", dataObject.getClass().getSimpleName());
                }
            }
            
            log.info("成功解析码流转发器列表，共{}条记录", resultList.size());
            return R.data(resultList);
            
        } catch (Exception e) {
            log.error("获取码流转发器列表失败，错误信息: {}", e.getMessage(), e);
            return R.fail("获取码流转发器列表失败: " + e.getMessage());
        }
    }

}
