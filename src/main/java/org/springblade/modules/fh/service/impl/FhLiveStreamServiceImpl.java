package org.springblade.modules.fh.service.impl;

import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.FhOpenapiPathConstant;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.modules.fh.pojo.dto.FhLiveStreamStartDTO;
import org.springblade.modules.fh.pojo.vo.FhLiveStreamResponseVO;
import org.springblade.modules.fh.service.IFhLiveStreamService;
import org.springblade.modules.fh.utils.FhOpenApiHttpUtil;
import org.springframework.stereotype.Service;

/**
 * 直播服务实现类
 *
 * <AUTHOR> AI
 */
@Slf4j
@Service
public class FhLiveStreamServiceImpl implements IFhLiveStreamService {

    @Override
    public FhLiveStreamResponseVO startLiveStream(FhLiveStreamStartDTO startDTO) {
        log.info("开启直播, 设备SN: {}, 摄像头索引: {}, 清晰度: {}",
                startDTO.getSn(), startDTO.getCameraIndex(), startDTO.getQualityType());

        try {
            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.set("sn", startDTO.getSn());
            requestBody.set("camera_index", startDTO.getCameraIndex());
            requestBody.set("video_expire", startDTO.getVideoExpire());
            requestBody.set("quality_type", startDTO.getQualityType());

            // 调用大疆API
            String response = FhOpenApiHttpUtil.post(FhOpenapiPathConstant.LIVE_STREAM_START, requestBody.toString());
            JSONObject responseData = FhOpenApiHttpUtil.resData(response);

            // 解析响应数据
            return parseStartLiveStreamResponse(responseData);

        } catch (Exception e) {
            log.error("开启直播失败", e);
            throw new ServiceException("开启直播失败: " + e.getMessage());
        }
    }

    /**
     * 解析开启直播响应数据
     *
     * @param responseData 响应数据
     * @return 直播响应VO
     */
    private FhLiveStreamResponseVO parseStartLiveStreamResponse(JSONObject responseData) {
        FhLiveStreamResponseVO responseVO = new FhLiveStreamResponseVO();

        // 解析直播推流Token有效期时间戳
        responseVO.setExpireTs(responseData.getLong("expire_ts"));

        // 解析直播拉流播放地址
        responseVO.setUrl(responseData.getStr("url"));

        // 解析直播推流类型
        responseVO.setUrlType(responseData.getStr("url_type"));

        return responseVO;
    }
}
