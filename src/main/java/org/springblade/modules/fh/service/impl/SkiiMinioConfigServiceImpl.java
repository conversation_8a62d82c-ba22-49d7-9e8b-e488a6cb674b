package org.springblade.modules.fh.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springblade.modules.fh.config.SkiiMinioConfig;
import org.springblade.modules.fh.dto.MinioConfigUpdateRequest;
import org.springblade.modules.fh.service.IApplicationConfigService;
import org.springblade.modules.fh.service.ISkiiMinioConfigService;
import org.springblade.modules.system.pojo.entity.Param;
import org.springblade.modules.system.service.IParamService;

/**
 * Skii MinIO配置服务实现类
 *
 * <AUTHOR>
@Slf4j
@Service
@RequiredArgsConstructor
public class SkiiMinioConfigServiceImpl implements ISkiiMinioConfigService {

    private final SkiiMinioConfig skiiMinioConfig;
    private final IParamService paramService;
    private final IApplicationConfigService applicationConfigService;

    @Override
    public String getEndpoint() {
        return skiiMinioConfig.getEndpoint();
    }

    @Override
    public String getAccessKey() {
        return skiiMinioConfig.getAccessKey();
    }

    @Override
    public String getSecretKey() {
        return skiiMinioConfig.getSecretKey();
    }

    @Override
    public String getBucket() {
        return skiiMinioConfig.getBucket();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateEndpoint(String endpoint) {
        return updateConfigValue(SkiiMinioConfig.ENDPOINT_KEY, endpoint, "Skii MinIO服务端点");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAccessKey(String accessKey) {
        return updateConfigValue(SkiiMinioConfig.ACCESS_KEY_KEY, accessKey, "Skii MinIO访问密钥");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSecretKey(String secretKey) {
        return updateConfigValue(SkiiMinioConfig.SECRET_KEY_KEY, secretKey, "Skii MinIO秘密密钥");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBucket(String bucket) {
        return updateConfigValue(SkiiMinioConfig.BUCKET_KEY, bucket, "Skii MinIO存储桶名称");
    }

    @Override
    public void refreshCache(String paramKey) {
        skiiMinioConfig.refreshCache(paramKey);
    }

    @Override
    public void refreshAllCache() {
        skiiMinioConfig.refreshAllCache();
    }

    @Override
    public void warmUpCache() {
        skiiMinioConfig.warmUpCache();
    }

    @Override
    public boolean validateConnection() {
        try {
            String endpoint = getEndpoint();
            String accessKey = getAccessKey();
            String secretKey = getSecretKey();
            String bucket = getBucket();

            // 复用ApplicationConfigService中的通用验证方法
            var validationResult = applicationConfigService.validateMinioConfig(endpoint, accessKey, secretKey, bucket);
            
            if (validationResult.isValid()) {
                log.info("[CONFIG] [INFO] MinIO连通性验证成功: endpoint={}", endpoint);
                return true;
            } else {
                log.warn("[CONFIG] [WARN] MinIO连通性验证失败: {}", validationResult.getFailureReason());
                return false;
            }

        } catch (Exception e) {
            log.error("[CONFIG] [ERROR] MinIO连通性验证异常: {}", e.getMessage());
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAllConfigs(MinioConfigUpdateRequest request) {
        try {
            log.info("[CONFIG] [INFO] 开始批量更新MinIO配置参数");
            
            // 批量更新四个配置参数
            boolean endpointUpdated = updateConfigValue(SkiiMinioConfig.ENDPOINT_KEY, request.getEndpoint(), "Skii MinIO服务端点");
            boolean accessKeyUpdated = updateConfigValue(SkiiMinioConfig.ACCESS_KEY_KEY, request.getAccessKey(), "Skii MinIO访问密钥");
            boolean secretKeyUpdated = updateConfigValue(SkiiMinioConfig.SECRET_KEY_KEY, request.getSecretKey(), "Skii MinIO秘密密钥");
            boolean bucketUpdated = updateConfigValue(SkiiMinioConfig.BUCKET_KEY, request.getBucket(), "Skii MinIO存储桶名称");
            
            // 检查是否所有更新都成功
            if (endpointUpdated && accessKeyUpdated && secretKeyUpdated && bucketUpdated) {
                // 批量更新成功后，刷新所有缓存
                refreshAllCache();
                log.info("[CONFIG] [INFO] MinIO配置批量更新成功，已刷新所有缓存");
                return true;
            } else {
                log.error("[CONFIG] [ERROR] MinIO配置批量更新失败，部分参数更新不成功");
                // 事务会自动回滚
                throw new RuntimeException("MinIO配置批量更新失败");
            }
        } catch (Exception e) {
            // 重新抛出异常，触发事务回滚
            log.error("[CONFIG] [ERROR] MinIO配置批量更新异常: error={}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新配置值的通用方法
     *
     * @param paramKey 参数键
     * @param paramValue 参数值
     * @param paramName 参数名称
     * @return 是否成功
     */
    private boolean updateConfigValue(String paramKey, String paramValue, String paramName) {
        try {
            // 查找现有配置
            Param existingParam = paramService.getOne(
                Wrappers.lambdaQuery(Param.class)
                    .eq(Param::getParamKey, paramKey)
                    .last("limit 1")
            );

            if (existingParam != null) {
                // 更新现有配置
                existingParam.setParamValue(paramValue);
                boolean updated = paramService.updateById(existingParam);
                if (updated) {
                    // 刷新缓存
                    refreshCache(paramKey);
                    log.info("[CONFIG] [INFO] 配置更新成功: key={}, value={}", paramKey, paramValue);
                    return true;
                } else {
                    log.error("[CONFIG] [ERROR] 配置更新失败: key={}", paramKey);
                    return false;
                }
            } else {
                // 创建新配置
                Param newParam = new Param();
                newParam.setParamKey(paramKey);
                newParam.setParamValue(paramValue);
                newParam.setParamName(paramName);
                newParam.setRemark(paramName + "配置");
                newParam.setStatus(1);

                boolean saved = paramService.save(newParam);
                if (saved) {
                    // 刷新缓存
                    refreshCache(paramKey);
                    log.info("[CONFIG] [INFO] 配置创建成功: key={}, value={}", paramKey, paramValue);
                    return true;
                } else {
                    log.error("[CONFIG] [ERROR] 配置创建失败: key={}", paramKey);
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("[CONFIG] [ERROR] 配置更新异常: key={}, error={}", paramKey, e.getMessage(), e);
            return false;
        }
    }
}
