package org.springblade.modules.fh.component.scheduler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.fh.mapper.FhFlightTaskMapper;
import org.springblade.modules.fh.pojo.entity.FhFlightTask;
import org.springblade.modules.fh.pojo.vo.FhFlightTaskVO;
import org.springblade.modules.fh.utils.FlightTaskNameUtil;
import org.springblade.modules.fh.utils.FlightTaskOpenApiUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 飞行任务状态定时检查更新
 * 定时查询飞行任务表中的数据，获取当前时间之前更新时间为空的数据
 * 通过司空API查询是否飞行完成并更新对应任务数据
 *
 * <AUTHOR> AI
 */
@Slf4j
@Component
@EnableScheduling
@RequiredArgsConstructor
public class FlightTaskStatusScheduler {

    /**
     * 查询时间范围扩展小时数（可配置）
     */
    @Value("${flight.task.query.time.extend.hours:24}")
    private int queryTimeExtendHours;

    /**
     * 异常数据判断的最小间隔小时数
     */
    @Value("${flight.task.abnormal.min.hours:48}")
    private int abnormalMinHours;

    private final FhFlightTaskMapper fhFlightTaskMapper;
    private final FlightTaskOpenApiUtil flightTaskOpenApiUtil;

    /**
     * 每一小时执行一次，检查未更新的飞行任务状态
     * cron表达式：秒 分 时 日 月 周
     */
    @Scheduled(cron = "0 0 */1 * * ?")
    public void checkAndUpdateFlightTaskStatus() {
        log.info("开始执行飞行任务状态检查定时任务");

        try {
            // 查询当前时间之前更新时间为空的飞行任务
            List<FhFlightTask> uncompletedTasks = findUncompletedFlightTasks();

            if (uncompletedTasks.isEmpty()) {
                log.info("没有需要更新的飞行任务");
                return;
            }

            log.info("找到{}个需要检查状态的飞行任务", uncompletedTasks.size());
			checkAndUpdateTaskStatus(uncompletedTasks);
            log.info("飞行任务状态检查定时任务执行完成");
        } catch (Exception e) {
            log.error("执行飞行任务状态检查定时任务出错", e);
        }
    }

    /**
     * 查询未完成的飞行任务
     * 条件：当前时间之前的任务 && 更新时间为空
     */
    private List<FhFlightTask> findUncompletedFlightTasks() {
		List<FhFlightTask> fhFlightTasks = fhFlightTaskMapper.selectList(
			new LambdaQueryWrapper<FhFlightTask>()
				// 完成时间为空
				.isNull(FhFlightTask::getCompletedAt)
				.and(wrapper -> wrapper
					.isNull(FhFlightTask::getTaskStatus)
					.or()
					.notIn(FhFlightTask::getTaskStatus, "terminated", "success")
				)
				// 按id升序排序
				.orderByAsc(FhFlightTask::getId)
		);
		log.info("找到{}个未完成的飞行任务", fhFlightTasks.size());
		return fhFlightTasks;
    }

    /**
     * 检查任务状态并更新（优化版本）
     * 通过司空API查询飞行任务状态
     * 将任务按设备SN分组，批量查询任务状态
     *
     * 优化点：
     * 1. 使用Map优化UUID匹配性能（O(1)查找）
     * 2. 添加事务管理确保数据一致性
     * 3. 批量数据库更新
     * 4. 可配置的时间范围
     * 5. 增强异常处理
     * 6. 智能异常数据判断
     */
    @Transactional(rollbackFor = Exception.class)
	protected void checkAndUpdateTaskStatus(List<FhFlightTask> uncompletedTasks) {
        if (uncompletedTasks == null || uncompletedTasks.isEmpty()) {
            return;
        }

        long startTime = System.currentTimeMillis();
        AtomicInteger totalProcessed = new AtomicInteger(0);
        AtomicInteger totalUpdated = new AtomicInteger(0);
        AtomicInteger totalFailed = new AtomicInteger(0);

        // 按设备SN分组任务
        Map<String, List<FhFlightTask>> tasksByDeviceSn = uncompletedTasks.stream()
                .collect(Collectors.groupingBy(FhFlightTask::getSn));

        log.info("开始批量检查飞行任务状态，共{}个设备，{}个任务",
                tasksByDeviceSn.size(), uncompletedTasks.size());

        tasksByDeviceSn.forEach((deviceSn, tasks) -> {
            try {
                processDeviceTasks(deviceSn, tasks, totalProcessed, totalUpdated, totalFailed);
            } catch (Exception e) {
                log.error("处理设备[{}]的任务时发生异常", deviceSn, e);
                totalFailed.addAndGet(tasks.size());
            }
        });

        long endTime = System.currentTimeMillis();
        log.info("飞行任务状态检查完成，耗时{}ms，处理{}个任务，成功更新{}个，失败{}个",
                endTime - startTime, totalProcessed.get(), totalUpdated.get(), totalFailed.get());
    }

    /**
     * 处理单个设备的任务列表
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    private void processDeviceTasks(String deviceSn, List<FhFlightTask> tasks,
                                   AtomicInteger totalProcessed, AtomicInteger totalUpdated, AtomicInteger totalFailed) {
        log.info("开始检查设备[{}]的飞行任务状态，任务数量: {}", deviceSn, tasks.size());

        // 过滤空值并获取有效的开始时间列表
        List<OffsetDateTime> validBeginTimes = tasks.stream()
            .map(FhFlightTask::getBeginAt)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        if (validBeginTimes.isEmpty()) {
            log.warn("设备[{}]的所有任务beginAt时间都为空，跳过状态检查", deviceSn);
            return;
        }

        // 确定查询时间范围（使用可配置参数）
        OffsetDateTime minBeginAt = validBeginTimes.stream().min(OffsetDateTime::compareTo).get();
        OffsetDateTime maxBeginAt = validBeginTimes.stream().max(OffsetDateTime::compareTo).get();
        OffsetDateTime now = OffsetDateTime.now();

        // 调用优化后的分段查询方法
        List<FhFlightTaskVO> apiTasks = flightTaskOpenApiUtil.getFlightTaskListWithTimeRangeOptimization(deviceSn, minBeginAt, maxBeginAt);

        if (apiTasks == null || apiTasks.isEmpty()) {
            log.warn("设备[{}]从API获取的任务列表为空", deviceSn);
            return;
        }

        // 优化：将API任务转换为Map，提高查找效率（O(1)）
        Map<String, FhFlightTaskVO> apiTaskMap = new HashMap<>(apiTasks.size());
        for (FhFlightTaskVO apiTask : apiTasks) {
            if (apiTask.getUuid() != null && !apiTask.getUuid().trim().isEmpty()) {
                apiTaskMap.put(apiTask.getUuid().trim(), apiTask);
            }
        }

        // 批量处理任务更新
        List<FhFlightTask> tasksToUpdate = new ArrayList<>();
        List<FhFlightTask> potentialAbnormalTasks = new ArrayList<>();
        List<String> dbTaskUuids = new ArrayList<>(tasks.size());

        // 遍历数据库任务，进行匹配和更新
        for (FhFlightTask task : tasks) {
            totalProcessed.incrementAndGet();

            if (task.getUuid() == null || task.getUuid().trim().isEmpty()) {
                log.warn("发现空UUID的数据库任务，跳过处理");
                continue;
            }

            String taskUuid = task.getUuid().trim();
            dbTaskUuids.add(taskUuid);

            FhFlightTaskVO matchedApiTask = apiTaskMap.get(taskUuid);

            if (matchedApiTask != null) {
                // 找到匹配的API任务，准备更新
                if (updateTaskFromApiResponse(task, matchedApiTask)) {
                    tasksToUpdate.add(task);
                    log.debug("飞行任务[{}]状态更新为[{}]", task.getUuid(), task.getTaskStatus());
                } else {
                    totalFailed.incrementAndGet();
                    log.warn("更新飞行任务[{}]失败", task.getUuid());
                }
            } else {
                // 数据库中存在但API中不存在的任务，需要智能判断是否为异常
                if (isTaskAbnormal(task, now)) {
                    potentialAbnormalTasks.add(task);
                }
            }
        }

        // 批量更新数据库
        if (!tasksToUpdate.isEmpty()) {
            int updateCount = batchUpdateTasks(tasksToUpdate);
            totalUpdated.addAndGet(updateCount);
            log.info("设备[{}]批量更新了{}个任务", deviceSn, updateCount);
        }

        // 处理潜在异常任务
        if (!potentialAbnormalTasks.isEmpty()) {
            log.info("设备[{}]发现{}个潜在异常任务", deviceSn, potentialAbnormalTasks.size());
            handleAbnormalTasksIntelligently(potentialAbnormalTasks);
        }

        // 记录API中存在但数据库中不存在的任务
        List<FhFlightTaskVO> orphanApiTasks = apiTasks.stream()
            .filter(apiTask -> apiTask.getUuid() != null &&
                    !dbTaskUuids.contains(apiTask.getUuid().trim()))
            .collect(Collectors.toList());

        if (!orphanApiTasks.isEmpty()) {
            log.info("设备[{}]发现{}个API中存在但数据库中不存在的任务", deviceSn, orphanApiTasks.size());
            orphanApiTasks.forEach(apiTask ->
                log.debug("孤立API任务UUID: {}, 状态: {}", apiTask.getUuid(), apiTask.getTaskStatus()));
        }
    }


    /**
     * 批量更新任务
     */
    private int batchUpdateTasks(List<FhFlightTask> tasksToUpdate) {
        if (tasksToUpdate == null || tasksToUpdate.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        for (FhFlightTask task : tasksToUpdate) {
            try {
                int result = fhFlightTaskMapper.updateById(task);
                if (result > 0) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量更新任务[{}]失败", task.getUuid(), e);
            }
        }
        return successCount;
    }

    /**
     * 智能判断任务是否为异常
     * 考虑任务状态、时间等因素
     */
    private boolean isTaskAbnormal(FhFlightTask task, OffsetDateTime now) {
        // 如果任务已经标记为删除，不再处理
        if (task.getIsDeleted() != null && task.getIsDeleted() != 0) {
            return false;
        }

        // 如果任务开始时间为空，无法判断，暂不标记为异常
        if (task.getBeginAt() == null) {
            return false;
        }

        // 如果任务开始时间距离现在超过配置的最小间隔小时数，且API中找不到，可能是异常
        long hoursSinceBegin = java.time.Duration.between(task.getBeginAt(), now).toHours();
        return hoursSinceBegin > abnormalMinHours;
    }

    /**
     * 智能处理异常任务
     */
    private void handleAbnormalTasksIntelligently(List<FhFlightTask> potentialAbnormalTasks) {
        if (potentialAbnormalTasks == null || potentialAbnormalTasks.isEmpty()) {
            return;
        }

        log.info("开始智能处理{}个潜在异常任务", potentialAbnormalTasks.size());

        List<FhFlightTask> confirmedAbnormalTasks = new ArrayList<>();

        for (FhFlightTask task : potentialAbnormalTasks) {
            // 进一步验证是否为真正的异常任务
            if (shouldMarkAsAbnormal(task)) {
				// 标记为异常数据
                task.setIsDeleted(3);
                confirmedAbnormalTasks.add(task);
                log.info("确认异常任务[{}]，开始时间: {}", task.getUuid(), task.getBeginAt());
            } else {
                log.debug("任务[{}]暂不标记为异常，继续观察", task.getUuid());
            }
        }

        // 批量更新确认的异常任务
        if (!confirmedAbnormalTasks.isEmpty()) {
            int updateCount = batchUpdateTasks(confirmedAbnormalTasks);
            log.info("成功标记{}个异常任务", updateCount);
        }
    }

    /**
     * 判断是否应该标记为异常任务
     */
    private boolean shouldMarkAsAbnormal(FhFlightTask task) {
        // 可以根据业务规则进一步细化判断逻辑
        // 例如：检查任务状态、执行时间、设备状态等

        // 如果任务状态已经是完成状态，但API中找不到，可能确实是异常
        if ("completed".equalsIgnoreCase(task.getTaskStatus()) ||
            "finished".equalsIgnoreCase(task.getTaskStatus())) {
            return true;
        }

        // 如果任务创建时间很久远（超过一定天数），且API中找不到，可能是异常
        if (task.getCreateTime() != null) {
            long daysSinceCreate = java.time.Duration.between(
                task.getCreateTime().toInstant().atOffset(java.time.ZoneOffset.UTC),
                OffsetDateTime.now()).toDays();
			// 超过7天的任务
            if (daysSinceCreate > 7) {
                return true;
            }
        }

        return false;
    }

    /**
     * 处理异常任务数据
     * 将数据库中存在但API中不存在的任务标记为异常数据（isDeleted=3）
     *
     * @param abnormalTasks 异常任务列表
     */
    private void handleAbnormalTasks(List<FhFlightTask> abnormalTasks) {
        if (abnormalTasks == null || abnormalTasks.isEmpty()) {
            return;
        }

        log.info("开始处理{}个异常任务数据", abnormalTasks.size());

        try {
            for (FhFlightTask task : abnormalTasks) {
                // 将isDeleted字段设置为3，表示异常数据
                task.setIsDeleted(3);

                // 更新数据库
                int result = fhFlightTaskMapper.updateById(task);
                if (result > 0) {
                    log.info("成功将异常飞行任务[{}]标记为已删除状态(isDeleted=3)", task.getUuid());
                } else {
                    log.warn("标记异常飞行任务[{}]失败", task.getUuid());
                }
            }

            log.info("异常任务数据处理完成，共处理{}个任务", abnormalTasks.size());
        } catch (Exception e) {
            log.error("处理异常任务数据时发生错误", e);
        }
    }

    /**
     * 根据API响应更新任务对象并更新数据库
     * 将API返回的飞行任务信息中的字段值更新到本地任务对象中，并直接更新数据库
     *
     * @param task 本地任务对象
     * @param apiTask API返回的任务对象
     * @return 是否更新成功
     */
    private boolean updateTaskFromApiResponse(FhFlightTask task, FhFlightTaskVO apiTask) {
        try {
            // 更新基本字段
            task.setTaskStatus(apiTask.getTaskStatus());
            // 使用工具类移除任务名称中的时间后缀
            task.setName(FlightTaskNameUtil.removeTimeSuffix(apiTask.getName()));
            task.setTaskType(apiTask.getTaskType());
            task.setWaylineUuid(apiTask.getWaylineUuid());
            task.setLandingDockSn(apiTask.getLandingDockSn());
            task.setCurrentWaypointIndex(apiTask.getCurrentWaypointIndex());
            task.setTotalWaypoints(apiTask.getTotalWaypoints());
            task.setFolderId(apiTask.getFolderId());
            task.setMediaUploadStatus(apiTask.getMediaUploadStatus());
            task.setResumableStatus(apiTask.getResumableStatus());
            task.setIsBreakPointResume(apiTask.getIsBreakPointResume());

            // 更新时间字段，需要将Date转换为OffsetDateTime
            if (apiTask.getRunAt() != null) {
                task.setRunAt(apiTask.getRunAt().toInstant().atOffset(java.time.ZoneOffset.of("+08:00")));
            }

            if (apiTask.getCompletedAt() != null) {
                task.setCompletedAt(apiTask.getCompletedAt().toInstant().atOffset(java.time.ZoneOffset.of("+08:00")));
            }

            // 更新操作记录
            if (apiTask.getOperations() != null) {
                task.setOperatorAccount(apiTask.getOperations().getOperatorAccount());
            }

            // 更新异常信息
            if (apiTask.getExceptions() != null) {
                try {
                    // 安全地将Long转换为Integer，避免数值溢出异常
                    task.setExceptionCode(apiTask.getExceptions().getCode() <= Integer.MAX_VALUE ?
                        (int)apiTask.getExceptions().getCode() : 0);
                    task.setExceptionMessage(apiTask.getExceptions().getMessage());
                    task.setExceptionSn(apiTask.getExceptions().getSn());

                    if (apiTask.getExceptions().getHappenAt() != null && !apiTask.getExceptions().getHappenAt().isEmpty()) {
                        try {
                            // FTExceptions中的happenAt是String类型，需要解析为OffsetDateTime
                            task.setExceptionHappenAt(OffsetDateTime.parse(apiTask.getExceptions().getHappenAt()));
                        } catch (Exception e) {
                            log.warn("解析异常发生时间失败: {}", e.getMessage());
                        }
                    }
                } catch (Exception ex) {
                    log.warn("设置异常信息失败: {}", ex.getMessage());
                }
            }

            // 直接更新数据库
            int result = fhFlightTaskMapper.updateById(task);
            if (result > 0) {
                log.info("成功更新飞行任务[{}]的所有数据项到数据库", task.getUuid());
                return true;
            } else {
                log.warn("更新飞行任务[{}]到数据库失败，可能任务不存在", task.getUuid());
                return false;
            }
        } catch (Exception e) {
            log.error("更新飞行任务[{}]数据时发生异常: {}", task.getUuid(), e.getMessage(), e);
            return false;
        }
    }

}
