package org.springblade.modules.fh.component;

import jakarta.annotation.Resource;
import org.springblade.modules.fh.service.IFhConfigService;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
public class ApplicationBootInitial implements CommandLineRunner {

    @Resource
    private IFhConfigService configService;


    /**
     * Subscribe to the devices that exist in the redis when the program starts,
     * to prevent the data from being different from the pilot side due to program interruptions.
     * @param args
     * @throws Exception
     */
    @Override
    public void run(String... args) throws Exception {

        System.out.println("当程序启动时，订阅系统主题");

		configService.subscribeOrgTopic();

//        RedisOpsUtils.getAllKeys(RedisConst.DEVICE_ONLINE_PREFIX + "*")
//                .stream()
//                .map(key -> key.substring(start))
//                .map(deviceRedisService::getDeviceOnline)
//                .map(Optional::get)
//                .filter(device -> DeviceDomainEnum.DRONE != device.getDomain())
//                .forEach(device -> deviceService.subDeviceOnlineSubscribeTopic(
//                        SDKManager.registerDevice(device.getDeviceSn(), device.getChildDeviceSn(), device.getDomain(),
//                                device.getType(), device.getSubType(), device.getThingVersion(),
//                                deviceRedisService.getDeviceOnline(device.getChildDeviceSn()).map(DeviceDTO::getThingVersion).orElse(null))));

    }
}
