package org.springblade.modules.fh.component.scheduler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.fh.pojo.dto.FlightTaskDistanceUpdateResult;
import org.springblade.modules.fh.service.impl.FhFlightTaskServiceImpl;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 飞行任务距离定时更新
 * 定时查询飞行任务表中的数据，当任务状态为成功且飞行距离为空时，
 * 调用飞行任务轨迹查询的相关方法，把获取到对应的飞行距离数据更新到数据库中
 *
 * <AUTHOR> AI
 */
@Slf4j
@Component
@EnableScheduling
@RequiredArgsConstructor
public class FlightTaskDistanceScheduler {

    private final FhFlightTaskServiceImpl fhFlightTaskService;

    /**
     * 每5分钟执行一次，检查并更新飞行任务的飞行距离
     * cron表达式：秒 分 时 日 月 周
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void updateFlightTaskDistance() {
        log.info("开始执行飞行任务距离更新定时任务");

        try {
            // 调用服务层的公共方法执行更新逻辑
            FlightTaskDistanceUpdateResult result = 
                fhFlightTaskService.executeFlightTaskDistanceUpdate();

            log.info("飞行任务距离更新定时任务执行完成：{}", result.getResultDescription());
        } catch (Exception e) {
            log.error("执行飞行任务距离更新定时任务出错", e);
        }
    }
}
