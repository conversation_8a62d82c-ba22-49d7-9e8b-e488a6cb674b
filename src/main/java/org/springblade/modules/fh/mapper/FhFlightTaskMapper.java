package org.springblade.modules.fh.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.fh.pojo.entity.FhFlightTask;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 飞行任务数据访问层
 *
 * <AUTHOR> AI
 */
@Mapper
public interface FhFlightTaskMapper extends BaseMapper<FhFlightTask> {

    /**
     * 根据UUID查询飞行任务
     *
     * @param uuid 任务UUID
     * @return 飞行任务实体
     */
    FhFlightTask selectByUuid(@Param("uuid") String uuid);

    /**
     * 获取飞行统计数据（总飞行时长和总飞行里程）
     * 计算所有已完成任务的飞行时长总和和飞行里程总和
     *
     * @return 包含总飞行时长（小时）和总飞行里程（米）的Map
     */
    Map<String, BigDecimal> getTotalFlightStats();

    /**
     * 获取所有任务的年份列表（按年份降序排序）
     * 根据任务的设定开始时间（begin_at）统计
     *
     * @return 年份列表
     */
    List<Integer> selectTaskYears();
}
