package org.springblade.modules.fh.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springblade.modules.system.service.IParamService;

import java.util.concurrent.TimeUnit;

/**
 * Skii MinIO动态配置管理类
 * 实现三层获取策略：Redis -> Database -> YAML
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SkiiMinioConfig {

    @Autowired
    private IParamService paramService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    @Lazy
    private ApplicationProperties.SkiiMinio skiiMinioProperties;

    /**
     * 配置缓存键前缀
     */
    private static final String CONFIG_CACHE_PREFIX = "config:";
    
    /**
     * 缓存过期时间（秒）
     */
    private static final long CACHE_EXPIRE_TIME = 3600L;

    /**
     * 配置键常量
     */
    public static final String ENDPOINT_KEY = "skii.minio.endpoint";
    public static final String ACCESS_KEY_KEY = "skii.minio.accessKey";
    public static final String SECRET_KEY_KEY = "skii.minio.secretKey";
    public static final String BUCKET_KEY = "skii.minio.bucket";

    /**
     * 获取MinIO服务端点
     * @return endpoint
     */
    public String getEndpoint() {
        return getConfig(ENDPOINT_KEY, skiiMinioProperties.getEndpoint());
    }

    /**
     * 获取MinIO访问密钥
     * @return accessKey
     */
    public String getAccessKey() {
        return getConfig(ACCESS_KEY_KEY, skiiMinioProperties.getAccessKey());
    }

    /**
     * 获取MinIO秘密密钥
     * @return secretKey
     */
    public String getSecretKey() {
        return getConfig(SECRET_KEY_KEY, skiiMinioProperties.getSecretKey());
    }

    /**
     * 获取MinIO存储桶名称
     * @return bucket
     */
    public String getBucket() {
        return getConfig(BUCKET_KEY, skiiMinioProperties.getBucket());
    }

    /**
     * 获取配置值的核心方法
     * 实现三层获取策略：Redis -> Database -> YAML
     * 
     * @param paramKey 参数键
     * @param fallbackValue YAML降级值
     * @return 配置值
     */
    private String getConfig(String paramKey, String fallbackValue) {
        String cacheKey = CONFIG_CACHE_PREFIX + paramKey;
        
        try {
            // 第一层：从Redis缓存获取
            Object cachedValue = redisTemplate.opsForValue().get(cacheKey);
            if (cachedValue != null) {
                log.debug("[CONFIG] [INFO] 配置获取成功: key={}, source=redis", paramKey);
                return cachedValue.toString();
            }

            // 第二层：从数据库获取
            String dbValue = paramService.getValue(paramKey);
            if (dbValue != null && !dbValue.trim().isEmpty()) {
                // 更新Redis缓存
                redisTemplate.opsForValue().set(cacheKey, dbValue, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
                log.debug("[CONFIG] [INFO] 配置获取成功: key={}, source=database", paramKey);
                return dbValue;
            }

            // 第三层：从YAML配置获取（降级方案）
            if (fallbackValue != null && !fallbackValue.trim().isEmpty()) {
                log.warn("[CONFIG] [WARN] 数据库无配置，降级到YAML: key={}", paramKey);
                return fallbackValue;
            }

            log.error("[CONFIG] [ERROR] 所有配置源均无法获取配置: key={}", paramKey);
            throw new RuntimeException("Unable to get configuration for key: " + paramKey);

        } catch (Exception e) {
            // Redis异常时，直接尝试数据库
            if (e.getMessage() != null && e.getMessage().contains("Redis")) {
                log.warn("[CONFIG] [WARN] Redis不可用，降级到数据库: key={}", paramKey);
                try {
                    String dbValue = paramService.getValue(paramKey);
                    if (dbValue != null && !dbValue.trim().isEmpty()) {
                        log.debug("[CONFIG] [INFO] 配置获取成功: key={}, source=database_fallback", paramKey);
                        return dbValue;
                    }
                } catch (Exception dbException) {
                    log.error("[CONFIG] [ERROR] 数据库异常，降级到YAML: key={}, error={}", paramKey, dbException.getMessage());
                }
            }

            // 最终降级到YAML配置
            if (fallbackValue != null && !fallbackValue.trim().isEmpty()) {
                log.warn("[CONFIG] [WARN] 异常情况降级到YAML: key={}, error={}", paramKey, e.getMessage());
                return fallbackValue;
            }

            log.error("[CONFIG] [ERROR] 配置获取完全失败: key={}, error={}", paramKey, e.getMessage());
            throw new RuntimeException("Failed to get configuration for key: " + paramKey, e);
        }
    }

    /**
     * 刷新指定配置的缓存
     * @param paramKey 参数键
     */
    public void refreshCache(String paramKey) {
        String cacheKey = CONFIG_CACHE_PREFIX + paramKey;
        try {
            redisTemplate.delete(cacheKey);
            log.info("[CONFIG] [INFO] 缓存刷新成功: key={}", paramKey);
        } catch (Exception e) {
            log.warn("[CONFIG] [WARN] 缓存刷新失败: key={}, error={}", paramKey, e.getMessage());
        }
    }

    /**
     * 刷新所有MinIO配置缓存
     */
    public void refreshAllCache() {
        refreshCache(ENDPOINT_KEY);
        refreshCache(ACCESS_KEY_KEY);
        refreshCache(SECRET_KEY_KEY);
        refreshCache(BUCKET_KEY);
        log.info("[CONFIG] [INFO] 所有MinIO配置缓存刷新完成");
    }

    /**
     * 预热配置缓存
     */
    public void warmUpCache() {
        try {
            getEndpoint();
            getAccessKey();
            getSecretKey();
            getBucket();
            log.info("[CONFIG] [INFO] 配置缓存预热完成");
        } catch (Exception e) {
            log.warn("[CONFIG] [WARN] 配置缓存预热失败: error={}", e.getMessage());
        }
    }
}