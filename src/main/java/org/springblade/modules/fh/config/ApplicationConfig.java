package org.springblade.modules.fh.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springblade.modules.system.pojo.entity.Param;
import org.springblade.modules.system.service.IParamService;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 应用动态配置管理类
 * 实现三层获取策略：Redis -> Database -> YAML
 * 支持Skii MinIO、MQTT、FH-SDK、EJ-FH-SERVER等多种配置
 * 每个环境使用独立的数据库，配置键不包含环境后缀
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ApplicationConfig {

    @Autowired
    private IParamService paramService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    @Lazy
    private ApplicationProperties.SkiiMinio skiiMinioProperties;
    
    @Autowired
    @Lazy
    private ApplicationProperties.Mqtt mqttProperties;
    
    @Autowired
    @Lazy
    private ApplicationProperties.FhSdk fhSdkProperties;
    
    @Autowired
    @Lazy
    private ApplicationProperties.EjFhServer ejFhServerProperties;
    
    @Value("${spring.profiles.active:default}")
    private String activeProfile;

    /**
     * 配置缓存键前缀
     */
    private static final String CONFIG_CACHE_PREFIX = "config:";
    
    /**
     * 分布式锁键前缀
     */
    private static final String LOCK_PREFIX = "config:lock:";
    
    /**
     * 缓存过期时间（秒）
     */
    private static final long CACHE_EXPIRE_TIME = 3600L;
    
    /**
     * 分布式锁过期时间（秒）
     */
    private static final long LOCK_EXPIRE_TIME = 30L;
    
    /**
     * 本地内存缓存 - 一级缓存
     */
    private final ConcurrentHashMap<String, CacheEntry> localCache = new ConcurrentHashMap<>();
    
    /**
     * 本地缓存锁，防止并发重复加载
     */
    private final ConcurrentHashMap<String, ReentrantLock> localLocks = new ConcurrentHashMap<>();
    
    /**
     * 本地缓存过期时间（毫秒）- 比Redis缓存短
     */
    private static final long LOCAL_CACHE_EXPIRE_TIME = 300_000L; // 5分钟

    /**
     * Skii MinIO配置键常量
     */
    public static final String SKII_MINIO_ENDPOINT_KEY = "skii.minio.endpoint";
    public static final String SKII_MINIO_ACCESS_KEY_KEY = "skii.minio.accessKey";
    public static final String SKII_MINIO_SECRET_KEY_KEY = "skii.minio.secretKey";
    public static final String SKII_MINIO_BUCKET_KEY = "skii.minio.bucket";

    /**
     * MQTT BASIC配置键常量
     */
    public static final String MQTT_BASIC_PROTOCOL_KEY = "mqtt.basic.protocol";
    public static final String MQTT_BASIC_HOST_KEY = "mqtt.basic.host";
    public static final String MQTT_BASIC_PORT_KEY = "mqtt.basic.port";
    public static final String MQTT_BASIC_USERNAME_KEY = "mqtt.basic.username";
    public static final String MQTT_BASIC_PASSWORD_KEY = "mqtt.basic.password";
    public static final String MQTT_BASIC_CLIENT_ID_KEY = "mqtt.basic.clientId";
    public static final String MQTT_BASIC_PATH_KEY = "mqtt.basic.path";

    /**
     * MQTT DRC配置键常量
     */
    public static final String MQTT_DRC_PROTOCOL_KEY = "mqtt.drc.protocol";
    public static final String MQTT_DRC_HOST_KEY = "mqtt.drc.host";
    public static final String MQTT_DRC_PORT_KEY = "mqtt.drc.port";
    public static final String MQTT_DRC_PATH_KEY = "mqtt.drc.path";
    public static final String MQTT_DRC_USERNAME_KEY = "mqtt.drc.username";
    public static final String MQTT_DRC_PASSWORD_KEY = "mqtt.drc.password";

    /**
     * FH-SDK配置键常量
     */
    public static final String FH_SDK_MQTT_INBOUND_TOPIC_KEY = "fh.sdk.mqtt.inboundTopic";

    /**
     * EJ-FH-SERVER配置键常量
     */
    public static final String EJ_FH_SERVER_URL_KEY = "ej.fh.server.url";

    /**
     * 标注文件夹ID配置键常量
     */
    public static final String ANNOTATION_FOLDER_ID_KEY = "annotation.folder.id";

    // ==================== Skii MinIO配置获取方法 ====================

    /**
     * 获取MinIO服务端点
     * @return endpoint
     */
    public String getSkiiMinioEndpoint() {
        return getConfig(SKII_MINIO_ENDPOINT_KEY, skiiMinioProperties.getEndpoint());
    }

    /**
     * 获取MinIO访问密钥
     * @return accessKey
     */
    public String getSkiiMinioAccessKey() {
        return getConfig(SKII_MINIO_ACCESS_KEY_KEY, skiiMinioProperties.getAccessKey());
    }

    /**
     * 获取MinIO秘密密钥
     * @return secretKey
     */
    public String getSkiiMinioSecretKey() {
        return getConfig(SKII_MINIO_SECRET_KEY_KEY, skiiMinioProperties.getSecretKey());
    }

    /**
     * 获取MinIO存储桶名称
     * @return bucket
     */
    public String getSkiiMinioBucket() {
        return getConfig(SKII_MINIO_BUCKET_KEY, skiiMinioProperties.getBucket());
    }

    // ==================== MQTT BASIC配置获取方法 ====================

    /**
     * 获取MQTT BASIC协议
     * @return protocol
     */
    public String getMqttBasicProtocol() {
        return getConfig(MQTT_BASIC_PROTOCOL_KEY, 
            mqttProperties.getBasic() != null ? mqttProperties.getBasic().getProtocol() : null);
    }

    /**
     * 获取MQTT BASIC主机
     * @return host
     */
    public String getMqttBasicHost() {
        return getConfig(MQTT_BASIC_HOST_KEY, 
            mqttProperties.getBasic() != null ? mqttProperties.getBasic().getHost() : null);
    }

    /**
     * 获取MQTT BASIC端口
     * @return port
     */
    public String getMqttBasicPort() {
        return getConfig(MQTT_BASIC_PORT_KEY, 
            mqttProperties.getBasic() != null && mqttProperties.getBasic().getPort() != null 
                ? mqttProperties.getBasic().getPort().toString() : null);
    }

    /**
     * 获取MQTT BASIC用户名
     * @return username
     */
    public String getMqttBasicUsername() {
        return getConfig(MQTT_BASIC_USERNAME_KEY, 
            mqttProperties.getBasic() != null ? mqttProperties.getBasic().getUsername() : null);
    }

    /**
     * 获取MQTT BASIC密码
     * @return password
     */
    public String getMqttBasicPassword() {
        return getConfig(MQTT_BASIC_PASSWORD_KEY, 
            mqttProperties.getBasic() != null ? mqttProperties.getBasic().getPassword() : null);
    }

    /**
     * 获取MQTT BASIC客户端ID
     * @return clientId
     */
    public String getMqttBasicClientId() {
        return getConfig(MQTT_BASIC_CLIENT_ID_KEY, 
            mqttProperties.getBasic() != null ? mqttProperties.getBasic().getClientId() : null);
    }

    /**
     * 获取MQTT BASIC路径
     * @return path
     */
    public String getMqttBasicPath() {
        return getConfig(MQTT_BASIC_PATH_KEY, 
            mqttProperties.getBasic() != null ? mqttProperties.getBasic().getPath() : null);
    }

    // ==================== MQTT DRC配置获取方法 ====================

    /**
     * 获取MQTT DRC协议
     * @return protocol
     */
    public String getMqttDrcProtocol() {
        return getConfig(MQTT_DRC_PROTOCOL_KEY, 
            mqttProperties.getDrc() != null ? mqttProperties.getDrc().getProtocol() : null);
    }

    /**
     * 获取MQTT DRC主机
     * @return host
     */
    public String getMqttDrcHost() {
        return getConfig(MQTT_DRC_HOST_KEY, 
            mqttProperties.getDrc() != null ? mqttProperties.getDrc().getHost() : null);
    }

    /**
     * 获取MQTT DRC端口
     * @return port
     */
    public String getMqttDrcPort() {
        return getConfig(MQTT_DRC_PORT_KEY, 
            mqttProperties.getDrc() != null && mqttProperties.getDrc().getPort() != null 
                ? mqttProperties.getDrc().getPort().toString() : null);
    }

    /**
     * 获取MQTT DRC路径
     * @return path
     */
    public String getMqttDrcPath() {
        return getConfig(MQTT_DRC_PATH_KEY, 
            mqttProperties.getDrc() != null ? mqttProperties.getDrc().getPath() : null);
    }

    /**
     * 获取MQTT DRC用户名
     * @return username
     */
    public String getMqttDrcUsername() {
        return getConfig(MQTT_DRC_USERNAME_KEY, 
            mqttProperties.getDrc() != null ? mqttProperties.getDrc().getUsername() : null);
    }

    /**
     * 获取MQTT DRC密码
     * @return password
     */
    public String getMqttDrcPassword() {
        return getConfig(MQTT_DRC_PASSWORD_KEY, 
            mqttProperties.getDrc() != null ? mqttProperties.getDrc().getPassword() : null);
    }

    // ==================== FH-SDK配置获取方法 ====================

    /**
     * 获取FH-SDK MQTT入站主题
     * @return inboundTopic
     */
    public String getFhSdkMqttInboundTopic() {
        return getConfig(FH_SDK_MQTT_INBOUND_TOPIC_KEY, 
            fhSdkProperties.getMqtt() != null ? fhSdkProperties.getMqtt().getInboundTopic() : null);
    }

    // ==================== EJ-FH-SERVER配置获取方法 ====================

    /**
     * 获取EJ-FH-SERVER服务URL
     * @return url
     */
    public String getEjFhServerUrl() {
        return getConfig(EJ_FH_SERVER_URL_KEY, ejFhServerProperties.getUrl());
    }

    // ==================== 标注文件夹ID配置获取方法 ====================

    /**
     * 获取标注文件夹ID
     * @return 标注文件夹ID
     */
    public String getAnnotationFolderId() {
        return getConfig(ANNOTATION_FOLDER_ID_KEY, null);
    }

    // ==================== 缓存实体类 ====================

    /**
     * 本地缓存条目
     */
    private static class CacheEntry {
        private final String value;
        private final long expireTime;
        
        public CacheEntry(String value, long ttl) {
            this.value = value;
            this.expireTime = System.currentTimeMillis() + ttl;
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() > expireTime;
        }
        
        public String getValue() {
            return value;
        }
    }

    // ==================== 通用配置管理方法 ====================

    /**
     * 获取配置值的核心方法
     * 实现四层获取策略：本地内存缓存 -> Redis缓存 -> Database -> YAML
     * 
     * @param paramKey 参数键
     * @param fallbackValue YAML降级值
     * @return 配置值
     */
    private String getConfig(String paramKey, String fallbackValue) {
        // ==================== 第一层：本地内存缓存 ====================
        CacheEntry localEntry = localCache.get(paramKey);
        if (localEntry != null && !localEntry.isExpired()) {
            log.debug("[CONFIG] [{}环境] 配置获取成功: key={}, value={}, source=本地内存缓存", 
                activeProfile.toUpperCase(), paramKey, maskSensitiveConfig(paramKey, localEntry.getValue()));
            return localEntry.getValue();
        }

        // 获取本地锁，防止并发重复加载
        ReentrantLock lock = localLocks.computeIfAbsent(paramKey, k -> new ReentrantLock());
        
        try {
            lock.lock();
            
            // 双重检查，防止重复加载
            localEntry = localCache.get(paramKey);
            if (localEntry != null && !localEntry.isExpired()) {
                return localEntry.getValue();
            }
            
            String configValue = loadConfigFromRemote(paramKey, fallbackValue);
            
            // 更新本地缓存
            if (configValue != null) {
                localCache.put(paramKey, new CacheEntry(configValue, LOCAL_CACHE_EXPIRE_TIME));
            }
            
            return configValue;
            
        } finally {
            lock.unlock();
        }
    }

    /**
     * 从远程加载配置（Redis -> Database -> YAML）
     * 
     * @param paramKey 参数键
     * @param fallbackValue YAML降级值
     * @return 配置值
     */
    private String loadConfigFromRemote(String paramKey, String fallbackValue) {
        String cacheKey = CONFIG_CACHE_PREFIX + paramKey;
        String lockKey = LOCK_PREFIX + paramKey;
        String configSource = "";
        String configValue = null;
        
        try {
            // ==================== 第二层：Redis缓存 ====================
            Object cachedValue = redisTemplate.opsForValue().get(cacheKey);
            if (cachedValue != null) {
                configSource = "Redis缓存";
                configValue = cachedValue.toString();
                log.debug("[CONFIG] [{}环境] 配置获取成功: key={}, value={}, source={}", 
                    activeProfile.toUpperCase(), paramKey, maskSensitiveConfig(paramKey, configValue), configSource);
                return configValue;
            }

            // ==================== 分布式锁防止缓存穿透 ====================
            String lockValue = String.valueOf(System.currentTimeMillis());
            boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
            
            if (!lockAcquired) {
                // 获取锁失败，稍等后重试从Redis获取（可能其他实例已经加载）
                try {
                    Thread.sleep(50); // 等待50ms
                } catch (InterruptedException ignored) {
                    Thread.currentThread().interrupt();
                }
                
                cachedValue = redisTemplate.opsForValue().get(cacheKey);
                if (cachedValue != null) {
                    configSource = "Redis缓存(锁等待后)";
                    configValue = cachedValue.toString();
                    log.debug("[CONFIG] [{}环境] 配置获取成功: key={}, value={}, source={}", 
                        activeProfile.toUpperCase(), paramKey, maskSensitiveConfig(paramKey, configValue), configSource);
                    return configValue;
                }
            }

            try {
                // ==================== 第三层：数据库 ====================
                String dbValue = paramService.getValue(paramKey);
                if (dbValue != null && !dbValue.trim().isEmpty()) {
                    configSource = "数据库";
                    configValue = dbValue;
                    
                    // 写时更新缓存（而不是删除）
                    updateCache(paramKey, configValue);
                    
                    log.info("[CONFIG] [{}环境] 配置获取成功: key={}, value={}, source={}", 
                        activeProfile.toUpperCase(), paramKey, maskSensitiveConfig(paramKey, configValue), configSource);
                    return configValue;
                }

                // ==================== 第四层：YAML配置降级 ====================
                if (fallbackValue != null && !fallbackValue.trim().isEmpty()) {
                    configSource = "application-" + activeProfile + ".yml";
                    configValue = fallbackValue;
                    log.warn("[CONFIG] [{}环境] 数据库无配置，降级到YAML: key={}, value={}, source={}", 
                        activeProfile.toUpperCase(), paramKey, maskSensitiveConfig(paramKey, configValue), configSource);
                    
                    // 配置降级后自动同步到Redis和数据库
                    syncConfigToRedisAndDatabase(paramKey, configValue, "YAML降级自动同步");
                    
                    return configValue;
                }

                log.error("[CONFIG] [{}环境] 所有配置源均无法获取配置: key={}", activeProfile.toUpperCase(), paramKey);
                return null;

            } finally {
                // 释放分布式锁
                if (lockAcquired) {
                    releaseLock(lockKey, lockValue);
                }
            }

        } catch (Exception e) {
            // ==================== 异常处理 ====================
            log.error("[CONFIG] [{}环境] 配置获取异常: key={}, error={}", 
                activeProfile.toUpperCase(), paramKey, e.getMessage());

            // 尝试从数据库降级获取
            try {
                String dbValue = paramService.getValue(paramKey);
                if (dbValue != null && !dbValue.trim().isEmpty()) {
                    configSource = "数据库(异常降级)";
                    configValue = dbValue;
                    log.info("[CONFIG] [{}环境] 配置获取成功: key={}, value={}, source={}", 
                        activeProfile.toUpperCase(), paramKey, maskSensitiveConfig(paramKey, configValue), configSource);
                    return configValue;
                }
            } catch (Exception dbException) {
                log.error("[CONFIG] [{}环境] 数据库异常降级失败: key={}, error={}", 
                    activeProfile.toUpperCase(), paramKey, dbException.getMessage());
            }

            // 最终降级到YAML配置
            if (fallbackValue != null && !fallbackValue.trim().isEmpty()) {
                configSource = "application-" + activeProfile + ".yml(异常降级)";
                configValue = fallbackValue;
                log.warn("[CONFIG] [{}环境] 异常情况降级到YAML: key={}, value={}, source={}, error={}", 
                    activeProfile.toUpperCase(), paramKey, maskSensitiveConfig(paramKey, configValue), configSource, e.getMessage());
                
                // 异常降级后自动同步到Redis和数据库
                syncConfigToRedisAndDatabase(paramKey, configValue, "异常降级自动同步");
                
                return configValue;
            }

            log.error("[CONFIG] [{}环境] 配置获取完全失败: key={}, error={}", activeProfile.toUpperCase(), paramKey, e.getMessage());
            return null;
        }
    }

    /**
     * 脱敏处理敏感配置信息
     * @param paramKey 配置键
     * @param configValue 配置值
     * @return 脱敏后的配置值
     */
    private String maskSensitiveConfig(String paramKey, String configValue) {
        if (configValue == null || configValue.trim().isEmpty()) {
            return "****";
        }
        
        // 敏感配置字段脱敏
        String lowerKey = paramKey.toLowerCase();
        if (lowerKey.contains("password") || lowerKey.contains("secret") || 
            lowerKey.contains("accesskey") || lowerKey.contains("key")) {
            if (configValue.length() <= 4) {
                return "****";
            }
            return configValue.substring(0, 2) + "****" + configValue.substring(configValue.length() - 2);
        }
        
        return configValue;
    }

    /**
     * 更新缓存（写时更新而不是删除）
     * @param paramKey 参数键
     * @param paramValue 参数值
     */
    private void updateCache(String paramKey, String paramValue) {
        try {
            String cacheKey = CONFIG_CACHE_PREFIX + paramKey;
            
            // 更新Redis缓存
            redisTemplate.opsForValue().set(cacheKey, paramValue, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
            
            // 更新本地缓存
            localCache.put(paramKey, new CacheEntry(paramValue, LOCAL_CACHE_EXPIRE_TIME));
            
            log.debug("[CONFIG] [{}环境] 缓存更新成功: key={}", activeProfile.toUpperCase(), paramKey);
        } catch (Exception e) {
            log.error("[CONFIG] [{}环境] 缓存更新失败: key={}, error={}", 
                activeProfile.toUpperCase(), paramKey, e.getMessage());
        }
    }

    /**
     * 直接更新缓存（供外部调用）
     * @param paramKey 参数键
     * @param paramValue 参数值
     */
    public void updateCacheDirectly(String paramKey, String paramValue) {
        updateCache(paramKey, paramValue);
        log.info("[CONFIG] [{}环境] 配置缓存直接更新: key={}, value={}", 
            activeProfile.toUpperCase(), paramKey, maskSensitiveConfig(paramKey, paramValue));
    }

    /**
     * 刷新指定配置的缓存（删除本地和Redis缓存，强制下次从数据库重新加载）
     * @param paramKey 参数键
     */
    public void refreshCache(String paramKey) {
        try {
            String cacheKey = CONFIG_CACHE_PREFIX + paramKey;
            
            // 删除本地缓存
            localCache.remove(paramKey);
            
            // 删除Redis缓存
            redisTemplate.delete(cacheKey);
            
            log.info("[CONFIG] [{}环境] 缓存刷新成功: key={}", activeProfile.toUpperCase(), paramKey);
        } catch (Exception e) {
            log.warn("[CONFIG] [{}环境] 缓存刷新失败: key={}, error={}", 
                activeProfile.toUpperCase(), paramKey, e.getMessage());
        }
    }

    /**
     * 释放分布式锁
     * @param lockKey 锁键
     * @param lockValue 锁值
     */
    private void releaseLock(String lockKey, String lockValue) {
        try {
            // 使用Lua脚本确保原子性释放锁
            String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
            redisTemplate.execute((org.springframework.data.redis.core.script.RedisScript<Long>) 
                org.springframework.data.redis.core.script.RedisScript.of(script, Long.class), 
                java.util.Arrays.asList(lockKey), lockValue);
        } catch (Exception e) {
            log.warn("[CONFIG] [{}环境] 释放分布式锁失败: key={}, error={}", 
                activeProfile.toUpperCase(), lockKey, e.getMessage());
        }
    }

    /**
     * 刷新所有配置缓存
     */
    public void refreshAllCache() {
        // 清空本地缓存
        localCache.clear();
        
        // 刷新Skii MinIO配置缓存
        refreshCache(SKII_MINIO_ENDPOINT_KEY);
        refreshCache(SKII_MINIO_ACCESS_KEY_KEY);
        refreshCache(SKII_MINIO_SECRET_KEY_KEY);
        refreshCache(SKII_MINIO_BUCKET_KEY);
        
        // 刷新MQTT BASIC配置缓存
        refreshCache(MQTT_BASIC_PROTOCOL_KEY);
        refreshCache(MQTT_BASIC_HOST_KEY);
        refreshCache(MQTT_BASIC_PORT_KEY);
        refreshCache(MQTT_BASIC_USERNAME_KEY);
        refreshCache(MQTT_BASIC_PASSWORD_KEY);
        refreshCache(MQTT_BASIC_CLIENT_ID_KEY);
        refreshCache(MQTT_BASIC_PATH_KEY);
        
        // 刷新MQTT DRC配置缓存
        refreshCache(MQTT_DRC_PROTOCOL_KEY);
        refreshCache(MQTT_DRC_HOST_KEY);
        refreshCache(MQTT_DRC_PORT_KEY);
        refreshCache(MQTT_DRC_PATH_KEY);
        refreshCache(MQTT_DRC_USERNAME_KEY);
        refreshCache(MQTT_DRC_PASSWORD_KEY);
        
        // 刷新FH-SDK配置缓存
        refreshCache(FH_SDK_MQTT_INBOUND_TOPIC_KEY);
        
        // 刷新EJ-FH-SERVER配置缓存
        refreshCache(EJ_FH_SERVER_URL_KEY);
        
        log.info("[CONFIG] [{}环境] 所有应用配置缓存刷新完成", activeProfile.toUpperCase());
    }

    /**
     * 预热配置缓存
     */
    public void warmUpCache() {
        try {
            // 预热Skii MinIO配置
            getSkiiMinioEndpoint();
            getSkiiMinioAccessKey();
            getSkiiMinioSecretKey();
            getSkiiMinioBucket();
            
            // 预热MQTT配置
            getMqttBasicProtocol();
            getMqttBasicHost();
            getMqttBasicPort();
            getMqttBasicUsername();
            getMqttBasicPassword();
            getMqttBasicClientId();
            getMqttBasicPath();
            getMqttDrcProtocol();
            getMqttDrcHost();
            getMqttDrcPort();
            getMqttDrcPath();
            getMqttDrcUsername();
            getMqttDrcPassword();
            
            // 预热FH-SDK配置
            getFhSdkMqttInboundTopic();
            
            // 预热EJ-FH-SERVER配置
            getEjFhServerUrl();
            
            log.info("[CONFIG] [INFO] 应用配置缓存预热完成");
        } catch (Exception e) {
            log.warn("[CONFIG] [WARN] 应用配置缓存预热失败: error={}", e.getMessage());
        }
    }

    // ==================== 配置同步相关方法 ====================

    /**
     * 配置降级后自动同步到Redis和数据库
     * 使用异步方式，避免阻塞主流程
     * 
     * @param paramKey 参数键
     * @param paramValue 参数值
     * @param syncReason 同步原因
     */
    private void syncConfigToRedisAndDatabase(String paramKey, String paramValue, String syncReason) {
        // 异步执行同步操作，避免阻塞配置获取流程
        CompletableFuture.runAsync(() -> {
            try {
                log.info("[CONFIG] [{}环境] 开始配置同步: key={}, reason={}", activeProfile.toUpperCase(), paramKey, syncReason);
                
                boolean redisSuccess = syncToRedis(paramKey, paramValue);
                boolean dbSuccess = syncToDatabase(paramKey, paramValue, syncReason);
                
                if (redisSuccess && dbSuccess) {
                    log.info("[CONFIG] [{}环境] 配置同步成功: key={}, redis={}, database={}", 
                        activeProfile.toUpperCase(), paramKey, redisSuccess, dbSuccess);
                } else {
                    log.warn("[CONFIG] [{}环境] 配置同步部分失败: key={}, redis={}, database={}", 
                        activeProfile.toUpperCase(), paramKey, redisSuccess, dbSuccess);
                }
                
            } catch (Exception e) {
                log.error("[CONFIG] [{}环境] 配置同步异常: key={}, error={}", 
                    activeProfile.toUpperCase(), paramKey, e.getMessage(), e);
            }
        });
    }

    /**
     * 同步配置到Redis缓存
     * 
     * @param paramKey 参数键
     * @param paramValue 参数值
     * @return 是否成功
     */
    private boolean syncToRedis(String paramKey, String paramValue) {
        try {
            String cacheKey = CONFIG_CACHE_PREFIX + paramKey;
            redisTemplate.opsForValue().set(cacheKey, paramValue, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
            log.debug("[CONFIG] [{}环境] Redis同步成功: key={}", activeProfile.toUpperCase(), paramKey);
            return true;
        } catch (Exception e) {
            log.error("[CONFIG] [{}环境] Redis同步失败: key={}, error={}", 
                activeProfile.toUpperCase(), paramKey, e.getMessage());
            return false;
        }
    }

    /**
     * 同步配置到数据库
     * 
     * @param paramKey 参数键
     * @param paramValue 参数值
     * @param syncReason 同步原因
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean syncToDatabase(String paramKey, String paramValue, String syncReason) {
        try {
            // 查找现有配置
            java.util.List<Param> existingParams = paramService.list(
                com.baomidou.mybatisplus.core.toolkit.Wrappers.lambdaQuery(Param.class)
                    .eq(Param::getParamKey, paramKey)
                    .last("limit 1")
            );
            
            Param existingParam = existingParams.isEmpty() ? null : existingParams.get(0);

            if (existingParam != null) {
                // 更新现有配置
                existingParam.setParamValue(paramValue);
                existingParam.setRemark(existingParam.getRemark() + " (" + syncReason + ")");
                boolean updated = paramService.updateById(existingParam);
                
                if (updated) {
                    log.info("[CONFIG] [{}环境] 数据库更新成功: key={}, reason={}", 
                        activeProfile.toUpperCase(), paramKey, syncReason);
                    return true;
                } else {
                    log.error("[CONFIG] [{}环境] 数据库更新失败: key={}", activeProfile.toUpperCase(), paramKey);
                    return false;
                }
            } else {
                // 创建新配置
                Param newParam = new Param();
                newParam.setParamKey(paramKey);
                newParam.setParamValue(paramValue);
                newParam.setParamName(generateParamName(paramKey));
                newParam.setRemark(generateParamName(paramKey) + "配置 (" + syncReason + ")");
                newParam.setStatus(1);
                
                boolean saved = paramService.save(newParam);
                if (saved) {
                    log.info("[CONFIG] [{}环境] 数据库创建成功: key={}, reason={}", 
                        activeProfile.toUpperCase(), paramKey, syncReason);
                    return true;
                } else {
                    log.error("[CONFIG] [{}环境] 数据库创建失败: key={}", activeProfile.toUpperCase(), paramKey);
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("[CONFIG] [{}环境] 数据库同步异常: key={}, error={}", 
                activeProfile.toUpperCase(), paramKey, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据配置键生成友好的参数名称
     * 
     * @param paramKey 参数键
     * @return 参数名称
     */
    private String generateParamName(String paramKey) {
        switch (paramKey) {
            case SKII_MINIO_ENDPOINT_KEY: return "Skii MinIO服务端点";
            case SKII_MINIO_ACCESS_KEY_KEY: return "Skii MinIO访问密钥";
            case SKII_MINIO_SECRET_KEY_KEY: return "Skii MinIO秘密密钥";
            case SKII_MINIO_BUCKET_KEY: return "Skii MinIO存储桶名称";
            case MQTT_BASIC_PROTOCOL_KEY: return "MQTT BASIC协议";
            case MQTT_BASIC_HOST_KEY: return "MQTT BASIC主机";
            case MQTT_BASIC_PORT_KEY: return "MQTT BASIC端口";
            case MQTT_BASIC_USERNAME_KEY: return "MQTT BASIC用户名";
            case MQTT_BASIC_PASSWORD_KEY: return "MQTT BASIC密码";
            case MQTT_BASIC_CLIENT_ID_KEY: return "MQTT BASIC客户端ID";
            case MQTT_BASIC_PATH_KEY: return "MQTT BASIC路径";
            case MQTT_DRC_PROTOCOL_KEY: return "MQTT DRC协议";
            case MQTT_DRC_HOST_KEY: return "MQTT DRC主机";
            case MQTT_DRC_PORT_KEY: return "MQTT DRC端口";
            case MQTT_DRC_PATH_KEY: return "MQTT DRC路径";
            case MQTT_DRC_USERNAME_KEY: return "MQTT DRC用户名";
            case MQTT_DRC_PASSWORD_KEY: return "MQTT DRC密码";
            case FH_SDK_MQTT_INBOUND_TOPIC_KEY: return "FH-SDK MQTT入站主题";
            case EJ_FH_SERVER_URL_KEY: return "EJ-FH-SERVER URL";
            default: return paramKey;
        }
    }
}