package org.springblade.modules.fh.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 应用配置属性类
 * 用于从application.yml读取所有动态配置作为降级方案
 * 支持多环境配置（dev、test、prod）
 *
 * <AUTHOR>
 */
@Data
@Component
public class ApplicationProperties {

    /**
     * Skii MinIO配置
     */
    @Data
    @Component
    @ConfigurationProperties(prefix = "skii-minio")
    public static class SkiiMinio {
        private String endpoint;
        private String accessKey;
        private String secretKey;
        private String bucket;
    }

    /**
     * MQTT配置
     */
    @Data
    @Component
    @ConfigurationProperties(prefix = "mqtt")
    public static class Mqtt {
        private Basic basic;
        private Drc drc;

        @Data
        public static class Basic {
            private String protocol;
            private String host;
            private Integer port;
            private String username;
            private String password;
            private String clientId;
            private String path;
        }

        @Data
        public static class Drc {
            private String protocol;
            private String host;
            private Integer port;
            private String path;
            private String username;
            private String password;
        }
    }

    /**
     * FH-SDK配置
     */
    @Data
    @Component
    @ConfigurationProperties(prefix = "fh-sdk")
    public static class FhSdk {
        private MqttConfig mqtt;

        @Data
        public static class MqttConfig {
            private String inboundTopic;
        }
    }

    /**
     * EJ-FH-SERVER配置
     */
    @Data
    @Component
    @ConfigurationProperties(prefix = "ej-fh-server")
    public static class EjFhServer {
        private String url;
    }
}
