package org.springblade.modules.fh.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springblade.modules.fh.service.ISkiiMinioConfigService;

/**
 * Skii MinIO配置初始化器
 * 应用启动后自动预热配置缓存和验证配置有效性
 *
 * <AUTHOR>
@Slf4j
@Component
@Order(100) // 设置较低优先级，确保其他组件已初始化
@RequiredArgsConstructor
public class SkiiMinioConfigInitializer implements ApplicationRunner {

    private final ISkiiMinioConfigService skiiMinioConfigService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("[CONFIG] [INFO] Skii MinIO配置初始化开始...");

        try {
            // 预热配置缓存
            log.info("[CONFIG] [INFO] 开始预热Skii MinIO配置缓存...");
            skiiMinioConfigService.warmUpCache();

            // 验证配置完整性
            validateConfigCompleteness();

            // 验证连通性（可选，避免启动时阻塞）
            validateConnectionAsync();

            log.info("[CONFIG] [INFO] Skii MinIO配置初始化完成");

        } catch (Exception e) {
            log.error("[CONFIG] [ERROR] Skii MinIO配置初始化失败: {}", e.getMessage(), e);
            // 不抛出异常，避免阻止应用启动
        }
    }

    /**
     * 验证配置完整性
     */
    private void validateConfigCompleteness() {
        try {
            String endpoint = skiiMinioConfigService.getEndpoint();
            String accessKey = skiiMinioConfigService.getAccessKey();
            String secretKey = skiiMinioConfigService.getSecretKey();
            String bucket = skiiMinioConfigService.getBucket();

            boolean isComplete = true;
            StringBuilder missingConfigs = new StringBuilder();

            if (endpoint == null || endpoint.trim().isEmpty()) {
                isComplete = false;
                missingConfigs.append("endpoint ");
            }
            if (accessKey == null || accessKey.trim().isEmpty()) {
                isComplete = false;
                missingConfigs.append("accessKey ");
            }
            if (secretKey == null || secretKey.trim().isEmpty()) {
                isComplete = false;
                missingConfigs.append("secretKey ");
            }
            if (bucket == null || bucket.trim().isEmpty()) {
                isComplete = false;
                missingConfigs.append("bucket ");
            }

            if (isComplete) {
                log.info("[CONFIG] [INFO] Skii MinIO配置完整性验证通过");
                log.debug("[CONFIG] [DEBUG] 配置信息: endpoint={}, accessKey={}, bucket={}",
                    endpoint, maskSensitiveInfo(accessKey), bucket);
            } else {
                log.warn("[CONFIG] [WARN] Skii MinIO配置不完整，缺少配置项: {}", missingConfigs.toString().trim());
            }

        } catch (Exception e) {
            log.error("[CONFIG] [ERROR] 配置完整性验证失败: {}", e.getMessage());
        }
    }

    /**
     * 异步验证连通性，避免阻塞启动过程
     */
    private void validateConnectionAsync() {
        // 使用新线程异步验证，避免阻塞启动
        Thread validationThread = new Thread(() -> {
            try {
                // 延迟5秒执行，确保应用完全启动
                Thread.sleep(5000);

                boolean isConnected = skiiMinioConfigService.validateConnection();
                if (isConnected) {
                    log.info("[CONFIG] [INFO] Skii MinIO连通性验证成功");
                } else {
                    log.warn("[CONFIG] [WARN] Skii MinIO连通性验证失败，请检查配置和网络连接");
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("[CONFIG] [WARN] Skii MinIO连通性验证被中断");
            } catch (Exception e) {
                log.error("[CONFIG] [ERROR] Skii MinIO连通性验证异常: {}", e.getMessage());
            }
        });

        validationThread.setName("SkiiMinioConfigValidator");
		// 设置为守护线程
        validationThread.setDaemon(true);
        validationThread.start();
    }

    /**
     * 脱敏处理敏感信息
     * @param sensitiveInfo 敏感信息
     * @return 脱敏后的信息
     */
    private String maskSensitiveInfo(String sensitiveInfo) {
        if (sensitiveInfo == null || sensitiveInfo.length() <= 4) {
            return "****";
        }
        return sensitiveInfo.substring(0, 2) + "****" + sensitiveInfo.substring(sensitiveInfo.length() - 2);
    }
}
