package org.springblade.modules.fh.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springblade.modules.fh.service.IApplicationConfigService;

/**
 * 应用配置初始化器
 * 应用启动后自动预热配置缓存和验证配置有效性
 * 支持Skii MinIO、MQTT、FH-SDK、EJ-FH-SERVER等多种配置
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@Order(100) // 设置较低优先级，确保其他组件已初始化
public class ApplicationConfigInitializer implements ApplicationRunner {

    @Autowired
    private IApplicationConfigService applicationConfigService;
    
    @Value("${spring.profiles.active:default}")
    private String activeProfile;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("[CONFIG] [INFO] 应用配置初始化开始...");
        
        try {
            // 预热配置缓存
            log.info("[CONFIG] [INFO] 开始预热应用配置缓存...");
            applicationConfigService.warmUpCache();
            
            // 验证配置完整性
            validateConfigCompleteness();
            
            // 验证MinIO连通性（可选，避免启动时阻塞）
            validateMinioConnectionAsync();
            
            log.info("[CONFIG] [INFO] 应用配置初始化完成");
            
        } catch (Exception e) {
            log.error("[CONFIG] [ERROR] 应用配置初始化失败: {}", e.getMessage(), e);
            // 不抛出异常，避免阻止应用启动
        }
    }

    /**
     * 验证配置完整性并详细打印所有配置信息
     */
    private void validateConfigCompleteness() {
        try {
            log.info("==================== [{}环境] 应用配置详情 ====================", activeProfile.toUpperCase());
            
            StringBuilder missingConfigs = new StringBuilder();
            int totalConfigs = 0;
            int validConfigs = 0;

            // ==================== Skii MinIO配置详情 ====================
            log.info("【Skii MinIO配置】");
            
            String endpoint = applicationConfigService.getSkiiMinioEndpoint();
            totalConfigs++;
            if (endpoint != null && !endpoint.trim().isEmpty()) {
                validConfigs++;
                log.info("  ✓ endpoint: {}", endpoint);
            } else {
                missingConfigs.append("skii.minio.endpoint ");
                log.warn("  ✗ endpoint: 未配置");
            }
            
            String accessKey = applicationConfigService.getSkiiMinioAccessKey();
            totalConfigs++;
            if (accessKey != null && !accessKey.trim().isEmpty()) {
                validConfigs++;
                log.info("  ✓ accessKey: {}", maskSensitiveInfo(accessKey));
            } else {
                missingConfigs.append("skii.minio.accessKey ");
                log.warn("  ✗ accessKey: 未配置");
            }
            
            String secretKey = applicationConfigService.getSkiiMinioSecretKey();
            totalConfigs++;
            if (secretKey != null && !secretKey.trim().isEmpty()) {
                validConfigs++;
                log.info("  ✓ secretKey: {}", maskSensitiveInfo(secretKey));
            } else {
                missingConfigs.append("skii.minio.secretKey ");
                log.warn("  ✗ secretKey: 未配置");
            }
            
            String bucket = applicationConfigService.getSkiiMinioBucket();
            totalConfigs++;
            if (bucket != null && !bucket.trim().isEmpty()) {
                validConfigs++;
                log.info("  ✓ bucket: {}", bucket);
            } else {
                missingConfigs.append("skii.minio.bucket ");
                log.warn("  ✗ bucket: 未配置");
            }

            // ==================== MQTT BASIC配置详情 ====================
            log.info("【MQTT BASIC配置】");
            
            String mqttBasicProtocol = applicationConfigService.getMqttBasicProtocol();
            totalConfigs++;
            if (mqttBasicProtocol != null && !mqttBasicProtocol.trim().isEmpty()) {
                validConfigs++;
                log.info("  ✓ protocol: {}", mqttBasicProtocol);
            } else {
                log.warn("  ✗ protocol: 未配置");
            }
            
            String mqttBasicHost = applicationConfigService.getMqttBasicHost();
            totalConfigs++;
            if (mqttBasicHost != null && !mqttBasicHost.trim().isEmpty()) {
                validConfigs++;
                log.info("  ✓ host: {}", mqttBasicHost);
            } else {
                missingConfigs.append("mqtt.basic.host ");
                log.warn("  ✗ host: 未配置");
            }
            
            String mqttBasicPort = applicationConfigService.getMqttBasicPort();
            totalConfigs++;
            if (mqttBasicPort != null && !mqttBasicPort.trim().isEmpty()) {
                validConfigs++;
                log.info("  ✓ port: {}", mqttBasicPort);
            } else {
                missingConfigs.append("mqtt.basic.port ");
                log.warn("  ✗ port: 未配置");
            }
            
            String mqttBasicUsername = applicationConfigService.getMqttBasicUsername();
            totalConfigs++;
            if (mqttBasicUsername != null && !mqttBasicUsername.trim().isEmpty()) {
                validConfigs++;
                log.info("  ✓ username: {}", mqttBasicUsername);
            } else {
                log.warn("  ✗ username: 未配置");
            }
            
            String mqttBasicPassword = applicationConfigService.getMqttBasicPassword();
            totalConfigs++;
            if (mqttBasicPassword != null && !mqttBasicPassword.trim().isEmpty()) {
                validConfigs++;
                log.info("  ✓ password: {}", maskSensitiveInfo(mqttBasicPassword));
            } else {
                log.warn("  ✗ password: 未配置");
            }
            
            String mqttBasicClientId = applicationConfigService.getMqttBasicClientId();
            totalConfigs++;
            if (mqttBasicClientId != null && !mqttBasicClientId.trim().isEmpty()) {
                validConfigs++;
                log.info("  ✓ clientId: {}", mqttBasicClientId);
            } else {
                log.warn("  ✗ clientId: 未配置");
            }
            
            String mqttBasicPath = applicationConfigService.getMqttBasicPath();
            totalConfigs++;
            if (mqttBasicPath != null && !mqttBasicPath.trim().isEmpty()) {
                validConfigs++;
                log.info("  ✓ path: {}", mqttBasicPath);
            } else {
                log.info("  ○ path: 空值（可选配置）");
            }

            // ==================== MQTT DRC配置详情 ====================
            log.info("【MQTT DRC配置】");
            
            String mqttDrcProtocol = applicationConfigService.getMqttDrcProtocol();
            totalConfigs++;
            if (mqttDrcProtocol != null && !mqttDrcProtocol.trim().isEmpty()) {
                validConfigs++;
                log.info("  ✓ protocol: {}", mqttDrcProtocol);
            } else {
                log.warn("  ✗ protocol: 未配置");
            }
            
            String mqttDrcHost = applicationConfigService.getMqttDrcHost();
            totalConfigs++;
            if (mqttDrcHost != null && !mqttDrcHost.trim().isEmpty()) {
                validConfigs++;
                log.info("  ✓ host: {}", mqttDrcHost);
            } else {
                missingConfigs.append("mqtt.drc.host ");
                log.warn("  ✗ host: 未配置");
            }
            
            String mqttDrcPort = applicationConfigService.getMqttDrcPort();
            totalConfigs++;
            if (mqttDrcPort != null && !mqttDrcPort.trim().isEmpty()) {
                validConfigs++;
                log.info("  ✓ port: {}", mqttDrcPort);
            } else {
                missingConfigs.append("mqtt.drc.port ");
                log.warn("  ✗ port: 未配置");
            }
            
            String mqttDrcPath = applicationConfigService.getMqttDrcPath();
            totalConfigs++;
            if (mqttDrcPath != null && !mqttDrcPath.trim().isEmpty()) {
                validConfigs++;
                log.info("  ✓ path: {}", mqttDrcPath);
            } else {
                log.warn("  ✗ path: 未配置");
            }
            
            String mqttDrcUsername = applicationConfigService.getMqttDrcUsername();
            totalConfigs++;
            if (mqttDrcUsername != null && !mqttDrcUsername.trim().isEmpty()) {
                validConfigs++;
                log.info("  ✓ username: {}", mqttDrcUsername);
            } else {
                log.warn("  ✗ username: 未配置");
            }
            
            String mqttDrcPassword = applicationConfigService.getMqttDrcPassword();
            totalConfigs++;
            if (mqttDrcPassword != null && !mqttDrcPassword.trim().isEmpty()) {
                validConfigs++;
                log.info("  ✓ password: {}", maskSensitiveInfo(mqttDrcPassword));
            } else {
                log.warn("  ✗ password: 未配置");
            }

            // ==================== FH-SDK配置详情 ====================
            log.info("【FH-SDK配置】");
            
            String fhSdkInboundTopic = applicationConfigService.getFhSdkMqttInboundTopic();
            totalConfigs++;
            if (fhSdkInboundTopic != null && !fhSdkInboundTopic.trim().isEmpty()) {
                validConfigs++;
                log.info("  ✓ mqtt.inboundTopic: {}", fhSdkInboundTopic);
            } else {
                missingConfigs.append("fh.sdk.mqtt.inboundTopic ");
                log.warn("  ✗ mqtt.inboundTopic: 未配置");
            }

            // ==================== EJ-FH-SERVER配置详情 ====================
            log.info("【EJ-FH-SERVER配置】");
            
            String ejFhServerUrl = applicationConfigService.getEjFhServerUrl();
            totalConfigs++;
            if (ejFhServerUrl != null && !ejFhServerUrl.trim().isEmpty()) {
                validConfigs++;
                log.info("  ✓ url: {}", ejFhServerUrl);
            } else {
                missingConfigs.append("ej.fh.server.url ");
                log.warn("  ✗ url: 未配置");
            }

            // ==================== 配置总结 ====================
            log.info("==================== 配置加载总结 ====================");
            log.info("环境: {}", activeProfile.toUpperCase());
            log.info("配置完整性: {}/{} ({}%)", validConfigs, totalConfigs, 
                String.format("%.1f", (double) validConfigs / totalConfigs * 100));
            
            if (missingConfigs.length() > 0) {
                log.warn("缺失的关键配置项: {}", missingConfigs.toString().trim());
                log.warn("请检查数据库参数表或application-{}.yml文件", activeProfile);
                log.info("💡 配置降级策略: Redis缓存 → 数据库 → YAML配置文件");
                log.info("💡 YAML配置降级后将自动同步到Redis缓存和数据库");
            } else {
                log.info("✓ 所有关键配置项均已正确加载");
            }
            
            log.info("📋 配置获取策略: Redis缓存 → 数据库 → YAML降级");
            log.info("🔄 自动同步机制: YAML降级时将自动同步到Redis和数据库");
            log.info("======================================================");
            
        } catch (Exception e) {
            log.error("[CONFIG] [ERROR] 配置完整性验证失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 异步验证MinIO连通性，避免阻塞启动过程
     */
    private void validateMinioConnectionAsync() {
        // 使用新线程异步验证，避免阻塞启动
        Thread validationThread = new Thread(() -> {
            try {
                // 延迟5秒执行，确保应用完全启动
                Thread.sleep(5000);
                
                // 获取当前MinIO配置进行验证
                String endpoint = applicationConfigService.getSkiiMinioEndpoint();
                String accessKey = applicationConfigService.getSkiiMinioAccessKey();
                String secretKey = applicationConfigService.getSkiiMinioSecretKey();
                String bucket = applicationConfigService.getSkiiMinioBucket();
                
                var validationResult = applicationConfigService.validateMinioConfig(endpoint, accessKey, secretKey, bucket);
                if (validationResult.isValid()) {
                    log.info("[CONFIG] [INFO] MinIO连通性验证成功");
                } else {
                    log.warn("[CONFIG] [WARN] MinIO连通性验证失败，请检查配置和网络连接: {}", validationResult.getFailureReason());
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("[CONFIG] [WARN] MinIO连通性验证被中断");
            } catch (Exception e) {
                log.error("[CONFIG] [ERROR] MinIO连通性验证异常: {}", e.getMessage());
            }
        });
        
        validationThread.setName("ApplicationConfigValidator");
        validationThread.setDaemon(true); // 设置为守护线程
        validationThread.start();
    }

    /**
     * 脱敏处理敏感信息
     * @param sensitiveInfo 敏感信息
     * @return 脱敏后的信息
     */
    private String maskSensitiveInfo(String sensitiveInfo) {
        if (sensitiveInfo == null || sensitiveInfo.length() <= 4) {
            return "****";
        }
        return sensitiveInfo.substring(0, 2) + "****" + sensitiveInfo.substring(sensitiveInfo.length() - 2);
    }
}