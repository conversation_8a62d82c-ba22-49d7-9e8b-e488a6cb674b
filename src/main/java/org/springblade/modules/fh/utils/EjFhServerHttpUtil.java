package org.springblade.modules.fh.utils;


import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.ParamCache;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.modules.system.pojo.FhParamEnum;
import org.springblade.modules.system.pojo.entity.Param;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

@Component
@Slf4j
public class EjFhServerHttpUtil {
	public static final String X_ORGANIZATION_KEY = "X-Organization-Key";

	public static final String X_ORGANIZATION_URL = "X-Organization-Url";

	private static String host;
	@Value("${ej-fh-server.url}")
	private void setHost(String host) { EjFhServerHttpUtil.host = host;}

    private EjFhServerHttpUtil(){}

	public static UrlBuilder urlBuilderCreate(){
		return UrlBuilder.of(host);
	}


	public static String getFhUrl(){
		return Optional.ofNullable(ParamCache.getByKey(FhParamEnum.DJI_FH2_URL.getKey()))
			.map(Param::getParamValue)
			.filter(StrUtil::isNotBlank)
			.orElseThrow(() -> new ServiceException("未配置司空地址"));
	}

	public static String getFhToken(){
		return Optional.ofNullable(ParamCache.getByKey(FhParamEnum.DJI_FH2_ORG_KEY.getKey()))
			.map(Param::getParamValue)
			.filter(StrUtil::isNotBlank)
			.orElseThrow(() -> new ServiceException("未配置司空密钥"));
	}
	public static String getFhOrgUuid(){
		return Optional.ofNullable(ParamCache.getByKey(FhParamEnum.DJI_FH2_ORG_UUID.getKey()))
			.map(Param::getParamValue)
			.filter(StrUtil::isNotBlank)
			.orElseThrow(() -> new ServiceException("未配置司空组织uuid"));
	}

	public static String getFhPrjUuid(){
		return Optional.ofNullable(ParamCache.getByKey(FhParamEnum.DJI_FH2_PRJ_UUID.getKey()))
			.map(Param::getParamValue)
			.filter(StrUtil::isNotBlank)
			.orElseThrow(() -> new ServiceException("未配置司空项目uuid"));
	}

	public static String post(String hostUrl,String body){
		return HttpRequest.post(hostUrl)
			.header(X_ORGANIZATION_KEY, getFhToken())
			.header(X_ORGANIZATION_URL,getFhUrl())
			.body(body)
			.timeout(60000)
			.execute().body();
	}

	public static String get(String hostUrl){
		return HttpRequest.get(hostUrl)
			.header(X_ORGANIZATION_KEY, getFhToken())
			.header(X_ORGANIZATION_URL,getFhUrl())
			.timeout(60000)
			.execute().body();
	}

    public static JSONObject resStrToJson(String response){
        if(StrUtil.isEmpty(response)){
            throw new RuntimeException("响应数据异常");
        }

        log.info("http响应数据 --> " + response);

        try {
            JSONObject obj = JSONUtil.parseObj(response);

            Integer code = obj.getInt("code");

            if (!Objects.equals(0,code)){
                throw new RuntimeException(obj.getStr("message"));
            }

            return obj;
        } catch (RuntimeException r){
            throw new RuntimeException(r.getMessage());
        }catch (Exception e){
            throw new RuntimeException("响应数据转换异常");
        }
    }

    public static JSONObject resData(String response){
        JSONObject obj = resStrToJson(response);
        return obj.getJSONObject("data");
    }
}
