package org.springblade.modules.fh.utils.minio;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springblade.modules.fh.service.IApplicationConfigService;

/**
 * FH MinIO配置类
 * 通过应用动态配置服务获取MinIO配置信息
 * 替代原有的硬编码配置方式
 * 
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class FhMinioConfiguration {

    private final IApplicationConfigService applicationConfigService;

    /**
     * 获取MinIO服务端点
     * @return endpoint
     */
    public String getEndpoint() {
        return applicationConfigService.getSkiiMinioEndpoint();
    }

    /**
     * 获取MinIO访问密钥
     * @return accessKey
     */
    public String getAccessKey() {
        return applicationConfigService.getSkiiMinioAccessKey();
    }

    /**
     * 获取MinIO秘密密钥
     * @return secretKey
     */
    public String getSecretKey() {
        return applicationConfigService.getSkiiMinioSecretKey();
    }

    /**
     * 获取MinIO存储桶名称
     * @return bucket
     */
    public String getBucket() {
        return applicationConfigService.getSkiiMinioBucket();
    }

    /**
     * MinIO服务端点静态字段（为保持向后兼容）
     */
    public static String endpoint;
    
    /**
     * MinIO访问密钥静态字段（为保持向后兼容）
     */
    public static String accessKey;
    
    /**
     * MinIO秘密密钥静态字段（为保持向后兼容）
     */
    public static String secretKey;
    
    /**
     * MinIO存储桶名称静态字段（为保持向后兼容）
     */
    public static String bucket;

    /**
     * 更新静态字段（为保持向后兼容）
     * 注意：这些静态字段仅用于兼容现有代码，建议使用实例方法
     */
    public void updateStaticFields() {
        try {
            endpoint = getEndpoint();
            accessKey = getAccessKey();
            secretKey = getSecretKey();
            bucket = getBucket();
        } catch (Exception e) {
            // 如果动态配置获取失败，保持原有值不变
        }
    }
}



