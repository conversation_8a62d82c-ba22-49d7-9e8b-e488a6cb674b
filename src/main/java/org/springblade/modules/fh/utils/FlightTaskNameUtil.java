package org.springblade.modules.fh.utils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.regex.Pattern;

/**
 * 飞行任务名称处理工具类
 * 用于统一处理任务名称中的时间后缀
 *
 * <AUTHOR> AI
 */
@Slf4j
public class FlightTaskNameUtil {

    /**
     * 时间后缀正则表达式模式
     * 匹配格式：-yyyy-MM-dd (例如：-2024-01-15)
     */
    private static final Pattern TIME_SUFFIX_PATTERN = Pattern.compile("-\\d{4}-\\d{2}-\\d{2}$");

    /**
     * 移除任务名称中的时间后缀
     * 
     * @param taskName 原始任务名称
     * @return 移除时间后缀后的任务名称
     */
    public static String removeTimeSuffix(String taskName) {
        if (StrUtil.isBlank(taskName)) {
            return taskName;
        }

        try {
            // 使用正则表达式匹配并移除时间后缀
            String cleanedName = TIME_SUFFIX_PATTERN.matcher(taskName).replaceFirst("");
            
            // 如果移除了后缀，记录日志
            if (!taskName.equals(cleanedName)) {
                log.debug("移除任务名称时间后缀：{} -> {}", taskName, cleanedName);
            }
            
            return cleanedName;
        } catch (Exception e) {
            log.warn("处理任务名称时间后缀失败，返回原始名称：{}", taskName, e);
            return taskName;
        }
    }

    /**
     * 检查任务名称是否包含时间后缀
     * 
     * @param taskName 任务名称
     * @return 如果包含时间后缀返回true，否则返回false
     */
    public static boolean hasTimeSuffix(String taskName) {
        if (StrUtil.isBlank(taskName)) {
            return false;
        }
        
        return TIME_SUFFIX_PATTERN.matcher(taskName).find();
    }

    /**
     * 批量处理任务名称，移除时间后缀
     * 
     * @param taskNames 任务名称数组
     * @return 处理后的任务名称数组
     */
    public static String[] removeTimeSuffixBatch(String[] taskNames) {
        if (taskNames == null || taskNames.length == 0) {
            return taskNames;
        }
        
        String[] result = new String[taskNames.length];
        for (int i = 0; i < taskNames.length; i++) {
            result[i] = removeTimeSuffix(taskNames[i]);
        }
        
        return result;
    }
}