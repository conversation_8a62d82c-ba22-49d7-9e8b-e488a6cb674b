package org.springblade.modules.fh.utils;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.FhOpenapiPathConstant;
import org.springblade.modules.fh.pojo.dto.FhFlightTaskRequestByTimeBaseDTO;
import org.springblade.modules.fh.pojo.entity.FTExceptions;
import org.springblade.modules.fh.pojo.entity.FTOperations;
import org.springblade.modules.fh.pojo.vo.FhFlightTaskVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.Date;

/**
 * 飞行任务OpenAPI工具类
 * 提供飞行任务查询的通用方法，包括时间范围优化查询
 *
 * <AUTHOR> AI
 */
@Slf4j
@Component
public class FlightTaskOpenApiUtil {

    /**
     * 最大单次查询天数（可配置）
     */
    @Value("${flight.task.query.max.days:7}")
    private int maxQueryDays;

    /**
     * 查询时间范围扩展小时数（可配置）
     */
    @Value("${flight.task.query.time.extend.hours:24}")
    private int queryTimeExtendHours;

    // 移除FhOpenApiHttpUtil的构造函数注入，因为它是工具类，应该使用静态方法

    /**
     * 带时间范围优化的飞行任务列表查询
     * 根据时间范围自动选择单次查询或分段查询策略
     *
     * @param deviceSn 设备SN
     * @param minBeginAt 最小开始时间
     * @param maxBeginAt 最大开始时间
     * @return 飞行任务列表
     */
    public List<FhFlightTaskVO> getFlightTaskListWithTimeRangeOptimization(String deviceSn,
                                                                           OffsetDateTime minBeginAt,
                                                                           OffsetDateTime maxBeginAt) {
        // 计算时间范围差值（天数）
        long daysBetween = Duration.between(minBeginAt, maxBeginAt).toDays();

        log.info("设备[{}]查询时间范围: {} 到 {}, 跨度{}天", deviceSn, minBeginAt, maxBeginAt, daysBetween);

        if (daysBetween <= maxQueryDays) {
            // 时间范围不超过配置的最大天数，直接查询
            log.info("设备[{}]时间范围{}天，不超过{}天限制，执行单次查询", deviceSn, daysBetween, maxQueryDays);
            return querySingleTimeRange(deviceSn, minBeginAt, maxBeginAt);
        } else {
            // 时间范围超过限制，分段查询
            log.info("设备[{}]时间范围{}天，超过{}天限制，执行分段查询", deviceSn, daysBetween, maxQueryDays);
            return queryMultipleTimeRanges(deviceSn, minBeginAt, maxBeginAt);
        }
    }

    /**
     * 单次时间范围查询
     *
     * @param deviceSn 设备SN
     * @param minBeginAt 最小开始时间
     * @param maxBeginAt 最大开始时间
     * @return 飞行任务列表
     */
    public List<FhFlightTaskVO> querySingleTimeRange(String deviceSn, OffsetDateTime minBeginAt, OffsetDateTime maxBeginAt) {
        FhFlightTaskRequestByTimeBaseDTO requestDTO = new FhFlightTaskRequestByTimeBaseDTO();
        requestDTO.setBeginAt(minBeginAt.minusHours(queryTimeExtendHours).toEpochSecond());
        requestDTO.setEndAt(maxBeginAt.plusHours(queryTimeExtendHours).toEpochSecond());
        requestDTO.setDeviceSn(deviceSn);

        return getFlightTaskListFromOpenApiWithRetry(requestDTO);
    }

    /**
     * 精确查询指定时间范围的飞行任务
     * 不进行时间范围扩展，直接使用传入的精确时间范围
     *
     * @param deviceSn 设备SN
     * @param minBeginAt 开始时间
     * @param maxBeginAt 结束时间
     * @return 飞行任务列表
     */
    public List<FhFlightTaskVO> getFlightTaskListWithExactTimeRange(String deviceSn, OffsetDateTime minBeginAt, OffsetDateTime maxBeginAt) {
        FhFlightTaskRequestByTimeBaseDTO requestDTO = new FhFlightTaskRequestByTimeBaseDTO();
        requestDTO.setBeginAt(minBeginAt.toEpochSecond());
        requestDTO.setEndAt(maxBeginAt.toEpochSecond());
        requestDTO.setDeviceSn(deviceSn);

        log.info("设备[{}]精确查询时间范围: {} 到 {}", deviceSn, minBeginAt, maxBeginAt);
        return getFlightTaskListFromOpenApiWithRetry(requestDTO);
    }

    /**
     * 分段时间范围查询
     * 将大时间范围按maxQueryDays天为单位连续分割查询，然后合并结果
     *
     * @param deviceSn 设备SN
     * @param minBeginAt 最小开始时间
     * @param maxBeginAt 最大开始时间
     * @return 合并后的飞行任务列表
     */
    public List<FhFlightTaskVO> queryMultipleTimeRanges(String deviceSn, OffsetDateTime minBeginAt, OffsetDateTime maxBeginAt) {
        List<FhFlightTaskVO> allTasks = new ArrayList<>();
        // 用于去重
        Set<String> taskUuids = new HashSet<>();

        try {
            OffsetDateTime currentStart = minBeginAt;
            int segmentIndex = 1;

            // 按maxQueryDays天为单位循环查询
            while (currentStart.isBefore(maxBeginAt)) {
                // 计算当前段的结束时间，不能超过maxBeginAt
                OffsetDateTime currentEnd = currentStart.plusDays(maxQueryDays);
                if (currentEnd.isAfter(maxBeginAt)) {
                    currentEnd = maxBeginAt;
                }

                log.info("设备[{}]执行第{}段查询: {} 到 {}", deviceSn, segmentIndex, currentStart, currentEnd);

                // 构建查询请求
                FhFlightTaskRequestByTimeBaseDTO request = new FhFlightTaskRequestByTimeBaseDTO();
                request.setBeginAt(currentStart.minusHours(queryTimeExtendHours).toEpochSecond());
                request.setEndAt(currentEnd.plusHours(queryTimeExtendHours).toEpochSecond());
                request.setDeviceSn(deviceSn);

                // 执行查询
                List<FhFlightTaskVO> segmentTasks = getFlightTaskListFromOpenApiWithRetry(request);
                if (segmentTasks != null) {
                    int addedCount = 0;
                    for (FhFlightTaskVO task : segmentTasks) {
                        if (task.getUuid() != null && taskUuids.add(task.getUuid())) {
                            allTasks.add(task);
                            addedCount++;
                        }
                    }
                    log.info("设备[{}]第{}段查询获得{}个任务，去重后新增{}个任务",
                            deviceSn, segmentIndex, segmentTasks.size(), addedCount);
                } else {
                    log.warn("设备[{}]第{}段查询返回空结果", deviceSn, segmentIndex);
                }

                // 移动到下一个时间段
                currentStart = currentEnd;
                segmentIndex++;

                // 频率控制：每查询5段后间隔1秒，防止API频率限制
                if (segmentIndex % 5 == 1 && segmentIndex > 1) {
                    try {
                        log.info("设备[{}]已完成{}段查询，暂停1秒防止API频率限制", deviceSn, segmentIndex - 1);
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        log.warn("设备[{}]查询间隔暂停被中断", deviceSn);
                        Thread.currentThread().interrupt();
                    }
                }
            }

            log.info("设备[{}]分段查询完成，共执行{}段查询，合并后共{}个唯一任务",
                    deviceSn, segmentIndex - 1, allTasks.size());

        } catch (Exception e) {
            log.error("设备[{}]分段查询过程中发生异常", deviceSn, e);
            // 如果分段查询失败，尝试降级为单次查询
            log.warn("设备[{}]分段查询失败，降级为单次查询", deviceSn);
            return querySingleTimeRange(deviceSn, minBeginAt, maxBeginAt);
        }

        return allTasks;
    }

    /**
     * 带重试机制的API调用
     * 增加延迟逻辑防止API频率限制
     */
    @Retryable(value = {Exception.class}, maxAttempts = 5, backoff = @Backoff(delay = 5000, multiplier = 2))
    public List<FhFlightTaskVO> getFlightTaskListFromOpenApiWithRetry(FhFlightTaskRequestByTimeBaseDTO requestDTO) {
        try {
            // 在每次API调用前添加小延迟，进一步降低调用频率
            Thread.sleep(500);
            return getFlightTaskListFromOpenApi(requestDTO);
        } catch (InterruptedException e) {
            log.warn("设备[{}]API调用前延迟被中断", requestDTO.getDeviceSn());
            Thread.currentThread().interrupt();
            throw new RuntimeException("API调用被中断", e);
        } catch (Exception e) {
            log.warn("API调用失败，设备SN: {}, 错误: {}", requestDTO.getDeviceSn(), e.getMessage());
            throw e;
        }
    }

    /**
     * 直接调用OpenAPI获取飞行任务列表
     *
     * @param requestDTO 请求参数
     * @return 飞行任务列表
     */
    public List<FhFlightTaskVO> getFlightTaskListFromOpenApi(FhFlightTaskRequestByTimeBaseDTO requestDTO) {
        try {
            log.info("直接调用OpenAPI获取设备[{}]的飞行任务列表，时间范围: {} - {}",
                    requestDTO.getDeviceSn(), requestDTO.getBeginAt(), requestDTO.getEndAt());

            // 使用链式调用构建请求URL
            StringBuilder urlBuilder = new StringBuilder(FhOpenapiPathConstant.FLIGHT_TASK_LIST)
                .append("?sn=").append(requestDTO.getDeviceSn())
                .append("&begin_at=").append(requestDTO.getBeginAt())
                .append("&end_at=").append(requestDTO.getEndAt());

            // 调用OpenAPI
            String responseStr = FhOpenApiHttpUtil.get(urlBuilder.toString());

            // 解析响应结果
            JSONObject responseJson = JSONUtil.parseObj(responseStr);

            // 检查响应状态
            if (responseJson.getInt("code") != 0) {
                log.error("调用OpenAPI获取飞行任务列表失败: {}", responseJson.getStr("message"));
                return new ArrayList<>();
            }

            // 获取data字段
            JSONObject data = responseJson.getJSONObject("data");
            if (data == null) {
                log.warn("设备[{}]的飞行任务列表响应data为空", requestDTO.getDeviceSn());
                return new ArrayList<>();
            }

            // 获取list字段
            JSONArray taskArray = data.getJSONArray("list");
            if (taskArray == null || taskArray.isEmpty()) {
                log.info("设备[{}]在指定时间范围内没有飞行任务", requestDTO.getDeviceSn());
                return new ArrayList<>();
            }

            // 解析任务列表
            List<FhFlightTaskVO> tasks = new ArrayList<>();
            for (int i = 0; i < taskArray.size(); i++) {
                JSONObject taskJson = taskArray.getJSONObject(i);
                FhFlightTaskVO task = parseFlightTaskFromJson(taskJson);
                if (task != null) {
                    tasks.add(task);
                }
            }

            log.info("设备[{}]成功获取{}个飞行任务", requestDTO.getDeviceSn(), tasks.size());
            return tasks;

        } catch (Exception e) {
            log.error("调用OpenAPI获取设备[{}]飞行任务列表时发生异常", requestDTO.getDeviceSn(), e);
            throw new RuntimeException("获取飞行任务列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从JSON对象解析飞行任务
     *
     * @param taskJson 任务JSON对象
     * @return 飞行任务VO
     */
    private FhFlightTaskVO parseFlightTaskFromJson(JSONObject taskJson) {
        try {
            FhFlightTaskVO task = new FhFlightTaskVO();

            // 基本字段
            task.setUuid(taskJson.getStr("uuid"));
            // 使用工具类移除任务名称中的时间后缀
            task.setName(FlightTaskNameUtil.removeTimeSuffix(taskJson.getStr("name")));
            task.setTaskType(taskJson.getStr("task_type"));
            task.setTaskStatus(taskJson.getStr("status"));
            task.setSn(taskJson.getStr("sn"));
            task.setWaylineUuid(taskJson.getStr("wayline_uuid"));
            task.setLandingDockSn(taskJson.getStr("landing_dock_sn"));

            // 时间字段转换 - 处理ISO 8601格式的时间字符串
            task.setBeginAt(parseIsoDateTimeToDate(taskJson.getStr("begin_at"), "begin_at"));
            task.setEndAt(parseIsoDateTimeToDate(taskJson.getStr("end_at"), "end_at"));
            task.setRunAt(parseIsoDateTimeToDate(taskJson.getStr("run_at"), "run_at"));
            task.setCompletedAt(parseIsoDateTimeToDate(taskJson.getStr("completed_at"), "completed_at"));

            // 其他字段
            String folderIdStr = taskJson.getStr("folder_id");
            if (folderIdStr != null && !folderIdStr.isEmpty()) {
                try {
                    task.setFolderId(Integer.valueOf(folderIdStr));
                } catch (NumberFormatException e) {
                    log.warn("无法解析folder_id: {}", folderIdStr);
                }
            }
            task.setCurrentWaypointIndex(taskJson.getInt("current_waypoint_index"));
            task.setTotalWaypoints(taskJson.getInt("total_waypoints"));
            task.setMediaUploadStatus(taskJson.getStr("media_upload_status"));
            task.setResumableStatus(taskJson.getStr("resumable_status"));
            task.setIsBreakPointResume(taskJson.getBool("is_break_point_resume"));

            // 解析operations
            JSONObject operationsJson = taskJson.getJSONObject("operations");
            if (operationsJson != null) {
                FTOperations operations = new FTOperations();
                operations.setOperatorAccount(operationsJson.getStr("operator_account"));
                task.setOperations(operations);
            }

            // 解析exceptions - 兼容JSONObject和JSONArray格式
            Object exceptionsObj = taskJson.get("exceptions");
            if (exceptionsObj != null) {
                FTExceptions exceptions = new FTExceptions();

                if (exceptionsObj instanceof JSONArray) {
                    // 如果是数组，取第一个元素
                    JSONArray exceptionsArray = (JSONArray) exceptionsObj;
                    if (!exceptionsArray.isEmpty()) {
                        JSONObject firstException = exceptionsArray.getJSONObject(0);
                        if (firstException != null) {
                            exceptions.setCode(firstException.getLong("code", 0L));
                            exceptions.setHappenAt(firstException.getStr("happen_at"));
                            exceptions.setMessage(firstException.getStr("message"));
                            exceptions.setSn(firstException.getStr("sn"));
                            task.setExceptions(exceptions);
                        }
                    }
                } else if (exceptionsObj instanceof JSONObject) {
                    // 如果是对象，按原逻辑处理
                    JSONObject exceptionsJson = (JSONObject) exceptionsObj;
                    exceptions.setCode(exceptionsJson.getLong("code", 0L));
                    exceptions.setHappenAt(exceptionsJson.getStr("happen_at"));
                    exceptions.setMessage(exceptionsJson.getStr("message"));
                    exceptions.setSn(exceptionsJson.getStr("sn"));
                    task.setExceptions(exceptions);
                }
            }

            return task;

        } catch (Exception e) {
            log.error("解析飞行任务JSON时发生异常: {}", taskJson, e);
            return null;
        }
    }

    /**
     * 将ISO 8601格式的时间字符串转换为Date对象
     * 支持格式：2025-06-25T08:45:00+08:00
     *
     * @param dateTimeStr ISO 8601格式的时间字符串
     * @param fieldName 字段名称，用于日志记录
     * @return Date对象，如果时间字符串无效则返回null
     */
    private Date parseIsoDateTimeToDate(String dateTimeStr, String fieldName) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }

        try {
            log.info("原始时间字符串 {}: {}", fieldName, dateTimeStr);

            // 使用OffsetDateTime解析ISO 8601格式的时间字符串
            OffsetDateTime offsetDateTime = OffsetDateTime.parse(dateTimeStr);
            Date result = Date.from(offsetDateTime.toInstant());
            
            // 添加详细的转换日志
            long timestampSeconds = result.getTime() / 1000;
            log.info("转换详情 {}: OffsetDateTime={} -> Date={} -> TimestampSeconds={}", 
                fieldName, offsetDateTime, result, timestampSeconds);
            return result;

        } catch (Exception e) {
            log.warn("ISO时间字符串转换失败，{}: {}, 错误: {}", fieldName, dateTimeStr, e.getMessage());
            return null;
        }
    }

    /**
     * 分段查询指定时间范围的飞行任务（静态方法）
     * 用于时间冲突检查，直接使用时间戳参数
     *
     * @param deviceSn 设备SN
     * @param beginAtTimestamp 开始时间戳（秒）
     * @param endAtTimestamp 结束时间戳（秒）
     * @return 飞行任务列表
     */
    public static List<FhFlightTaskVO> getFlightTaskListFromOpenApiSegmented(String deviceSn, long beginAtTimestamp, long endAtTimestamp) {
        try {
            log.info("分段查询设备[{}]的飞行任务列表，时间范围: {} - {}", deviceSn, beginAtTimestamp, endAtTimestamp);

            // 转换时间戳为OffsetDateTime
            OffsetDateTime minBeginAt = OffsetDateTime.ofInstant(
                java.time.Instant.ofEpochSecond(beginAtTimestamp), 
                java.time.ZoneId.systemDefault());
            OffsetDateTime maxBeginAt = OffsetDateTime.ofInstant(
                java.time.Instant.ofEpochSecond(endAtTimestamp), 
                java.time.ZoneId.systemDefault());

            // 使用链式调用构建请求URL
            StringBuilder urlBuilder = new StringBuilder(FhOpenapiPathConstant.FLIGHT_TASK_LIST)
                .append("?sn=").append(deviceSn)
                .append("&begin_at=").append(beginAtTimestamp)
                .append("&end_at=").append(endAtTimestamp);

            // 调用OpenAPI
            String responseStr = FhOpenApiHttpUtil.get(urlBuilder.toString());

            // 解析响应结果
            JSONObject responseJson = JSONUtil.parseObj(responseStr);

            // 检查响应状态
            if (responseJson.getInt("code") != 0) {
                log.error("调用OpenAPI获取飞行任务列表失败: {}", responseJson.getStr("message"));
                return new ArrayList<>();
            }

            // 获取data字段
            JSONObject data = responseJson.getJSONObject("data");
            if (data == null) {
                log.warn("设备[{}]的飞行任务列表响应data为空", deviceSn);
                return new ArrayList<>();
            }

            // 获取list字段
            JSONArray taskArray = data.getJSONArray("list");
            if (taskArray == null || taskArray.isEmpty()) {
                log.info("设备[{}]在指定时间范围内没有飞行任务", deviceSn);
                return new ArrayList<>();
            }

            // 解析任务列表
            List<FhFlightTaskVO> tasks = new ArrayList<>();
            for (int i = 0; i < taskArray.size(); i++) {
                JSONObject taskJson = taskArray.getJSONObject(i);
                FhFlightTaskVO task = parseFlightTaskFromJsonStatic(taskJson);
                if (task != null) {
                    tasks.add(task);
                }
            }

            log.info("设备[{}]成功获取{}个飞行任务", deviceSn, tasks.size());
            return tasks;

        } catch (Exception e) {
            log.error("调用OpenAPI获取设备[{}]飞行任务列表时发生异常", deviceSn, e);
            return new ArrayList<>();
        }
    }

    /**
     * 从JSON对象解析飞行任务（静态方法）
     *
     * @param taskJson 任务JSON对象
     * @return 飞行任务VO
     */
    private static FhFlightTaskVO parseFlightTaskFromJsonStatic(JSONObject taskJson) {
        try {
            FhFlightTaskVO task = new FhFlightTaskVO();

            // 基本字段
            task.setUuid(taskJson.getStr("uuid"));
            // 使用工具类移除任务名称中的时间后缀
            task.setName(FlightTaskNameUtil.removeTimeSuffix(taskJson.getStr("name")));
            task.setTaskType(taskJson.getStr("task_type"));
            task.setTaskStatus(taskJson.getStr("status"));
            task.setSn(taskJson.getStr("sn"));
            task.setWaylineUuid(taskJson.getStr("wayline_uuid"));
            task.setLandingDockSn(taskJson.getStr("landing_dock_sn"));

            // 时间字段转换 - 处理ISO 8601格式的时间字符串
            task.setBeginAt(parseIsoDateTimeToDateStatic(taskJson.getStr("begin_at"), "begin_at"));
            task.setEndAt(parseIsoDateTimeToDateStatic(taskJson.getStr("end_at"), "end_at"));
            task.setRunAt(parseIsoDateTimeToDateStatic(taskJson.getStr("run_at"), "run_at"));
            task.setCompletedAt(parseIsoDateTimeToDateStatic(taskJson.getStr("completed_at"), "completed_at"));

            // 其他字段
            String folderIdStr = taskJson.getStr("folder_id");
            if (folderIdStr != null && !folderIdStr.isEmpty()) {
                try {
                    task.setFolderId(Integer.parseInt(folderIdStr));
                } catch (NumberFormatException e) {
                    log.warn("解析folder_id失败: {}", folderIdStr);
                }
            }

            task.setCurrentWaypointIndex(taskJson.getInt("current_waypoint_index"));
            task.setTotalWaypoints(taskJson.getInt("total_waypoints"));
            task.setMediaUploadStatus(taskJson.getStr("media_upload_status"));
            task.setResumableStatus(taskJson.getStr("resumable_status"));
            task.setIsBreakPointResume(taskJson.getBool("is_break_point_resume"));

            // 解析操作记录
            JSONObject operationsJson = taskJson.getJSONObject("operations");
            if (operationsJson != null) {
                FTOperations operations = new FTOperations();
                operations.setOperatorAccount(operationsJson.getStr("operator_account"));
                task.setOperations(operations);
            }

            // 解析异常信息
            JSONObject exceptionsJson = taskJson.getJSONObject("exceptions");
            if (exceptionsJson != null) {
                FTExceptions exceptions = new FTExceptions();
                exceptions.setCode(exceptionsJson.getLong("code"));
                exceptions.setMessage(exceptionsJson.getStr("message"));
                exceptions.setHappenAt(exceptionsJson.getStr("happen_at"));
                exceptions.setSn(exceptionsJson.getStr("sn"));
                task.setExceptions(exceptions);
            }

            return task;
        } catch (Exception e) {
            log.error("解析飞行任务JSON失败", e);
            return null;
        }
    }

    /**
     * 将ISO 8601格式的日期时间字符串转换为Date对象（静态方法）
     *
     * @param dateTimeStr 日期时间字符串
     * @param fieldName 字段名称（用于日志）
     * @return Date对象，转换失败时返回null
     */
    private static Date parseIsoDateTimeToDateStatic(String dateTimeStr, String fieldName) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }

        try {
            // 解析ISO 8601格式的时间字符串
            OffsetDateTime offsetDateTime = OffsetDateTime.parse(dateTimeStr.trim());
            // 转换为Date对象
            Date result = Date.from(offsetDateTime.toInstant());
            log.debug("时间字段{}转换成功: {} -> {}", fieldName, dateTimeStr, result);
            return result;

        } catch (Exception e) {
            log.warn("ISO时间字符串转换失败，{}: {}, 错误: {}", fieldName, dateTimeStr, e.getMessage());
            return null;
        }
    }

}
