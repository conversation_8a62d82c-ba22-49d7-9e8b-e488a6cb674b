package org.springblade.modules.fh.utils;

import org.springblade.modules.fh.pojo.dto.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 地图标注构建工具类
 * 用于简化不同类型地图标注的创建
 *
 * <AUTHOR>
 */
public class FhMapElementBuilder {

    /**
     * 创建点状标注
     *
     * @param projectUuid 项目UUID
     * @param groupId 标注文件夹ID
     * @param name 标注名称
     * @param desc 标注描述
     * @param longitude 经度
     * @param latitude 纬度
     * @param color 标注颜色
     * @param icon 标注图标
     * @return 地图标注创建DTO
     */
    public static FhMapElementCreateV09DTO createPointElement(String projectUuid, String groupId,
                                                              String name, String desc,
                                                              Double longitude, Double latitude,
                                                              String color, String icon) {
        FhMapElementCreateV09DTO dto = new FhMapElementCreateV09DTO();
        dto.setProjectUuid(projectUuid);
        dto.setGroupId(groupId);
        dto.setName(name);
        dto.setDesc(desc);
        dto.setElementSource(1);
        dto.setAddIndex(true);
        dto.setC2C(false);
        dto.setSource(1);

        // 构建资源项
        FhMapElementResourceDTO resource = new FhMapElementResourceDTO();
		// 点状标注
		resource.setType(0);

        // 构建内容
        FhMapElementContentDTO content = new FhMapElementContentDTO();
        content.setType("Feature");

        // 构建属性
        FhMapElementPropertiesDTO properties = new FhMapElementPropertiesDTO();
        properties.setColor(color);
        content.setProperties(properties);

        // 构建几何信息
        FhMapElementGeometryDTO geometry = new FhMapElementGeometryDTO();
        geometry.setType("Point");
        content.setGeometry(geometry);

        // 设置坐标 - 点的坐标格式: [longitude, latitude]
        geometry.setCoordinates(List.of(longitude, latitude));

        resource.setContent(content);
        dto.setResource(resource);

        return dto;
    }

    /**
     * 创建线状标注
     *
     * @param projectUuid 项目UUID
     * @param groupId 标注文件夹ID
     * @param name 标注名称
     * @param desc 标注描述
     * @param coordinates 线的坐标点列表 [[lng1,lat1], [lng2,lat2], ...]
     * @param color 线条颜色
     * @param strokeWidth 线宽
     * @return 地图标注创建DTO
     */
    public static FhMapElementCreateV09DTO createLineElement(String projectUuid, String groupId,
                                                             String name, String desc,
                                                             List<Double[]> coordinates,
                                                             String color, Integer strokeWidth) {
        FhMapElementCreateV09DTO dto = new FhMapElementCreateV09DTO();
        dto.setProjectUuid(projectUuid);
        dto.setGroupId(groupId);
        dto.setName(name);
        dto.setDesc(desc);
        dto.setElementSource(1);
        dto.setAddIndex(true);
        dto.setC2C(false);
        dto.setSource(1);

        // 构建资源项
        FhMapElementResourceDTO resource = new FhMapElementResourceDTO();
		// 线状标注
        resource.setType(1);

        // 构建内容
        FhMapElementContentDTO content = new FhMapElementContentDTO();
        content.setType("Feature");

        // 构建属性
        FhMapElementPropertiesDTO properties = new FhMapElementPropertiesDTO();
        properties.setColor(color);
        content.setProperties(properties);

        // 构建几何信息
        FhMapElementGeometryDTO geometry = new FhMapElementGeometryDTO();
        geometry.setType("LineString");
        content.setGeometry(geometry);

        // 设置坐标 - 线的坐标格式: [[lng1,lat1], [lng2,lat2], ...]
        List<List<Number>> lineCoordinates = coordinates.stream()
            .map(coord -> List.of((Number)coord[0], (Number)coord[1]))
            .collect(Collectors.toList());
        geometry.setCoordinates(lineCoordinates);

        resource.setContent(content);
        dto.setResource(resource);

        return dto;
    }

    /**
     * 创建多边形标注
     *
     * @param projectUuid 项目UUID
     * @param groupId 标注文件夹ID
     * @param name 标注名称
     * @param desc 标注描述
     * @param coordinates 多边形的坐标点列表 [[[lng1,lat1], [lng2,lat2], ...]]
     * @param color 边框颜色
     * @param fillColor 填充颜色
     * @param fillOpacity 填充透明度
     * @return 地图标注创建DTO
     */
    public static FhMapElementCreateV09DTO createPolygonElement(String projectUuid, String groupId,
                                                                String name, String desc,
                                                                List<List<Double[]>> coordinates,
                                                                String color, String fillColor, Double fillOpacity) {
        FhMapElementCreateV09DTO dto = new FhMapElementCreateV09DTO();
        dto.setProjectUuid(projectUuid);
        dto.setGroupId(groupId);
        dto.setName(name);
        dto.setDesc(desc);
        dto.setElementSource(1);
        dto.setAddIndex(true);
        dto.setC2C(false);
        dto.setSource(1);

        // 构建资源项
        FhMapElementResourceDTO resource = new FhMapElementResourceDTO();
		// 多边形标注
        resource.setType(2);

        // 构建内容
        FhMapElementContentDTO content = new FhMapElementContentDTO();
        content.setType("Feature");

        // 构建属性
        FhMapElementPropertiesDTO properties = new FhMapElementPropertiesDTO();
        properties.setColor(color);
        content.setProperties(properties);

        // 构建几何信息
        FhMapElementGeometryDTO geometry = new FhMapElementGeometryDTO();
        geometry.setType("Polygon");
        content.setGeometry(geometry);

        // 设置坐标 - 多边形的坐标格式: [[[lng1,lat1], [lng2,lat2], ...]]
        List<List<List<Number>>> polygonCoordinates = coordinates.stream()
            .map(ring -> ring.stream()
                .map(coord -> List.of((Number)coord[0], (Number)coord[1]))
                .collect(Collectors.toList()))
            .collect(Collectors.toList());
        geometry.setCoordinates(polygonCoordinates);

        resource.setContent(content);
        dto.setResource(resource);

        return dto;
    }

    /**
     * 创建圆形标注
     *
     * @param projectUuid 项目UUID
     * @param groupId 标注文件夹ID
     * @param name 标注名称
     * @param desc 标注描述
     * @param centerLongitude 圆心经度
     * @param centerLatitude 圆心纬度
     * @param radius 半径(米)
     * @param color 边框颜色
     * @param fillColor 填充颜色
     * @param fillOpacity 填充透明度
     * @return 地图标注创建DTO
     */
    public static FhMapElementCreateV09DTO createCircleElement(String projectUuid, String groupId,
                                                               String name, String desc,
                                                               Double centerLongitude, Double centerLatitude, Double radius,
                                                               String color, String fillColor, Double fillOpacity) {
        FhMapElementCreateV09DTO dto = new FhMapElementCreateV09DTO();
        dto.setProjectUuid(projectUuid);
        dto.setGroupId(groupId);
        dto.setName(name);
        dto.setDesc(desc);
        dto.setElementSource(1);
        dto.setAddIndex(true);
        dto.setC2C(false);
        dto.setSource(1);

        // 构建资源项
        FhMapElementResourceDTO resource = new FhMapElementResourceDTO();
		// 圆形标注
        resource.setType(7);

        // 构建内容
        FhMapElementContentDTO content = new FhMapElementContentDTO();

        // 构建属性
        FhMapElementPropertiesDTO properties = new FhMapElementPropertiesDTO();
        properties.setColor(color);
        content.setProperties(properties);

        // 构建几何信息
        FhMapElementGeometryDTO geometry = new FhMapElementGeometryDTO();
        geometry.setType("Circle");
        content.setGeometry(geometry);

        // 设置坐标 - 圆形的坐标格式: [longitude, latitude]
        geometry.setCoordinates(List.of(centerLongitude, centerLatitude));
        // 设置半径
        geometry.setRadius(radius);

        resource.setContent(content);
        dto.setResource(resource);

        return dto;
    }

}
