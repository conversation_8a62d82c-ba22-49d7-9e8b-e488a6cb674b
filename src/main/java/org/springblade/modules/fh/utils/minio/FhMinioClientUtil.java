package org.springblade.modules.fh.utils.minio;

import io.minio.MinioClient;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.modules.fh.service.IApplicationConfigService;

/**
 * FH MinIO客户端工具类
 * 支持动态配置的MinIO客户端创建和管理
 *
 * <AUTHOR>
public class FhMinioClientUtil {
	public static final Integer EXPIRY = 12;
    private static MinioClient minioClient;
    private static String lastConfigHash;

    private FhMinioClientUtil(){}

    /**
     * 获取MinIO客户端实例
     * 支持配置变更时自动重新创建客户端
     * @return MinioClient实例
     */
    public static MinioClient getMinioClient(){
        try {
            IApplicationConfigService configService = SpringUtil.getBean(IApplicationConfigService.class);

            String endpoint = configService.getSkiiMinioEndpoint();
            String accessKey = configService.getSkiiMinioAccessKey();
            String secretKey = configService.getSkiiMinioSecretKey();

            // 计算配置哈希值，用于检测配置变更
            String currentConfigHash = generateConfigHash(endpoint, accessKey, secretKey);

            // 如果配置发生变更或客户端未初始化，则重新创建
            if (minioClient == null || !currentConfigHash.equals(lastConfigHash)) {
                synchronized (FhMinioClientUtil.class) {
                    // 双重检查锁定
                    if (minioClient == null || !currentConfigHash.equals(lastConfigHash)) {
                        minioClient = MinioClient.builder()
                                .endpoint(endpoint)
                                .credentials(accessKey, secretKey)
                                .build();
                        lastConfigHash = currentConfigHash;

                        // 更新静态字段以保持兼容性
                        updateStaticCompatibilityFields(endpoint, accessKey, secretKey, configService.getSkiiMinioBucket());
                    }
                }
            }

            return minioClient;

        } catch (Exception e) {
            // 如果动态配置获取失败，尝试使用静态配置作为降级方案
            if (minioClient == null && FhMinioConfiguration.endpoint != null) {
                synchronized (FhMinioClientUtil.class) {
                    if (minioClient == null) {
                        minioClient = MinioClient.builder()
                                .endpoint(FhMinioConfiguration.endpoint)
                                .credentials(FhMinioConfiguration.accessKey, FhMinioConfiguration.secretKey)
                                .build();
                    }
                }
            }

            if (minioClient == null) {
                throw new RuntimeException("Unable to create MinioClient: " + e.getMessage(), e);
            }

            return minioClient;
        }
    }

    /**
     * 强制重新创建MinIO客户端
     * 用于配置更新后立即应用新配置
     */
    public static synchronized void forceRefresh() {
        minioClient = null;
        lastConfigHash = null;
    }

    /**
     * 生成配置哈希值
     * @param endpoint 端点
     * @param accessKey 访问密钥
     * @param secretKey 秘密密钥
     * @return 配置哈希值
     */
    private static String generateConfigHash(String endpoint, String accessKey, String secretKey) {
        return String.valueOf((endpoint + accessKey + secretKey).hashCode());
    }

    /**
     * 更新静态字段以保持向后兼容性
     * @param endpoint 端点
     * @param accessKey 访问密钥
     * @param secretKey 秘密密钥
     * @param bucket 存储桶
     */
    private static void updateStaticCompatibilityFields(String endpoint, String accessKey, String secretKey, String bucket) {
        FhMinioConfiguration.endpoint = endpoint;
        FhMinioConfiguration.accessKey = accessKey;
        FhMinioConfiguration.secretKey = secretKey;
        FhMinioConfiguration.bucket = bucket;
    }
}
