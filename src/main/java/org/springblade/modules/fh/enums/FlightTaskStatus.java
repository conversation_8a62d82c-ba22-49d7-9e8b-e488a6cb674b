package org.springblade.modules.fh.enums;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "飞行任务状态枚举")
public enum FlightTaskStatus {

    @Schema(description = "待开始")
    WAITING("waiting"),

    @Schema(description = "启动失败")
    STARTING_FAILURE("starting_failure"),

    @Schema(description = "执行中")
    EXECUTING("executing"),

    @Schema(description = "暂停")
    PAUSED("paused"),

    @Schema(description = "终止")
    TERMINATED("terminated"),

    @Schema(description = "成功")
    SUCCESS("success"),

    @Schema(description = "挂起")
    SUSPENDED("suspended"),

    @Schema(description = "超时")
    TIMEOUT("timeout");

    private final String value;

    FlightTaskStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}