package org.springblade.modules.fh.enums;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "飞行任务类型枚举")
public enum FlightTaskType {

	@Schema(description = "立即任务")
	IMMEDIATE("immediate"),

	@Schema(description = "单次定时任务")
	TIMED("timed"),

	@Schema(description = "重复任务")
	RECURRING("recurring"),

	@Schema(description = "连续任务")
	CONTINUOUS("continuous");

	private final String value;

	FlightTaskType(String value) {
		this.value = value;
	}

	public String getValue() {
		return value;
	}

}
