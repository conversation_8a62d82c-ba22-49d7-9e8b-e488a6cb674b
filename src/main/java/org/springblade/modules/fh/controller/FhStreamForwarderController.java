package org.springblade.modules.fh.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.modules.fh.pojo.dto.FhStreamForwarderCreateDTO;
import org.springblade.modules.fh.pojo.dto.FhStreamForwarderListItemDTO;
import org.springblade.modules.fh.pojo.dto.FhStreamForwarderStatusDTO;
import org.springblade.modules.fh.service.IFhStreamForwarderService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 码流转发器管理接口
 *
 * <AUTHOR> AI
 */
@Slf4j
@RestController
@RequestMapping("/fh/stream-forwarder")
@RequiredArgsConstructor
@Tag(name = "码流转发器管理接口")
public class FhStreamForwarderController {

    @Resource
    private IFhStreamForwarderService streamForwarderService;

    /**
     * 创建码流转发器
     * 该方法调用大疆API创建新的码流转发器
     *
     * @param createDTO 创建参数
     * @return 创建结果
     */
    @PostMapping("/create")
    @Operation(summary = "创建码流转发器", description = "调用大疆API创建新的码流转发器，支持RTMP和GB28181协议")
    public R<String> createStreamForwarder(@Validated @RequestBody FhStreamForwarderCreateDTO createDTO) {
        log.info("创建码流转发器请求，设备SN: {}, 转发器名称: {}, 协议: {}",
                createDTO.getSn(), createDTO.getConverterName(), createDTO.getSchema());
        return streamForwarderService.createStreamForwarder(createDTO);
    }

    /**
     * 开启/关闭码流转发器
     * 该方法调用大疆API控制码流转发器的启停状态
     *
     * @param statusDTO 状态控制参数
     * @return 操作结果
     */
    @PostMapping("/status")
    @Operation(summary = "开启/关闭码流转发器", description = "调用大疆API控制码流转发器的启停状态")
    public R<String> updateStreamForwarderStatus(@Validated @RequestBody FhStreamForwarderStatusDTO statusDTO) {
        String actionText = statusDTO.getAction() == true ? "开启" : "关闭";
        log.info("{}码流转发器请求， 码流转发器ID: {}", actionText, statusDTO.getConverterId());
        return streamForwarderService.updateStreamForwarderStatus(statusDTO);
    }

    /**
     * 删除码流转发器
     * 该方法调用大疆API删除指定的码流转发器
     *
     * @param converterId 码流转发器ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{converterId}")
    @Operation(summary = "删除码流转发器", description = "调用大疆API删除指定的码流转发器")
    public R<String> deleteStreamForwarder(
            @Parameter(description = "码流转发器ID", required = true) @PathVariable String converterId) {
        log.info("删除码流转发器请求，码流转发器ID: {}", converterId);
        return streamForwarderService.deleteStreamForwarder(converterId);
    }

    /**
     * 获取码流转发器列表
     * 该方法调用大疆API获取所有码流转发器的列表
     *
     * @return 码流转发器列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取码流转发器列表", description = "调用大疆API获取所有码流转发器的列表")
    public R<List<FhStreamForwarderListItemDTO>> getStreamForwarderList() {
        log.info("获取码流转发器列表请求");
        return streamForwarderService.getStreamForwarderList();
    }

}
