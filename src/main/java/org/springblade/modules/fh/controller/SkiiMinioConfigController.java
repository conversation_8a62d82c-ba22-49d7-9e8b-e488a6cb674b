package org.springblade.modules.fh.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springblade.core.tool.api.R;
import org.springblade.modules.fh.dto.MinioConfigUpdateRequest;
import org.springblade.modules.fh.service.ISkiiMinioConfigService;

import jakarta.validation.Valid;

import java.util.HashMap;
import java.util.Map;

/**
 * Skii MinIO配置管理控制器
 * 提供配置的查询、更新、缓存管理等API接口
 *
 * <AUTHOR>
@Slf4j
@RestController
@RequestMapping("/fh/skii-minio-config")
@RequiredArgsConstructor
@Tag(name = "Skii MinIO配置管理", description = "Skii MinIO配置的动态管理接口")
public class SkiiMinioConfigController {

    private final ISkiiMinioConfigService skiiMinioConfigService;



    @GetMapping("/all")
    @Operation(summary = "获取所有MinIO配置信息")
    public R<Map<String, String>> getAllConfigs() {
        try {
            Map<String, String> configs = new HashMap<>();
            configs.put("endpoint", skiiMinioConfigService.getEndpoint());
            configs.put("accessKey", maskSensitiveInfo(skiiMinioConfigService.getAccessKey()));
            configs.put("secretKey", maskSensitiveInfo(skiiMinioConfigService.getSecretKey()));
            configs.put("bucket", skiiMinioConfigService.getBucket());
            return R.data(configs);
        } catch (Exception e) {
            log.error("获取所有MinIO配置失败: {}", e.getMessage(), e);
            return R.fail("获取所有MinIO配置失败: " + e.getMessage());
        }
    }

    @PutMapping("/endpoint")
    @Operation(summary = "更新MinIO服务端点")
    public R<Boolean> updateEndpoint(
            @Parameter(description = "MinIO服务端点", required = true)
            @RequestParam String endpoint) {
        try {
            boolean success = skiiMinioConfigService.updateEndpoint(endpoint);
            if (success) {
                return R.success("MinIO服务端点更新成功");
            } else {
                return R.fail("MinIO服务端点更新失败");
            }
        } catch (Exception e) {
            log.error("更新MinIO服务端点失败: {}", e.getMessage(), e);
            return R.fail("更新MinIO服务端点失败: " + e.getMessage());
        }
    }

    @PutMapping("/access-key")
    @Operation(summary = "更新MinIO访问密钥")
    public R<Boolean> updateAccessKey(
            @Parameter(description = "MinIO访问密钥", required = true)
            @RequestParam String accessKey) {
        try {
            boolean success = skiiMinioConfigService.updateAccessKey(accessKey);
            if (success) {
                return R.success("MinIO访问密钥更新成功");
            } else {
                return R.fail("MinIO访问密钥更新失败");
            }
        } catch (Exception e) {
            log.error("更新MinIO访问密钥失败: {}", e.getMessage(), e);
            return R.fail("更新MinIO访问密钥失败: " + e.getMessage());
        }
    }

    @PutMapping("/secret-key")
    @Operation(summary = "更新MinIO秘密密钥")
    public R<Boolean> updateSecretKey(
            @Parameter(description = "MinIO秘密密钥", required = true)
            @RequestParam String secretKey) {
        try {
            boolean success = skiiMinioConfigService.updateSecretKey(secretKey);
            if (success) {
                return R.success("MinIO秘密密钥更新成功");
            } else {
                return R.fail("MinIO秘密密钥更新失败");
            }
        } catch (Exception e) {
            log.error("更新MinIO秘密密钥失败: {}", e.getMessage(), e);
            return R.fail("更新MinIO秘密密钥失败: " + e.getMessage());
        }
    }

    @PutMapping("/bucket")
    @Operation(summary = "更新MinIO存储桶名称")
    public R<Boolean> updateBucket(
            @Parameter(description = "MinIO存储桶名称", required = true)
            @RequestParam String bucket) {
        try {
            boolean success = skiiMinioConfigService.updateBucket(bucket);
            if (success) {
                return R.success("MinIO存储桶名称更新成功");
            } else {
                return R.fail("MinIO存储桶名称更新失败");
            }
        } catch (Exception e) {
            log.error("更新MinIO存储桶名称失败: {}", e.getMessage(), e);
            return R.fail("更新MinIO存储桶名称失败: " + e.getMessage());
        }
    }

    @PostMapping("/batch")
    @Operation(summary = "批量更新所有MinIO配置参数")
    public R<Boolean> updateAllConfigs(
            @Parameter(description = "MinIO配置更新请求", required = true)
            @Valid @RequestBody MinioConfigUpdateRequest request) {
        try {
            boolean success = skiiMinioConfigService.updateAllConfigs(request);
            if (success) {
                return R.success("MinIO配置批量更新成功");
            } else {
                return R.fail("MinIO配置批量更新失败");
            }
        } catch (Exception e) {
            log.error("MinIO配置批量更新失败: {}", e.getMessage(), e);
            return R.fail("MinIO配置批量更新失败: " + e.getMessage());
        }
    }

	@Deprecated
    @PostMapping("/cache/refresh")
    @Operation(summary = "刷新指定配置缓存")
    public R<Boolean> refreshCache(
            @Parameter(description = "参数键", required = true)
            @RequestParam String paramKey) {
        try {
            skiiMinioConfigService.refreshCache(paramKey);
            return R.success("配置缓存刷新成功");
        } catch (Exception e) {
            log.error("刷新配置缓存失败: {}", e.getMessage(), e);
            return R.fail("刷新配置缓存失败: " + e.getMessage());
        }
    }

    @PostMapping("/cache/refresh-all")
    @Operation(summary = "刷新所有MinIO配置缓存")
    public R<Boolean> refreshAllCache() {
        try {
            skiiMinioConfigService.refreshAllCache();
            return R.success("所有MinIO配置缓存刷新成功");
        } catch (Exception e) {
            log.error("刷新所有MinIO配置缓存失败: {}", e.getMessage(), e);
            return R.fail("刷新所有MinIO配置缓存失败: " + e.getMessage());
        }
    }

    @PostMapping("/cache/warm-up")
    @Operation(summary = "预热配置缓存")
    public R<Boolean> warmUpCache() {
        try {
            skiiMinioConfigService.warmUpCache();
            return R.success("配置缓存预热成功");
        } catch (Exception e) {
            log.error("配置缓存预热失败: {}", e.getMessage(), e);
            return R.fail("配置缓存预热失败: " + e.getMessage());
        }
    }

    @GetMapping("/validate")
    @Operation(summary = "验证MinIO连通性")
    public R<Boolean> validateConnection() {
        try {
            boolean isConnected = skiiMinioConfigService.validateConnection();
            if (isConnected) {
                return R.data(true, "MinIO连通性验证成功");
            } else {
                return R.data(false, "MinIO连通性验证失败，请检查配置和网络连接");
            }
        } catch (Exception e) {
            log.error("验证MinIO连通性失败: {}", e.getMessage(), e);
            return R.fail("验证MinIO连通性失败: " + e.getMessage());
        }
    }

    /**
     * 脱敏处理敏感信息
     * @param sensitiveInfo 敏感信息
     * @return 脱敏后的信息
     */
    private String maskSensitiveInfo(String sensitiveInfo) {
        if (sensitiveInfo == null || sensitiveInfo.length() <= 4) {
            return "****";
        }
        return sensitiveInfo.substring(0, 2) + "****" + sensitiveInfo.substring(sensitiveInfo.length() - 2);
    }
}
