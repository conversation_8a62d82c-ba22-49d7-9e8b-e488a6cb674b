package org.springblade.modules.fh.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.modules.fh.pojo.dto.FhLiveStreamStartDTO;
import org.springblade.modules.fh.pojo.vo.FhLiveStreamResponseVO;
import org.springblade.modules.fh.service.IFhLiveStreamService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 直播管理接口
 *
 * <AUTHOR> AI
 */
@Slf4j
@RestController
@RequestMapping("/fh/live-stream")
@RequiredArgsConstructor
@Tag(name = "直播管理接口")
public class FhLiveStreamController {

    @Resource
    private IFhLiveStreamService liveStreamService;

    /**
     * 开启直播
     * 该方法调用大疆API开启设备的直播功能
     *
     * @param startDTO 开启直播请求参数
     * @return 直播响应数据
     */
    @PostMapping("/start")
    @Operation(summary = "开启直播", description = "开启指定设备摄像头的直播功能，返回直播流地址和Token信息")
    public R<FhLiveStreamResponseVO> startLiveStream(@Validated @RequestBody FhLiveStreamStartDTO startDTO) {
        log.info("开启直播请求, 设备序列号: {}, 摄像头索引: {}", startDTO.getSn(), startDTO.getCameraIndex());
        return R.data(liveStreamService.startLiveStream(startDTO));
    }

}
