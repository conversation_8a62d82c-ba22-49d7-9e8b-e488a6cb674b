package org.springblade.modules.fh.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.modules.fh.pojo.dto.FhMapElementCreateV09DTO;
import org.springblade.modules.fh.pojo.dto.FhMapElementDeleteDTO;
import org.springblade.modules.fh.pojo.dto.FhMapElementFolderCreateDTO;
import org.springblade.modules.fh.pojo.dto.FhMapElementFolderQueryDTO;
import org.springblade.modules.fh.pojo.vo.FhMapElementDeleteVO;
import org.springblade.modules.fh.pojo.vo.FhMapElementFolderCreateVO;
import org.springblade.modules.fh.pojo.vo.FhMapElementFolderVO;
import org.springblade.modules.fh.pojo.vo.FhMapElementV09VO;
import org.springblade.modules.fh.service.IFhMapElementService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 地图标注管理接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/fh/map-element")
@RequiredArgsConstructor
@Tag(name = "地图标注管理接口")
public class FhMapElementController {

    @Resource
    private IFhMapElementService mapElementService;

    /**
     * 创建地图标注(v0.9版本)
     *
     * 该方法使用大疆司空v0.9版本API创建地图标注，支持点、线、多边形、圆形等多种标注类型
     * 使用GeoJSON格式定义几何信息，支持更丰富的标注属性和样式设置
     *
     * @param createDTO 地图标注创建参数(v0.9版本)
     * @return 创建的地图标注信息
     */
    @PostMapping("/create/v09")
    @Operation(summary = "创建地图标注(v0.9版本)", description = "使用v0.9版本API在地图上创建新的标注，支持多种几何类型")
    public R<FhMapElementV09VO> createMapElementV09(@Valid @RequestBody FhMapElementCreateV09DTO createDTO) {
        log.info("创建地图标注请求(v0.9): 项目UUID={}, 文件夹ID={}, 名称={}",
                createDTO.getProjectUuid(), createDTO.getGroupId(), createDTO.getName());
        return mapElementService.createMapElementV09(createDTO);
    }

    /**
     * 删除地图标注
     *
     * 该方法使用大疆司空v0.9版本API删除指定的地图标注
     * 需要提供项目UUID和标注ID作为参数
     *
     * @param deleteDTO 地图标注删除参数
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除地图标注", description = "删除指定的地图标注")
    public R<FhMapElementDeleteVO> deleteMapElement(@Valid @RequestBody FhMapElementDeleteDTO deleteDTO) {
        log.info("删除地图标注请求: 项目UUID={}, 标注ID={}",
                deleteDTO.getProjectUuid(), deleteDTO.getElementId());
        return mapElementService.deleteMapElement(deleteDTO);
    }

    @PostMapping("/folders")
    @Operation(summary = "获取标注文件夹列表")
    public R<List<FhMapElementFolderVO>> getMapElementFolders(
            @Valid @RequestBody FhMapElementFolderQueryDTO queryDTO) {

        log.info("获取标注文件夹列表请求: 项目UUID={}, 标注文件ID={}, 是否已分发={}, 类型={}",
                queryDTO.getProjectId(), queryDTO.getGroupId(), queryDTO.getIsDistributed(), queryDTO.getType());
        List<FhMapElementFolderVO> result = mapElementService.getMapElementFolders(queryDTO);
        return R.data(result);
    }

    /**
     * 创建标注文件夹
     *
     * 该方法使用大疆司空v0.9版本API创建标注文件夹
     * 支持设置父文件夹ID来创建子文件夹
     *
     * @param createDTO 标注文件夹创建参数
     * @return 创建的标注文件夹信息
     */
    @PostMapping("/folder/create")
    @Operation(summary = "创建标注文件夹", description = "创建一个新的标注文件夹")
    public R<FhMapElementFolderCreateVO> createMapElementFolder(@Valid @RequestBody FhMapElementFolderCreateDTO createDTO) {
        log.info("创建标注文件夹请求: 项目UUID={}, 文件夹名称={}, 父文件夹ID={}",
                createDTO.getProjectUuid(), createDTO.getName(), createDTO.getPid());
        return mapElementService.createMapElementFolder(createDTO);
    }

}
