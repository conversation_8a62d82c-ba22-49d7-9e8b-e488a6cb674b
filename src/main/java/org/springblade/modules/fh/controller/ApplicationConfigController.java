package org.springblade.modules.fh.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.modules.fh.dto.ApplicationConfigResponse;
import org.springblade.modules.fh.dto.BatchConfigUpdateRequest;
import org.springblade.modules.fh.dto.BatchConfigUpdateResult;
import org.springblade.modules.fh.dto.BatchConfigValidationRequest;
import org.springblade.modules.fh.dto.BatchConfigValidationResult;
import org.springblade.modules.fh.service.IApplicationConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应用配置管理控制器
 * 提供Skii MinIO、MQTT、FH-SDK、EJ-FH-SERVER等多种配置的管理API接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/fh/application-config")
@Tag(name = "应用配置管理", description = "应用配置的动态管理接口")
public class ApplicationConfigController {

    @Autowired
    private IApplicationConfigService applicationConfigService;

    // ==================== 配置查询接口 ====================

    @GetMapping("/all")
    @Operation(summary = "获取所有应用配置信息")
    public R<ApplicationConfigResponse> getAllConfigs() {
        try {
            ApplicationConfigResponse response = ApplicationConfigResponse.builder()
                .skiiMinio(ApplicationConfigResponse.SkiiMinioConfig.builder()
                    .endpoint(applicationConfigService.getSkiiMinioEndpoint())
                    .accessKey(maskSensitiveInfo(applicationConfigService.getSkiiMinioAccessKey()))
                    .secretKey(maskSensitiveInfo(applicationConfigService.getSkiiMinioSecretKey()))
                    .bucket(applicationConfigService.getSkiiMinioBucket())
                    .build())
                .mqtt(ApplicationConfigResponse.MqttConfig.builder()
                    .basic(ApplicationConfigResponse.MqttConfig.MqttBasicConfig.builder()
                        .protocol(applicationConfigService.getMqttBasicProtocol())
                        .host(applicationConfigService.getMqttBasicHost())
                        .port(applicationConfigService.getMqttBasicPort())
                        .username(applicationConfigService.getMqttBasicUsername())
                        .password(maskSensitiveInfo(applicationConfigService.getMqttBasicPassword()))
                        .clientId(applicationConfigService.getMqttBasicClientId())
                        .path(applicationConfigService.getMqttBasicPath())
                        .build())
                    .drc(ApplicationConfigResponse.MqttConfig.MqttDrcConfig.builder()
                        .protocol(applicationConfigService.getMqttDrcProtocol())
                        .host(applicationConfigService.getMqttDrcHost())
                        .port(applicationConfigService.getMqttDrcPort())
                        .path(applicationConfigService.getMqttDrcPath())
                        .username(applicationConfigService.getMqttDrcUsername())
                        .password(maskSensitiveInfo(applicationConfigService.getMqttDrcPassword()))
                        .build())
                    .build())
                .fhSdk(ApplicationConfigResponse.FhSdkConfig.builder()
                    .inboundTopic(applicationConfigService.getFhSdkMqttInboundTopic())
                    .build())
                .ejFhServer(ApplicationConfigResponse.EjFhServerConfig.builder()
                    .url(applicationConfigService.getEjFhServerUrl())
                    .build())
                .annotationFolderId(applicationConfigService.getAnnotationFolderId())
                .build();

            return R.data(response);
        } catch (Exception e) {
            log.error("获取所有应用配置失败: {}", e.getMessage(), e);
            return R.fail("获取所有应用配置失败: " + e.getMessage());
        }
    }

    // ==================== 批量配置验证接口 ====================

    @PostMapping("/batch-validate")
    @Operation(summary = "批量验证应用配置", description = "验证多种配置类型的连接有效性，不进行实际更新")
    public R<BatchConfigValidationResult> batchValidateConfigs(
            @Parameter(description = "批量配置验证请求", required = true)
            @Valid @RequestBody BatchConfigValidationRequest request) {
        try {
            log.info("收到批量配置验证请求，配置数量: {}", request.getConfigCount());

            BatchConfigValidationResult result = applicationConfigService.validateBatchConfigs(request);

            if (result.isSuccess()) {
                log.info("批量配置验证成功，成功: {}/{}, 耗时: {}ms",
                        result.getSuccessCount(), result.getTotalCount(), result.getTotalTimeMs());
                return R.data(result, "批量配置验证成功");
            } else {
                log.warn("批量配置验证部分失败，成功: {}/{}, 失败原因: {}",
                        result.getSuccessCount(), result.getTotalCount(), result.getOverallFailureReason());
                return R.data(result, "批量配置验证完成，部分配置验证失败");
            }

        } catch (Exception e) {
            log.error("批量配置验证异常: {}", e.getMessage(), e);
            return R.fail("批量配置验证异常: " + e.getMessage());
        }
    }

    // ==================== 批量配置更新接口 ====================

    @PostMapping("/batch-update-only")
    @Operation(summary = "批量更新应用配置", description = "直接更新多种配置类型，不进行连接验证")
    public R<BatchConfigUpdateResult> batchUpdateConfigsOnly(
            @Parameter(description = "批量配置更新请求", required = true)
            @Valid @RequestBody BatchConfigUpdateRequest request) {
        try {
            log.info("收到批量配置更新请求（跳过验证），配置数量: {}", request.getConfigCount());

            BatchConfigUpdateResult result = applicationConfigService.updateBatchConfigs(request);

            if (result.isSuccess()) {
                log.info("批量配置更新成功，成功: {}/{}, 耗时: {}ms",
                        result.getSuccessCount(), result.getTotalCount(), result.getTotalTimeMs());
                return R.data(result, "批量配置更新成功");
            } else {
                log.warn("批量配置更新部分失败，成功: {}/{}, 失败原因: {}",
                        result.getSuccessCount(), result.getTotalCount(), result.getOverallFailureReason());
                return R.fail(result.getOverallFailureReason());
            }

        } catch (Exception e) {
            log.error("批量配置更新异常: {}", e.getMessage(), e);
            return R.fail("批量配置更新异常: " + e.getMessage());
        }
    }

    @PostMapping("/batch-update")
    @Operation(summary = "批量验证和更新应用配置", description = "支持同时更新多种配置类型，包含连接验证和原子性更新")
    public R<BatchConfigUpdateResult> batchUpdateConfigs(
            @Parameter(description = "批量配置更新请求", required = true)
            @Valid @RequestBody BatchConfigUpdateRequest request) {
        try {
            log.info("收到批量配置更新请求，配置数量: {}", request.getConfigCount());

            BatchConfigUpdateResult result = applicationConfigService.validateAndUpdateBatchConfigs(request);

            if (result.isSuccess()) {
                log.info("批量配置更新成功，成功: {}/{}, 耗时: {}ms",
                        result.getSuccessCount(), result.getTotalCount(), result.getTotalTimeMs());
                return R.data(result, "批量配置更新成功");
            } else {
                log.warn("批量配置更新部分失败，成功: {}/{}, 失败原因: {}",
                        result.getSuccessCount(), result.getTotalCount(), result.getOverallFailureReason());
                return R.fail(result.getOverallFailureReason());
            }

        } catch (Exception e) {
            log.error("批量配置更新异常: {}", e.getMessage(), e);
            return R.fail("批量配置更新异常: " + e.getMessage());
        }
    }

    // ==================== 缓存管理接口 ====================

    @PostMapping("/cache/refresh")
    @Operation(summary = "刷新指定配置缓存")
    public R<Boolean> refreshCache(
            @Parameter(description = "参数键", required = true)
            @RequestParam String paramKey) {
        try {
            applicationConfigService.refreshCache(paramKey);
            return R.success("配置缓存刷新成功");
        } catch (Exception e) {
            log.error("刷新配置缓存失败: {}", e.getMessage(), e);
            return R.fail("刷新配置缓存失败: " + e.getMessage());
        }
    }

    @PostMapping("/cache/refresh-all")
    @Operation(summary = "刷新所有应用配置缓存")
    public R<Boolean> refreshAllCache() {
        try {
            applicationConfigService.refreshAllCache();
            return R.success("所有应用配置缓存刷新成功");
        } catch (Exception e) {
            log.error("刷新所有应用配置缓存失败: {}", e.getMessage(), e);
            return R.fail("刷新所有应用配置缓存失败: " + e.getMessage());
        }
    }

    @PostMapping("/cache/warm-up")
    @Operation(summary = "预热配置缓存")
    public R<Boolean> warmUpCache() {
        try {
            applicationConfigService.warmUpCache();
            return R.success("配置缓存预热成功");
        } catch (Exception e) {
            log.error("配置缓存预热失败: {}", e.getMessage(), e);
            return R.fail("配置缓存预热失败: " + e.getMessage());
        }
    }



    // ==================== 配置回滚接口 ====================

    @PostMapping("/rollback")
    @Operation(summary = "回滚指定配置到上一个版本")
    public R<Boolean> rollbackConfigs(
            @Parameter(description = "要回滚的配置键列表", required = true)
            @RequestBody List<String> configKeys) {
        try {
            log.info("收到配置回滚请求: keys={}", configKeys);

            boolean success = applicationConfigService.rollbackConfigs(configKeys);

            if (success) {
                log.info("配置回滚成功: keys={}", configKeys);
                return R.success("配置回滚成功");
            } else {
                log.warn("配置回滚失败: keys={}", configKeys);
                return R.fail("配置回滚失败，请检查日志");
            }

        } catch (Exception e) {
            log.error("配置回滚异常: {}", e.getMessage(), e);
            return R.fail("配置回滚异常: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 脱敏处理敏感信息
     * @param sensitiveInfo 敏感信息
     * @return 脱敏后的信息
     */
    private String maskSensitiveInfo(String sensitiveInfo) {
//        if (sensitiveInfo == null || sensitiveInfo.length() <= 4) {
//            return "****";
//        }
//        return sensitiveInfo.substring(0, 2) + "****" + sensitiveInfo.substring(sensitiveInfo.length() - 2);
		return sensitiveInfo;
    }
    
}
