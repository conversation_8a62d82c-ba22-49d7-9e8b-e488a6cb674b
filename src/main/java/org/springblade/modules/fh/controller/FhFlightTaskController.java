package org.springblade.modules.fh.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.modules.fh.component.scheduler.FlightTaskStatusScheduler;
import org.springblade.modules.fh.pojo.dto.*;
import org.springblade.modules.fh.pojo.vo.*;
import org.springblade.modules.fh.service.IFhFlightTaskService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 飞行任务管理接口
 *
 * <AUTHOR> AI
 */
@Slf4j
@RestController
@RequestMapping("/fh/flight-task")
@RequiredArgsConstructor
@Tag(name = "飞行任务管理接口")
public class FhFlightTaskController {

    @Resource
    private IFhFlightTaskService flightTaskService;
    @Resource
    private FlightTaskStatusScheduler flightTaskStatusScheduler;

    /**
     * 根据时间获取飞行任务列表
     * 该方法会先获取所有drone设备的SN号，然后循环调用大疆API获取每个设备的飞行任务列表
     *
     * @param requestDTO 请求参数
     * @return 飞行任务列表
     */
    @PostMapping("/list")
    @Operation(summary = "获取飞行任务列表", description = "获取所有设备的飞行任务列表")
    public R<List<FhFlightTaskVO>> getFlightTaskList(@Validated @RequestBody FhFlightTaskRequestByTimeBaseDTO requestDTO) {
        return R.data(flightTaskService.getFlightTaskList(requestDTO));
    }

    /**
     * 根据任务的设定开始时间，统计哪些年份有任务，返回一个年份列表
     *
     * @return 年份列表，按降序排序
     */
    @GetMapping("/years")
    @Operation(summary = "获取任务年份列表", description = "根据任务的设定开始时间，统计哪些年份有任务")
    public R<List<Integer>> getTaskYears() {
        return R.data(flightTaskService.getTaskYears());
    }

    /**
     * 获取设备执行任务统计
     * 根据年份统计每月各设备的任务数量，返回按设备组织的统计结果
     *
     * @param dto 包含年份的请求参数
     * @return 按设备组织的任务统计结果列表，每个设备包含12个月的任务数量数据
     */
    @PostMapping("/device-stats")
    @Operation(summary = "设备执行任务统计", description = "根据年份统计每月各设备的任务数量，按设备组织返回")
    public R<List<FhDeviceTaskStatsVO>> getDeviceTaskStats(@Validated @RequestBody FhDeviceTaskStatsDTO dto) {
        return R.data(flightTaskService.getDeviceTaskStats(dto));
    }

    /**
     * 创建飞行任务
     * 该方法调用大疆API创建新的飞行任务
     *
     * @return 创建结果
     */
    @PostMapping(value = "/create")
    public R<String> createFlightTask(@Validated @RequestBody FhFlightTaskCreateDTO createDTO) {
        return flightTaskService.createFlightTask(createDTO);
    }

    /**
     * 获取项目下的航线列表
     * 该方法调用大疆API获取项目下的航线列表
     *
     * @return 航线列表
     */
    @PostMapping("/wayline/list")
    @Operation(summary = "获取航线列表", description = "获取项目下的航线列表")
    public R<List<FhWaylineVO>> getWaylineList() {
        return R.data(flightTaskService.getWaylineList());
    }

    /**
     * 更新飞行任务状态
     * 该方法调用大疆API更新飞行任务的状态
     *
     * @param updateDTO 更新状态请求参数
     * @return 更新结果
     */
    @PostMapping("/status")
    @Operation(description = "更新指定飞行任务的状态")
    public R<String> updateFlightTaskStatus(@Validated @RequestBody FhFlightTaskStatusUpdateDTO updateDTO) {
        return flightTaskService.updateFlightTaskStatus(updateDTO);
    }

    /**
     * 获取飞行任务媒体资源列表
     * 调用大疆API获取指定飞行任务的媒体资源列表
     *
     * @param taskUuid 请求参数
     * @return 媒体资源分页列表
     */
    @GetMapping("/media/list/{taskUuid}")
    @Operation(summary = "获取飞行任务媒体资源列表", description = "获取指定飞行任务的媒体资源列表，支持分页查询")
    public R<List<FhMediaResourceVO>> getFlightTaskMediaList(@Validated @PathVariable String taskUuid) {
        return R.data(flightTaskService.getFlightTaskMediaList(taskUuid));
    }

    /**
     * 根据筛选条件获取飞行任务列表
     *
     * @param requestDTO 请求参数，包含时间范围、状态和任务类型
     * @return 飞行任务列表
     */
    @PostMapping("/listByGrid")
    @Operation(summary = "根据筛选条件获取飞行任务列表", description = "根据时间范围、状态和任务类型获取飞行任务列表")
    public R<List<FhFlightTaskVO>> getFlightTaskListByGrid(@Validated @RequestBody FhFlightTaskGridQueryDTO requestDTO) {
        // 在获取列表前先检查并更新飞行任务状态
//        flightTaskStatusScheduler.checkAndUpdateFlightTaskStatus();
        return R.data(flightTaskService.getFlightTaskListByGrid(requestDTO));
    }

	/**
	 * 根据ID获取飞行任务详情
	 * @param id 任务id
	 * @return
	 */
	@Deprecated
	@GetMapping("/task/{id}")
	@Operation(summary = "获取飞行任务详情", description = "根据ID获取飞行任务详情")
	public R<FhFlightTaskVO> getFlightTaskById(@PathVariable String id) {
		return R.data(flightTaskService.getFlightTaskById(id));
	}

	/**
     * 获取飞行任务轨迹信息
     * 该接口调用SK2获取指定飞行任务的轨迹信息
     *
     * @param taskUuid 任务UUID
     * @return 飞行任务轨迹信息
     */
    @GetMapping("/track/{taskUuid}")
    @Operation(summary = "获取飞行任务轨迹信息", description = "根据任务UUID获取飞行任务轨迹信息")
    public R<FhFlightTaskTrackVO> getFlightTaskTrack(@PathVariable String taskUuid) {
        return R.data(flightTaskService.getFlightTaskTrack(taskUuid));
    }

    /**
     * 手动触发飞行任务状态更新
     * 该接口会立即执行飞行任务状态检查和更新，无需等待定时任务
     *
     * @return 更新结果
     */
    @PostMapping("/refresh-status")
    @Operation(summary = "手动刷新飞行任务状态", description = "手动触发飞行任务状态检查和更新")
    public R<String> refreshFlightTaskStatus() {
        try {
            log.info("手动触发飞行任务状态更新开始");
            flightTaskStatusScheduler.checkAndUpdateFlightTaskStatus();
            log.info("手动触发飞行任务状态更新完成");
			flightTaskService.updateFlightTaskDistance();
			log.info("手动更新飞行任务距离完成");
            return R.success("飞行任务状态更新成功");
        } catch (Exception e) {
            log.error("手动触发飞行任务状态更新失败", e);
            return R.fail("飞行任务状态更新失败: " + e.getMessage());
        }
    }

    /**
     * 手动更新飞行任务距离
     * 该接口会立即查找所有状态为成功且飞行距离为空的任务，通过调用轨迹API获取飞行距离并更新到数据库
     *
     * @return 更新结果
     */
    @PostMapping("/update-distance")
    @Operation(summary = "手动更新飞行任务距离", description = "手动触发飞行任务距离更新，查找成功任务中缺失飞行距离的记录并更新")
    public R<String> updateFlightTaskDistance() {
        try {
            log.info("手动触发飞行任务距离更新开始");
            R<String> result = flightTaskService.updateFlightTaskDistance();
            log.info("手动触发飞行任务距离更新完成");
            return result;
        } catch (Exception e) {
            log.error("手动触发飞行任务距离更新失败", e);
            return R.fail("飞行任务距离更新失败: " + e.getMessage());
        }
    }

}
