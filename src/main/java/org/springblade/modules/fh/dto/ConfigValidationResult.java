package org.springblade.modules.fh.dto;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 配置验证结果DTO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigValidationResult {

    /**
     * 验证是否成功
     */
    private boolean valid;

    /**
     * 验证失败原因
     */
    private String failureReason;

    /**
     * 验证耗时（毫秒）
     */
    private long validationTimeMs;

    /**
     * 验证详情
     */
    private String details;

    /**
     * 创建验证成功结果
     */
    public static ConfigValidationResult success(long validationTimeMs, String details) {
        return ConfigValidationResult.builder()
                .valid(true)
                .validationTimeMs(validationTimeMs)
                .details(details)
                .build();
    }

    /**
     * 创建验证失败结果
     */
    public static ConfigValidationResult failure(String failureReason, long validationTimeMs, String details) {
        return ConfigValidationResult.builder()
                .valid(false)
                .failureReason(failureReason)
                .validationTimeMs(validationTimeMs)
                .details(details)
                .build();
    }
}