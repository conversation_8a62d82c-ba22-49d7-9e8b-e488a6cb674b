package org.springblade.modules.fh.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 应用配置请求DTO
 * 包含所有配置项的更新请求结构
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "应用配置请求")
public class ApplicationConfigRequest {

    /**
     * Skii MinIO配置
     */
    @Data
    @Schema(description = "Skii MinIO配置")
    public static class SkiiMinioConfig {
        @NotBlank(message = "MinIO服务端点不能为空")
        @Schema(description = "MinIO服务端点", example = "https://www.skii.ejdrone.com:20802")
        private String endpoint;

        @NotBlank(message = "MinIO访问密钥不能为空")
        @Schema(description = "MinIO访问密钥", example = "sZ6iY65sQcXfj6aDppsN")
        private String accessKey;

        @NotBlank(message = "MinIO秘密密钥不能为空")
        @Schema(description = "MinIO秘密密钥", example = "D0of1tjsvdk1i4hADKBVWQmKX9KxAGDwFJ996ZLS")
        private String secretKey;

        @NotBlank(message = "MinIO存储桶名称不能为空")
        @Schema(description = "MinIO存储桶名称", example = "file-storage-privatization")
        private String bucket;
    }

    /**
     * MQTT Basic配置
     */
    @Data
    @Schema(description = "MQTT Basic配置")
    public static class MqttBasicConfig {
        @NotBlank(message = "MQTT协议不能为空")
        @Schema(description = "MQTT协议", example = "MQTT")
        private String protocol;

        @NotBlank(message = "MQTT主机不能为空")
        @Schema(description = "MQTT主机", example = "ecs04.ejdrone.com")
        private String host;

        @Schema(description = "MQTT端口", example = "1883")
        private String port;

        @Schema(description = "MQTT用户名", example = "MLP-Server-User")
        private String username;

        @Schema(description = "MQTT密码", example = "EJdr0neServer")
        private String password;

        @Schema(description = "MQTT客户端ID", example = "mlp-server-0")
        private String clientId;

        @Schema(description = "MQTT路径", example = "")
        private String path;
    }

    /**
     * MQTT DRC配置
     */
    @Data
    @Schema(description = "MQTT DRC配置")
    public static class MqttDrcConfig {
        @NotBlank(message = "MQTT DRC协议不能为空")
        @Schema(description = "MQTT DRC协议", example = "WS")
        private String protocol;

        @NotBlank(message = "MQTT DRC主机不能为空")
        @Schema(description = "MQTT DRC主机", example = "ecs04.ejdrone.com")
        private String host;

        @Schema(description = "MQTT DRC端口", example = "8083")
        private String port;

        @Schema(description = "MQTT DRC路径", example = "/mqtt")
        private String path;

        @Schema(description = "MQTT DRC用户名", example = "MLP-Server-User")
        private String username;

        @Schema(description = "MQTT DRC密码", example = "EJdr0neServer")
        private String password;
    }

    /**
     * FH-SDK配置
     */
    @Data
    @Schema(description = "FH-SDK配置")
    public static class FhSdkConfig {
        @Schema(description = "FH-SDK MQTT入站主题", example = "sys/product/+/status")
        private String inboundTopic;
    }

    /**
     * EJ-FH-SERVER配置
     */
    @Data
    @Schema(description = "EJ-FH-SERVER配置")
    public static class EjFhServerConfig {
        @NotBlank(message = "EJ-FH-SERVER URL不能为空")
        @Schema(description = "EJ-FH-SERVER URL", example = "https://ims.ejdrone.com/fh-cloud")
        private String url;
    }

    /**
     * 标注文件夹ID配置
     */
    @Data
    @Schema(description = "标注文件夹ID配置")
    public static class AnnotationFolderConfig {
        @NotBlank(message = "标注文件夹ID不能为空")
        @Schema(description = "标注文件夹ID", example = "folder123")
        private String folderId;
    }
}