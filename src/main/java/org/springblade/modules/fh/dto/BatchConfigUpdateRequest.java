package org.springblade.modules.fh.dto;

import lombok.Data;
import jakarta.validation.Valid;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 批量配置更新请求DTO
 * 支持同时更新多种配置类型的统一接口
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "批量配置更新请求")
public class BatchConfigUpdateRequest {

    /**
     * Skii MinIO配置（可选）
     */
    @Valid
    @Schema(description = "Skii MinIO配置")
    private ApplicationConfigRequest.SkiiMinioConfig skiiMinio;

    /**
     * MQTT Basic配置（可选）
     */
    @Valid
    @Schema(description = "MQTT Basic配置")
    private ApplicationConfigRequest.MqttBasicConfig mqttBasic;

    /**
     * MQTT DRC配置（可选）
     */
    @Valid
    @Schema(description = "MQTT DRC配置")
    private ApplicationConfigRequest.MqttDrcConfig mqttDrc;

    /**
     * FH-SDK配置（可选）
     */
    @Valid
    @Schema(description = "FH-SDK配置")
    private ApplicationConfigRequest.FhSdkConfig fhSdk;

    /**
     * EJ-FH-SERVER配置（可选）
     */
    @Valid
    @Schema(description = "EJ-FH-SERVER配置")
    private ApplicationConfigRequest.EjFhServerConfig ejFhServer;

    /**
     * 标注文件夹ID配置（可选）
     */
    @Valid
    @Schema(description = "标注文件夹ID配置")
    private ApplicationConfigRequest.AnnotationFolderConfig annotationFolder;

    /**
     * 检查是否有任何配置需要更新
     * @return 是否有配置需要更新
     */
    public boolean hasAnyConfig() {
        return skiiMinio != null || mqttBasic != null || mqttDrc != null || 
               fhSdk != null || ejFhServer != null || annotationFolder != null;
    }

    /**
     * 获取需要更新的配置类型数量
     * @return 配置类型数量
     */
    public int getConfigCount() {
        int count = 0;
        if (skiiMinio != null) count++;
        if (mqttBasic != null) count++;
        if (mqttDrc != null) count++;
        if (fhSdk != null) count++;
        if (ejFhServer != null) count++;
        if (annotationFolder != null) count++;
        return count;
    }
}