package org.springblade.modules.fh.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 配置更新结果DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigUpdateResult {

    /**
     * 更新是否成功
     */
    private boolean success;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 验证结果
     */
    private ConfigValidationResult validationResult;

    /**
     * 更新详情（键值对）
     */
    private Map<String, String> updateDetails;

    /**
     * 回滚信息（如果需要回滚）
     */
    private List<String> rollbackConfigKeys;

    /**
     * 总耗时（毫秒）
     */
    private long totalTimeMs;

    /**
     * 创建更新成功结果
     */
    public static ConfigUpdateResult success(ConfigValidationResult validationResult,
                                           Map<String, String> updateDetails,
                                           long totalTimeMs) {
        return ConfigUpdateResult.builder()
                .success(true)
                .validationResult(validationResult)
                .updateDetails(updateDetails)
                .totalTimeMs(totalTimeMs)
                .build();
    }

    /**
     * 创建更新成功结果（简化版本）
     */
    public static ConfigUpdateResult success(Map<String, String> updateDetails, long totalTimeMs) {
        return ConfigUpdateResult.builder()
                .success(true)
                .updateDetails(updateDetails)
                .totalTimeMs(totalTimeMs)
                .build();
    }

    /**
     * 创建验证失败结果
     */
    public static ConfigUpdateResult validationFailure(ConfigValidationResult validationResult, long totalTimeMs) {
        return ConfigUpdateResult.builder()
                .success(false)
                .failureReason("配置验证失败: " + validationResult.getFailureReason())
                .validationResult(validationResult)
                .totalTimeMs(totalTimeMs)
                .build();
    }

    /**
     * 创建更新失败结果
     */
    public static ConfigUpdateResult updateFailure(String failureReason,
                                                 ConfigValidationResult validationResult,
                                                 List<String> rollbackConfigKeys,
                                                 long totalTimeMs) {
        return ConfigUpdateResult.builder()
                .success(false)
                .failureReason(failureReason)
                .validationResult(validationResult)
                .rollbackConfigKeys(rollbackConfigKeys)
                .totalTimeMs(totalTimeMs)
                .build();
    }

    /**
     * 创建更新失败结果（简化版本）
     */
    public static ConfigUpdateResult failure(String failureReason) {
        return ConfigUpdateResult.builder()
                .success(false)
                .failureReason(failureReason)
                .build();
    }

}
