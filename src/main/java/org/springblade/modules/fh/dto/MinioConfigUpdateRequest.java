package org.springblade.modules.fh.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * MinIO配置批量更新请求DTO
 * 用于接收前端传入的四个MinIO配置参数
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "MinIO配置批量更新请求")
public class MinioConfigUpdateRequest {

    /**
     * MinIO服务端点
     */
    @NotBlank(message = "MinIO服务端点不能为空")
    @Pattern(regexp = "^https?://.*", message = "MinIO服务端点格式不正确，必须以http://或https://开头")
    @Size(max = 255, message = "MinIO服务端点长度不能超过255个字符")
    @Schema(description = "MinIO服务端点", example = "http://localhost:9000", required = true)
    private String endpoint;

    /**
     * MinIO访问密钥
     */
    @NotBlank(message = "MinIO访问密钥不能为空")
    @Size(min = 3, max = 128, message = "MinIO访问密钥长度必须在3-128个字符之间")
    @Schema(description = "MinIO访问密钥", example = "minioadmin", required = true)
    private String accessKey;

    /**
     * MinIO秘密密钥
     */
    @NotBlank(message = "MinIO秘密密钥不能为空")
    @Size(min = 8, max = 128, message = "MinIO秘密密钥长度必须在8-128个字符之间")
    @Schema(description = "MinIO秘密密钥", example = "minioadmin", required = true)
    private String secretKey;

    /**
     * MinIO存储桶名称
     */
    @NotBlank(message = "MinIO存储桶名称不能为空")
    @Pattern(regexp = "^[a-z0-9][a-z0-9.-]*[a-z0-9]$", message = "存储桶名称格式不正确，只能包含小写字母、数字、点和连字符，且不能以点或连字符开头或结尾")
    @Size(min = 3, max = 63, message = "MinIO存储桶名称长度必须在3-63个字符之间")
    @Schema(description = "MinIO存储桶名称", example = "my-bucket", required = true)
    private String bucket;
}