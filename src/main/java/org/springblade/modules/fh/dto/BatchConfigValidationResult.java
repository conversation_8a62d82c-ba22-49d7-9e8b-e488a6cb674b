package org.springblade.modules.fh.dto;

import lombok.Data;
import lombok.Builder;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;

/**
 * 批量配置验证结果DTO
 * 包含所有配置的验证状态和详细信息
 * 
 * <AUTHOR>
 */
@Data
@Builder
@Schema(description = "批量配置验证结果")
public class BatchConfigValidationResult {

    /**
     * 整体验证是否成功
     */
    @Schema(description = "整体验证是否成功")
    private boolean success;

    /**
     * 总耗时（毫秒）
     */
    @Schema(description = "总耗时（毫秒）")
    private long totalTimeMs;

    /**
     * 验证通过的配置数量
     */
    @Schema(description = "验证通过的配置数量")
    private int successCount;

    /**
     * 验证失败的配置数量
     */
    @Schema(description = "验证失败的配置数量")
    private int failureCount;

    /**
     * 总配置数量
     */
    @Schema(description = "总配置数量")
    private int totalCount;

    /**
     * 各配置类型的验证结果
     */
    @Schema(description = "各配置类型的验证结果")
    @Builder.Default
    private Map<String, ConfigValidationResult> validationResults = new HashMap<>();

    /**
     * 验证失败原因汇总
     */
    @Schema(description = "验证失败原因汇总")
    @Builder.Default
    private List<String> failureReasons = new ArrayList<>();

    /**
     * 整体失败原因（当所有配置都失败时）
     */
    @Schema(description = "整体失败原因")
    private String overallFailureReason;

    /**
     * 添加配置验证结果
     * @param configType 配置类型
     * @param result 验证结果
     */
    public void addValidationResult(String configType, ConfigValidationResult result) {
        this.validationResults.put(configType, result);
        if (!result.isValid()) {
            this.failureReasons.add(configType + ": " + result.getFailureReason());
        }
    }

    /**
     * 计算统计信息
     */
    public void calculateStats() {
        this.totalCount = this.validationResults.size();
        this.successCount = (int) this.validationResults.values().stream()
                .filter(ConfigValidationResult::isValid)
                .count();
        this.failureCount = this.totalCount - this.successCount;
        this.success = this.failureCount == 0;
        
        if (this.failureCount == this.totalCount && this.totalCount > 0) {
            this.overallFailureReason = "所有配置验证都失败";
        }
    }

    /**
     * 创建验证成功结果
     * @param totalTimeMs 总耗时
     * @return 验证结果
     */
    public static BatchConfigValidationResult success(long totalTimeMs) {
        BatchConfigValidationResult result = BatchConfigValidationResult.builder()
                .success(true)
                .totalTimeMs(totalTimeMs)
                .build();
        result.calculateStats();
        return result;
    }

    /**
     * 创建验证失败结果
     * @param failureReason 失败原因
     * @param totalTimeMs 总耗时
     * @return 验证结果
     */
    public static BatchConfigValidationResult failure(String failureReason, long totalTimeMs) {
        return BatchConfigValidationResult.builder()
                .success(false)
                .overallFailureReason(failureReason)
                .totalTimeMs(totalTimeMs)
                .build();
    }
}