package org.springblade.modules.fh.dto;

import lombok.Data;
import lombok.Builder;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 应用配置响应DTO
 * 包含所有配置项的响应结构
 * 
 * <AUTHOR>
 */
@Data
@Builder
@Schema(description = "应用配置响应")
public class ApplicationConfigResponse {

    /**
     * Skii MinIO配置
     */
    @Schema(description = "Skii MinIO配置")
    private SkiiMinioConfig skiiMinio;

    /**
     * MQTT配置
     */
    @Schema(description = "MQTT配置")
    private MqttConfig mqtt;

    /**
     * FH-SDK配置
     */
    @Schema(description = "FH-SDK配置")
    private FhSdkConfig fhSdk;

    /**
     * EJ-FH-SERVER配置
     */
    @Schema(description = "EJ-FH-SERVER配置")
    private EjFhServerConfig ejFhServer;

    /**
     * 标注文件夹ID
     */
    @Schema(description = "标注文件夹ID")
    private String annotationFolderId;

    /**
     * Skii MinIO配置
     */
    @Data
    @Builder
    @Schema(description = "Skii MinIO配置")
    public static class SkiiMinioConfig {
        @Schema(description = "MinIO服务端点")
        private String endpoint;

        @Schema(description = "MinIO访问密钥（脱敏）")
        private String accessKey;

        @Schema(description = "MinIO秘密密钥（脱敏）")
        private String secretKey;

        @Schema(description = "MinIO存储桶名称")
        private String bucket;
    }

    /**
     * MQTT配置
     */
    @Data
    @Builder
    @Schema(description = "MQTT配置")
    public static class MqttConfig {
        @Schema(description = "MQTT Basic配置")
        private MqttBasicConfig basic;

        @Schema(description = "MQTT DRC配置")
        private MqttDrcConfig drc;

        @Data
        @Builder
        @Schema(description = "MQTT Basic配置")
        public static class MqttBasicConfig {
            @Schema(description = "MQTT协议")
            private String protocol;

            @Schema(description = "MQTT主机")
            private String host;

            @Schema(description = "MQTT端口")
            private String port;

            @Schema(description = "MQTT用户名")
            private String username;

            @Schema(description = "MQTT密码（脱敏）")
            private String password;

            @Schema(description = "MQTT客户端ID")
            private String clientId;

            @Schema(description = "MQTT路径")
            private String path;
        }

        @Data
        @Builder
        @Schema(description = "MQTT DRC配置")
        public static class MqttDrcConfig {
            @Schema(description = "MQTT DRC协议")
            private String protocol;

            @Schema(description = "MQTT DRC主机")
            private String host;

            @Schema(description = "MQTT DRC端口")
            private String port;

            @Schema(description = "MQTT DRC路径")
            private String path;

            @Schema(description = "MQTT DRC用户名")
            private String username;

            @Schema(description = "MQTT DRC密码（脱敏）")
            private String password;
        }
    }

    /**
     * FH-SDK配置
     */
    @Data
    @Builder
    @Schema(description = "FH-SDK配置")
    public static class FhSdkConfig {
        @Schema(description = "FH-SDK MQTT入站主题")
        private String inboundTopic;
    }

    /**
     * EJ-FH-SERVER配置
     */
    @Data
    @Builder
    @Schema(description = "EJ-FH-SERVER配置")
    public static class EjFhServerConfig {
        @Schema(description = "EJ-FH-SERVER URL")
        private String url;
    }
}