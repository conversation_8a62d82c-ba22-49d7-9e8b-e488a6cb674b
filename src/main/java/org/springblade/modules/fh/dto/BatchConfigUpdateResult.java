package org.springblade.modules.fh.dto;

import lombok.Data;
import lombok.Builder;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;

/**
 * 批量配置更新结果DTO
 * 包含所有配置的更新状态和详细信息
 * 
 * <AUTHOR>
 */
@Data
@Builder
@Schema(description = "批量配置更新结果")
public class BatchConfigUpdateResult {

    /**
     * 整体更新是否成功
     */
    @Schema(description = "整体更新是否成功")
    private boolean success;

    /**
     * 总耗时（毫秒）
     */
    @Schema(description = "总耗时（毫秒）")
    private long totalTimeMs;

    /**
     * 成功更新的配置数量
     */
    @Schema(description = "成功更新的配置数量")
    private int successCount;

    /**
     * 失败的配置数量
     */
    @Schema(description = "失败的配置数量")
    private int failureCount;

    /**
     * 总配置数量
     */
    @Schema(description = "总配置数量")
    private int totalCount;

    /**
     * 各配置类型的更新结果
     */
    @Schema(description = "各配置类型的更新结果")
    @Builder.Default
    private Map<String, ConfigUpdateResult> configResults = new HashMap<>();

    /**
     * 失败原因汇总
     */
    @Schema(description = "失败原因汇总")
    @Builder.Default
    private List<String> failureReasons = new ArrayList<>();

    /**
     * 整体失败原因（当所有配置都失败时）
     */
    @Schema(description = "整体失败原因")
    private String overallFailureReason;

    /**
     * 更新详情汇总
     */
    @Schema(description = "更新详情汇总")
    @Builder.Default
    private Map<String, Object> updateSummary = new HashMap<>();

    /**
     * 添加配置更新结果
     * @param configType 配置类型
     * @param result 更新结果
     */
    public void addConfigResult(String configType, ConfigUpdateResult result) {
        configResults.put(configType, result);
        if (!result.isSuccess()) {
            failureReasons.add(configType + ": " + result.getFailureReason());
        }
    }

    /**
     * 计算统计信息
     */
    public void calculateStatistics() {
        totalCount = configResults.size();
        successCount = (int) configResults.values().stream()
                .filter(ConfigUpdateResult::isSuccess)
                .count();
        failureCount = totalCount - successCount;
        success = failureCount == 0 && totalCount > 0;
        
        // 设置整体失败原因
        if (failureCount == totalCount && totalCount > 0) {
            overallFailureReason = "所有配置更新均失败: " + String.join("; ", failureReasons);
        } else if (failureCount > 0) {
            overallFailureReason = "部分配置更新失败: " + String.join("; ", failureReasons);
        }
    }

    /**
     * 创建成功结果
     * @param totalTimeMs 总耗时
     * @return 批量更新结果
     */
    public static BatchConfigUpdateResult success(long totalTimeMs) {
        return BatchConfigUpdateResult.builder()
                .success(true)
                .totalTimeMs(totalTimeMs)
                .build();
    }

    /**
     * 创建失败结果
     * @param reason 失败原因
     * @param totalTimeMs 总耗时
     * @return 批量更新结果
     */
    public static BatchConfigUpdateResult failure(String reason, long totalTimeMs) {
        return BatchConfigUpdateResult.builder()
                .success(false)
                .overallFailureReason(reason)
                .totalTimeMs(totalTimeMs)
                .build();
    }

    /**
     * 创建验证失败结果
     * @param reason 验证失败原因
     * @param totalTimeMs 总耗时
     * @return 批量更新结果
     */
    public static BatchConfigUpdateResult validationFailure(String reason, long totalTimeMs) {
        return BatchConfigUpdateResult.builder()
                .success(false)
                .overallFailureReason("配置验证失败: " + reason)
                .totalTimeMs(totalTimeMs)
                .build();
    }
}