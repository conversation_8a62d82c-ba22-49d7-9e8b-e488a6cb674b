package org.springblade.modules.system.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 简化用户数据导入模板
 *
 * <AUTHOR> AI
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class SimpleUserExcel implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ColumnWidth(15)
    @ExcelProperty("登录账号")
    private String account;

	@ColumnWidth(10)
    @ExcelProperty("所属角色")
    private String roleName;

    @ColumnWidth(20)
    @ExcelProperty("用户姓名")
    private String realName;

    @ColumnWidth(15)
    @ExcelProperty("手机号码")
    private String phone;

}
