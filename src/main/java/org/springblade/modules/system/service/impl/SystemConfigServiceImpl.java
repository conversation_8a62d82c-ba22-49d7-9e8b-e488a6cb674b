package org.springblade.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springblade.core.tool.api.R;
import org.springblade.modules.system.mapper.SystemConfigMapper;
import org.springblade.modules.system.pojo.entity.SystemConfig;
import org.springblade.modules.system.service.ISystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统全局配置服务实现类
 *
 * <AUTHOR>
 */
@Service
public class SystemConfigServiceImpl implements ISystemConfigService {

	/** 系统全局配置Mapper */
	@Autowired
	private SystemConfigMapper systemConfigMapper;

	/**
	 * 获取所有全局配置参数
	 * <p>
	 * 查询数据库中所有标记为全局的配置项，并以键值对形式返回。
	 * 若查询过程中发生异常，则抛出自定义异常并记录日志。
	 *
	 * @return 全局配置参数Map
	 */
	@Override
	public R<Map<String, String>> getGlobalConfigMap() {
		try {
			List<SystemConfig> configs = systemConfigMapper.selectList(
				new LambdaQueryWrapper<SystemConfig>().eq(SystemConfig::getGlobal, true)
			);
			Map<String, String> configMap = new HashMap<>(configs.size());
			for (SystemConfig config : configs) {
				configMap.put(config.getConfigKey(), config.getConfigValue());
			}
			return R.data(configMap);
		} catch (Exception e) {
			return R.fail("获取全局配置参数失败: " + e.getMessage());
		}
	}

	/**
	 * 保存全局配置参数
	 * <p>
	 * 根据传入的配置参数Map，更新或插入全局配置项。
	 * 若操作过程中发生异常，则回滚事务并抛出自定义异常。
	 *
	 * @param configMap 配置参数Map
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<Boolean> saveGlobalConfig(Map<String, String> configMap) {
		try {
			// 1. 校验所有字段不能为空且不能包含空格
			for (Map.Entry<String, String> entry : configMap.entrySet()) {
				String key = entry.getKey();
				String value = entry.getValue();
				if (value == null || value.trim().isEmpty()) {
					return R.fail("字段[" + key + "]不能为空");
				}
				if (value.contains(" ")) {
					return R.fail("字段[" + key + "]不能包含空格");
				}
			}

			// 2. 事件通知阈值 eventNotifyThreshold 必须为数字且不超过100
			if (configMap.containsKey("eventNotifyThreshold")) {
				String threshold = configMap.get("eventNotifyThreshold");
				try {
					int t = Integer.parseInt(threshold);
					if (t < 0 || t > 100) {
						return R.fail("事件通知阈值必须为0-100的数字");
					}
				} catch (NumberFormatException e) {
					return R.fail("事件通知阈值必须为数字");
				}
			}

			// 3. 系统标题 systemTitle 长度不超过18
			if (configMap.containsKey("systemTitle")) {
				String title = configMap.get("systemTitle");
				if (title.length() > 18) {
					return R.fail("系统标题不能超过18个字符");
				}
			}

			// 4. 地图中心点经纬度 mapCenterLng/mapCenterLat 必须为合法经纬度
			if (configMap.containsKey("mapCenterLng")) {
				try {
					double lng = Double.parseDouble(configMap.get("mapCenterLng"));
					if (lng < -180 || lng > 180) {
						return R.fail("地图中心点经度必须在-180到180之间");
					}
				} catch (NumberFormatException e) {
					return R.fail("地图中心点经度必须为数字");
				}
			}
			if (configMap.containsKey("mapCenterLat")) {
				try {
					double lat = Double.parseDouble(configMap.get("mapCenterLat"));
					if (lat < -90 || lat > 90) {
						return R.fail("地图中心点纬度必须在-90到90之间");
					}
				} catch (NumberFormatException e) {
					return R.fail("地图中心点纬度必须为数字");
				}
			}

			// 5. 地图缩放层级 mapZoom 必须为数字
			if (configMap.containsKey("mapZoom")) {
				try {
					Integer.parseInt(configMap.get("mapZoom"));
				} catch (NumberFormatException e) {
					return R.fail("地图缩放层级必须为数字");
				}
			}

			List<SystemConfig> configs = systemConfigMapper.selectList(
				new LambdaQueryWrapper<SystemConfig>().eq(SystemConfig::getGlobal, true)
			);
			Map<String, SystemConfig> dbConfigMap = new HashMap<>(configs.size());
			for (SystemConfig config : configs) {
				dbConfigMap.put(config.getConfigKey(), config);
			}
			for (Map.Entry<String, String> entry : configMap.entrySet()) {
				SystemConfig config = dbConfigMap.get(entry.getKey());
				if (config != null) {
					config.setConfigValue(entry.getValue());
					systemConfigMapper.updateById(config);
				} else {
					SystemConfig newConfig = new SystemConfig();
					newConfig.setConfigKey(entry.getKey());
					newConfig.setConfigValue(entry.getValue());
					newConfig.setGlobal(true);
					systemConfigMapper.insert(newConfig);
				}
			}
			return R.success("ok");
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return R.fail("保存全局配置参数失败: " + e.getMessage());
		}
	}

	@Override
	public String getTitle() {
		SystemConfig config = systemConfigMapper.selectOne(
			new LambdaQueryWrapper<SystemConfig>().eq(SystemConfig::getConfigKey, "systemTitle")
		);
		return config != null ? config.getConfigValue() : "海滩垃圾网格化巡检系统";
	}

}
