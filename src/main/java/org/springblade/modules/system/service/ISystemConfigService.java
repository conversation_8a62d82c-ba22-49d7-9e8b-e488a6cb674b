package org.springblade.modules.system.service;

import org.springblade.core.tool.api.R;
import java.util.Map;

/**
 * 系统全局配置服务接口
 *
 * <AUTHOR>
 */
public interface ISystemConfigService {
    /**
     * 获取所有全局配置参数
     *
     * @return 全局配置参数Map
     */
    R<Map<String, String>> getGlobalConfigMap();

    /**
     * 保存全局配置参数
     *
     * @param configMap 配置参数Map
     */
    R<Boolean> saveGlobalConfig(Map<String, String> configMap);

	/**
	 * @return system title
	 */
    String getTitle();

}
