
package org.springblade.modules.system.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;


@Data
@TableName("system_wechat_applet")
@EqualsAndHashCode(callSuper = true)
public class WechatApplet extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	private String clientId;

	private String clientSecret;

	private String appId;

	private String secret;

}
