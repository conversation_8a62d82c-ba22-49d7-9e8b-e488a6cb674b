package org.springblade.modules.system.pojo;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;
@Getter
@AllArgsConstructor
public enum FhParamEnum {
	DJI_FH2_URL("Dji_Fh2_Url","司空访问地址"),
    DJI_FH2_ORG_KEY("Dji_Fh2_X-Organization-Key","司空组织密钥"),
    DJI_FH2_ORG_UUID("Dji_Fh2_Org_Uuid","司空组织uuid"),
	DJI_FH2_PRJ_UUID("Dji_Fh2_Prj_Uuid","司空项目uuid")
	;


    private final String key;
    private final String label;

    public static Optional<FhParamEnum> find(String key){
        return Arrays.stream(values()).filter(item -> item.key.equals(key)).findAny();
    }

    public static String findLabel(String key){
        return find(key).map(FhParamEnum::getLabel).orElse("");
    }

}
