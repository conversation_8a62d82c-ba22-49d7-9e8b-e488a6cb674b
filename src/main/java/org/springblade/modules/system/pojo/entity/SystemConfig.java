package org.springblade.modules.system.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 系统全局配置实体类
 * <p>对应表：system_config</p>
 * 
 * <AUTHOR>
 */
@Data
@TableName("system_config")
@Schema(description = "系统全局配置")
public class SystemConfig implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 配置键名
     */
    @TableField("config_key")
    private String configKey;

    /**
     * 配置值
     */
    @TableField("config_value")
    private String configValue;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 是否全局，固定为true
     */
    private Boolean global;
}