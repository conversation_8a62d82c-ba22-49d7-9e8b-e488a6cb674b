<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.system.mapper.ParamMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="paramResultMap" type="org.springblade.modules.system.pojo.entity.Param">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="param_name" property="paramName"/>
        <result column="param_key" property="paramKey"/>
        <result column="param_value" property="paramValue"/>
        <result column="remark" property="remark"/>
    </resultMap>

</mapper>
