package org.springblade.modules.system.importer;

import lombok.RequiredArgsConstructor;
import org.springblade.core.excel.support.ExcelImporter;
import org.springblade.modules.system.excel.SimpleUserExcel;
import org.springblade.modules.system.service.IUserService;

import java.util.List;

/**
 * 简化用户数据导入类
 *
 * <AUTHOR> AI
 */
@RequiredArgsConstructor
public class SimpleUserImporter implements ExcelImporter<SimpleUserExcel> {

    private final IUserService service;
    private final Boolean isCovered;
    private final String tenantId;

    @Override
    public void save(List<SimpleUserExcel> data) {
        service.importSimpleUser(data, isCovered, tenantId);
    }

}
