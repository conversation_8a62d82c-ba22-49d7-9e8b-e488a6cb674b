package org.springblade.modules.system.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springblade.core.tool.api.R;
import org.springblade.modules.system.service.ISystemConfigService;
import org.springframework.web.bind.annotation.*;
import java.util.Map;

@RestController
@RequiredArgsConstructor
@RequestMapping("/config/global")
@Tag(name = "系统全局配置", description = "系统全局配置管理")
public class SystemConfigController {

    private final ISystemConfigService systemConfigService;

    @GetMapping("/info")
    @Operation(summary = "获取全局参数")
    public R<Map<String, String>> getGlobalConfig() {
        return systemConfigService.getGlobalConfigMap();
    }

    @PostMapping("/save")
    @Operation(summary = "保存全局参数")
    public R<Boolean> saveGlobalConfig(@RequestBody Map<String, String> configMap) {
        return systemConfigService.saveGlobalConfig(configMap);
    }

	/**
	 * 获取系统标题
	 */
	@GetMapping("/title")
	public R<String> getTitle() {
		return R.data(systemConfigService.getTitle());
	}
}
