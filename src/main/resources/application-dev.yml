#数据源配置
spring:
  data:
    redis:
      ##redis 单机环境配置
      host: ***************
      port: 6379
      password: Ej2017
      database: 1
      ssl:
        enabled: false
      ##redis 集群环境配置
      #cluster:
      #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
      #  commandTimeout: 5000
  datasource:
    # MySql
    #url: *********************************************************************************************************************************************************************************************************************************************
    #username: root
    #password: root
    # PostgreSQL
    url: **********************************************
    username: postgres
    password: root

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:1888

#blade配置
blade:
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: false
    ##redis服务地址
    address: redis://127.0.0.1:6379
  #本地文件上传
  file:
    remote-mode: true
    upload-domain: http://localhost:8999
    remote-path: /usr/share/nginx/html
server:
  port: 8501

mqtt:
  BASIC:
    protocol: MQTT
    host: ecs04.ejdrone.com
    port: 1883
    username: MLP-Server-User
    password: EJdr0neServer
    client-id: mlp-server-0
    path:
  DRC:
    protocol: WS
    host: ecs04.ejdrone.com
    port: 8083
    path: /mqtt
    username: MLP-Server-User
    password: EJdr0neServer

fh-sdk:
  mqtt:
    # 最初连接到mqtt时需要订阅的主题，多个主题按 ",".
    inbound-topic: sys/product/+/status

ej-fh-server:
  url: https://ims.ejdrone.com/fh-cloud

# 像素尺寸配置
media:
  pixel-size: 2

# AI检测服务配置
ai-detect:
  url: http://***************:9090/detect_batch
  callback-url: http://***************:8501/detect/callback/detect-batch

# MinIO配置 - 开发环境
minio:
  # MinIO 服务主机地址 - 开发环境使用具体IP
  host: devminio.ljxj.ejdrone.com
  # MinIO 服务端口
  port: 9000
  # SSL配置
  ssl:
    enabled: true
  # 访问密钥
  access-key: springboot-app-user
  # 秘密密钥
  secret-key: ljxjminioadmin
  # 存储桶名称
  bucket-name: processed-images
  # 连接超时时间（毫秒）
  connect-timeout: 10000
  # 读取超时时间（毫秒）
  read-timeout: 10000
