# 飞行任务相关配置
flight:
  task:
    # 查询时间范围配置
    query:
      time:
        extend:
          hours: 24  # 查询时间范围扩展小时数，默认24小时
    
    # 异常数据判断配置
    abnormal:
      min:
        hours: 48  # 异常数据判断的最小间隔小时数，默认48小时
    
    # 调度器配置
    scheduler:
      # 定时任务执行间隔（cron表达式）
      cron: "0 */10 * * * ?"  # 每10分钟执行一次
      
      # 批量处理配置
      batch:
        size: 100  # 批量处理的任务数量
        
      # 性能监控配置
      monitor:
        enabled: true  # 是否启用性能监控
        log:
          slow:
            threshold: 5000  # 慢查询阈值（毫秒）